'use client'

export default function TypingIndicator() {
  return (
    <div className="flex justify-start message-slide-in-left">
      <div className="flex items-start space-x-3 max-w-[80%]">
        {/* Avatar */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-red-600 to-yellow-500 flex items-center justify-center shadow-lg arc-reactor avatar-pulse">
          <span className="text-white text-sm font-bold">J</span>
        </div>

        {/* Enhanced Typing Animation */}
        <div className="bg-grok-gray border border-red-500/20 rounded-2xl px-4 py-3 shadow-lg glowing-border">
          <div className="flex items-center space-x-2">
            <span className="text-red-400 text-sm mr-2 font-mono tracking-wider">PROCESSING</span>
            <div className="flex space-x-1">
              <div className="w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg"></div>
              <div className="w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg"></div>
              <div className="w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg"></div>
            </div>
            <div className="ml-2 text-yellow-400 text-xs font-mono">
              ▶
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
