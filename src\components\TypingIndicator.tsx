'use client'

export default function TypingIndicator() {
  return (
    <div className="flex justify-start message-fade-in">
      <div className="flex items-start space-x-3 max-w-[80%]">
        {/* Avatar */}
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-cyan-400 flex items-center justify-center">
          <span className="text-white text-sm font-bold">J</span>
        </div>

        {/* Typing Animation */}
        <div className="bg-grok-gray border border-grok-muted/20 rounded-2xl px-4 py-3">
          <div className="flex items-center space-x-1">
            <span className="text-grok-muted text-sm mr-2">Processing</span>
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full typing-dot"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full typing-dot"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full typing-dot"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
