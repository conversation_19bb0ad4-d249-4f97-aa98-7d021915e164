'use client'

import { useState } from 'react'
import { UserProfile } from '@/lib/memory-system'

interface MemoryStats {
  interests: number
  conversationDays: number
  communicationStyle: string
  personalityMode: string
  lastActive: Date
}

interface MemoryPanelProps {
  userProfile: UserProfile | null
  memoryStats: MemoryStats | null
  onUpdateProfile: (updates: Partial<UserProfile>) => void
  onClose: () => void
}

export default function MemoryPanel({ userProfile, memoryStats, onUpdateProfile, onClose }: MemoryPanelProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    name: userProfile?.name || '',
    occupation: userProfile?.personalInfo.occupation || '',
    communicationStyle: userProfile?.preferences.communicationStyle || 'friendly',
    personalityMode: userProfile?.preferences.personalityMode || 'professional'
  })

  const handleSave = () => {
    onUpdateProfile({
      name: editForm.name,
      personalInfo: {
        ...userProfile?.personalInfo,
        occupation: editForm.occupation
      },
      preferences: {
        ...userProfile?.preferences,
        communicationStyle: editForm.communicationStyle as any,
        personalityMode: editForm.personalityMode as any
      }
    })
    setIsEditing(false)
  }

  return (
    <div className="w-80 bg-grok-gray border-l border-red-500/20 shadow-lg overflow-y-auto">
      {/* Header */}
      <div className="p-4 border-b border-red-500/20 bg-gradient-to-r from-red-900/20 to-yellow-900/20">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-bold text-red-400 font-mono tracking-wider">
            MEMORY CORE
          </h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-red-500/20 rounded transition-colors"
          >
            <svg className="w-5 h-5 text-grok-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <p className="text-xs text-grok-muted font-mono mt-1">
          ADVANCED AI LEARNING SYSTEM
        </p>
      </div>

      {/* Memory Stats */}
      <div className="p-4 space-y-4">
        <div className="bg-grok-dark/50 rounded-lg p-3 border border-red-500/20">
          <h3 className="text-sm font-bold text-yellow-400 font-mono mb-2">SYSTEM STATUS</h3>
          <div className="space-y-2 text-xs font-mono">
            <div className="flex justify-between">
              <span className="text-grok-muted">INTERESTS LEARNED:</span>
              <span className="text-red-400">{memoryStats?.interests || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-grok-muted">ACTIVE DAYS:</span>
              <span className="text-red-400">{memoryStats?.conversationDays || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-grok-muted">COMM STYLE:</span>
              <span className="text-red-400">{memoryStats?.communicationStyle?.toUpperCase()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-grok-muted">PERSONALITY:</span>
              <span className="text-red-400">{memoryStats?.personalityMode?.toUpperCase()}</span>
            </div>
          </div>
        </div>

        {/* User Profile */}
        <div className="bg-grok-dark/50 rounded-lg p-3 border border-red-500/20">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-sm font-bold text-yellow-400 font-mono">USER PROFILE</h3>
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="text-xs text-red-400 hover:text-red-300 font-mono"
            >
              {isEditing ? 'CANCEL' : 'EDIT'}
            </button>
          </div>

          {isEditing ? (
            <div className="space-y-3">
              <div>
                <label className="block text-xs text-grok-muted font-mono mb-1">NAME:</label>
                <input
                  type="text"
                  value={editForm.name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono"
                  placeholder="Enter your name"
                />
              </div>
              
              <div>
                <label className="block text-xs text-grok-muted font-mono mb-1">OCCUPATION:</label>
                <input
                  type="text"
                  value={editForm.occupation}
                  onChange={(e) => setEditForm(prev => ({ ...prev, occupation: e.target.value }))}
                  className="w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono"
                  placeholder="Your occupation"
                />
              </div>

              <div>
                <label className="block text-xs text-grok-muted font-mono mb-1">COMMUNICATION:</label>
                <select
                  value={editForm.communicationStyle}
                  onChange={(e) => setEditForm(prev => ({ ...prev, communicationStyle: e.target.value }))}
                  className="w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono"
                >
                  <option value="formal">FORMAL</option>
                  <option value="casual">CASUAL</option>
                  <option value="friendly">FRIENDLY</option>
                  <option value="technical">TECHNICAL</option>
                </select>
              </div>

              <div>
                <label className="block text-xs text-grok-muted font-mono mb-1">PERSONALITY:</label>
                <select
                  value={editForm.personalityMode}
                  onChange={(e) => setEditForm(prev => ({ ...prev, personalityMode: e.target.value }))}
                  className="w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono"
                >
                  <option value="professional">PROFESSIONAL</option>
                  <option value="witty">WITTY</option>
                  <option value="helpful">HELPFUL</option>
                  <option value="sarcastic">SARCASTIC</option>
                </select>
              </div>

              <button
                onClick={handleSave}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded text-xs font-mono transition-colors"
              >
                SAVE CHANGES
              </button>
            </div>
          ) : (
            <div className="space-y-2 text-xs font-mono">
              <div className="flex justify-between">
                <span className="text-grok-muted">NAME:</span>
                <span className="text-red-400">{userProfile?.name || 'NOT SET'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-grok-muted">OCCUPATION:</span>
                <span className="text-red-400">{userProfile?.personalInfo.occupation || 'NOT SET'}</span>
              </div>
            </div>
          )}
        </div>

        {/* Interests */}
        <div className="bg-grok-dark/50 rounded-lg p-3 border border-red-500/20">
          <h3 className="text-sm font-bold text-yellow-400 font-mono mb-2">LEARNED INTERESTS</h3>
          <div className="flex flex-wrap gap-1">
            {userProfile?.personalInfo.interests.map((interest, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs font-mono"
              >
                {interest.toUpperCase()}
              </span>
            )) || <span className="text-grok-muted text-xs font-mono">NO INTERESTS LEARNED YET</span>}
          </div>
        </div>

        {/* Memory Actions */}
        <div className="bg-grok-dark/50 rounded-lg p-3 border border-red-500/20">
          <h3 className="text-sm font-bold text-yellow-400 font-mono mb-2">MEMORY ACTIONS</h3>
          <div className="space-y-2">
            <button className="w-full bg-yellow-600/20 hover:bg-yellow-600/30 text-yellow-400 py-2 rounded text-xs font-mono transition-colors">
              EXPORT MEMORY DATA
            </button>
            <button className="w-full bg-red-600/20 hover:bg-red-600/30 text-red-400 py-2 rounded text-xs font-mono transition-colors">
              CLEAR ALL MEMORY
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
