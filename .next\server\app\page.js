/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vd2VlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2NoYXRib3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQStHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamFydmlzLWFpLWFzc2lzdGFudC8/NGY2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vd2VlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(ssr)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* harmony import */ var _components_MemoryPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/MemoryPanel */ \"(ssr)/./src/components/MemoryPanel.tsx\");\n/* harmony import */ var _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useJarvisMemory */ \"(ssr)/./src/hooks/useJarvisMemory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Home() {\n    const { userProfile, memoryContext, isInitialized, addMessage, updateUserInfo, getPersonalizedPrompt, getMemoryStats } = (0,_hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_5__.useJarvisMemory)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemoryPanel, setShowMemoryPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize welcome message based on memory\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && messages.length === 0) {\n            const welcomeMessage = {\n                id: \"1\",\n                content: userProfile?.name ? `Welcome back, ${userProfile.name}! I'm Jarvis, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?` : \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n            addMessage(welcomeMessage);\n        }\n    }, [\n        isInitialized,\n        userProfile\n    ]);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        addMessage(userMessage) // Add to memory system\n        ;\n        setIsLoading(true);\n        try {\n            // Include memory context in the request\n            const personalizedPrompt = getPersonalizedPrompt();\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages,\n                    memoryContext: personalizedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date(),\n                metadata: data.metadata\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            addMessage(assistantMessage) // Add to memory system\n            ;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onMemoryClick: ()=>setShowMemoryPanel(!showMemoryPanel)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            messages: messages,\n                            onSendMessage: handleSendMessage,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    showMemoryPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MemoryPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        userProfile: userProfile,\n                        memoryStats: getMemoryStats(),\n                        onUpdateProfile: updateUserInfo,\n                        onClose: ()=>setShowMemoryPanel(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./src/components/MessageBubble.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageInput */ \"(ssr)/./src/components/MessageInput.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatInterface({ messages, onSendMessage, isLoading }) {\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isLoading\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const message = inputValue.trim();\n        setInputValue(\"\");\n        await onSendMessage(message);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full tech-grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto px-4 py-6 space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-red-500/20 bg-grok-gray/50 backdrop-blur-sm shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        value: inputValue,\n                        onChange: setInputValue,\n                        onSend: handleSend,\n                        onKeyPress: handleKeyPress,\n                        disabled: isLoading,\n                        placeholder: \"How may I assist you today? I'm at your service...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Header({ onMemoryClick } = {}) {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scan-line\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-grok-gray border-b border-grok-muted/20 px-4 py-3 tech-grid relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"J\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-grok-text font-semibold text-xl bg-gradient-to-r from-red-400 to-yellow-400 bg-clip-text text-transparent\",\n                                                children: \"JARVIS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-grok-muted text-xs font-mono tracking-wider\",\n                                                children: \"ADVANCED AI ASSISTANT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-2 text-grok-muted text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full holographic-glow\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono tracking-wider\",\n                                                children: \"ONLINE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, this),\n                                    onMemoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onMemoryClick,\n                                        className: \"p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30\",\n                                        title: \"Memory System\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-grok-text\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: \"p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-grok-text\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 6h16M4 12h16M4 18h16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 right-4 bg-grok-gray border border-red-500/30 rounded-lg shadow-lg p-2 z-50 glowing-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"NEW CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"SETTINGS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"ABOUT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MemoryPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/MemoryPanel.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MemoryPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MemoryPanel({ userProfile, memoryStats, onUpdateProfile, onClose }) {\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: userProfile?.name || \"\",\n        occupation: userProfile?.personalInfo.occupation || \"\",\n        communicationStyle: userProfile?.preferences.communicationStyle || \"friendly\",\n        personalityMode: userProfile?.preferences.personalityMode || \"professional\"\n    });\n    const handleSave = ()=>{\n        onUpdateProfile({\n            name: editForm.name,\n            personalInfo: {\n                ...userProfile?.personalInfo,\n                occupation: editForm.occupation\n            },\n            preferences: {\n                ...userProfile?.preferences,\n                communicationStyle: editForm.communicationStyle,\n                personalityMode: editForm.personalityMode\n            }\n        });\n        setIsEditing(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-grok-gray border-l border-red-500/20 shadow-lg overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-red-500/20 bg-gradient-to-r from-red-900/20 to-yellow-900/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold text-red-400 font-mono tracking-wider\",\n                                children: \"MEMORY CORE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 hover:bg-red-500/20 rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-grok-text\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-grok-muted font-mono mt-1\",\n                        children: \"ADVANCED AI LEARNING SYSTEM\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"SYSTEM STATUS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-xs font-mono\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"INTERESTS LEARNED:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats?.interests || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"ACTIVE DAYS:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats?.conversationDays || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"COMM STYLE:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats?.communicationStyle?.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"PERSONALITY:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats?.personalityMode?.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-bold text-yellow-400 font-mono\",\n                                        children: \"USER PROFILE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsEditing(!isEditing),\n                                        className: \"text-xs text-red-400 hover:text-red-300 font-mono\",\n                                        children: isEditing ? \"CANCEL\" : \"EDIT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"NAME:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: editForm.name,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                placeholder: \"Enter your name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"OCCUPATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: editForm.occupation,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            occupation: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                placeholder: \"Your occupation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"COMMUNICATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: editForm.communicationStyle,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            communicationStyle: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"formal\",\n                                                        children: \"FORMAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"casual\",\n                                                        children: \"CASUAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"friendly\",\n                                                        children: \"FRIENDLY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"technical\",\n                                                        children: \"TECHNICAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"PERSONALITY:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: editForm.personalityMode,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            personalityMode: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"professional\",\n                                                        children: \"PROFESSIONAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"witty\",\n                                                        children: \"WITTY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"helpful\",\n                                                        children: \"HELPFUL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sarcastic\",\n                                                        children: \"SARCASTIC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"SAVE CHANGES\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-xs font-mono\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"NAME:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: userProfile?.name || \"NOT SET\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"OCCUPATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: userProfile?.personalInfo.occupation || \"NOT SET\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"LEARNED INTERESTS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: userProfile?.personalInfo.interests.map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs font-mono\",\n                                        children: interest.toUpperCase()\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-grok-muted text-xs font-mono\",\n                                    children: \"NO INTERESTS LEARNED YET\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"MEMORY ACTIONS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-yellow-600/20 hover:bg-yellow-600/30 text-yellow-400 py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"EXPORT MEMORY DATA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-red-600/20 hover:bg-red-600/30 text-red-400 py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"CLEAR ALL MEMORY\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MemoryPanel.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageBubble.tsx":
/*!******************************************!*\
  !*** ./src/components/MessageBubble.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction MessageBubble({ message }) {\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex ${isUser ? \"justify-end\" : \"justify-start\"} ${isUser ? \"message-slide-in-right\" : \"message-slide-in-left\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex max-w-[80%] ${isUser ? \"flex-row-reverse\" : \"flex-row\"} items-start space-x-3`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${isUser ? \"bg-red-600 ml-3 holographic-glow\" : \"bg-gradient-to-r from-red-600 to-yellow-500 mr-3 arc-reactor avatar-pulse\"}`,\n                    children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-medium\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex flex-col ${isUser ? \"items-end\" : \"items-start\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `rounded-2xl px-4 py-3 shadow-lg transition-all duration-300 ${isUser ? \"bg-gradient-to-r from-red-600 to-red-700 text-white border border-red-500/30 holographic-glow\" : \"bg-grok-gray border border-red-500/20 text-grok-text glowing-border hover:border-red-500/40\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"whitespace-pre-wrap break-words\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                isAssistant && message.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 pt-2 border-t border-red-500/20 text-xs text-grok-muted font-mono\",\n                                    children: [\n                                        message.metadata.sources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-red-400\",\n                                            children: [\n                                                \"SOURCES: \",\n                                                message.metadata.sources.join(\", \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        message.metadata.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-yellow-400\",\n                                            children: [\n                                                \"CONFIDENCE: \",\n                                                Math.round(message.metadata.confidence * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-xs text-grok-muted mt-1 font-mono tracking-wider ${isUser ? \"text-right\" : \"text-left\"}`,\n                            children: formatTimeAgo(message.timestamp).toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for date formatting\nfunction formatTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageInput.tsx":
/*!*****************************************!*\
  !*** ./src/components/MessageInput.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MessageInput({ value, onChange, onSend, onKeyPress, disabled, placeholder = \"Type your message...\" }) {\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative flex items-end space-x-3 p-4 rounded-2xl border transition-all duration-300 shadow-lg ${isFocused ? \"border-red-500 bg-grok-dark/50 glowing-border\" : \"border-red-500/20 bg-grok-gray/30 hover:border-red-500/40\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: value,\n                    onChange: (e)=>onChange(e.target.value),\n                    onKeyPress: onKeyPress,\n                    onFocus: ()=>setIsFocused(true),\n                    onBlur: ()=>setIsFocused(false),\n                    disabled: disabled,\n                    placeholder: placeholder,\n                    rows: 1,\n                    className: \"w-full bg-transparent text-grok-text placeholder-red-400/60 resize-none outline-none min-h-[24px] max-h-32 overflow-y-auto font-mono\",\n                    style: {\n                        height: \"auto\",\n                        minHeight: \"24px\"\n                    },\n                    onInput: (e)=>{\n                        const target = e.target;\n                        target.style.height = \"auto\";\n                        target.style.height = `${Math.min(target.scrollHeight, 128)}px`;\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onSend,\n                disabled: disabled || !value.trim(),\n                className: `flex-shrink-0 p-3 rounded-xl transition-all duration-300 shadow-lg ${disabled || !value.trim() ? \"bg-grok-muted/20 text-grok-muted cursor-not-allowed\" : \"bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 active:scale-95 button-glow border border-red-500/30\"}`,\n                children: disabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 border-2 border-red-400 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            value.length > 500 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-6 right-0 text-xs text-grok-muted\",\n                children: [\n                    value.length,\n                    \"/1000\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./src/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TypingIndicator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start message-slide-in-left\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[80%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-red-600 to-yellow-500 flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-grok-gray border border-red-500/20 rounded-2xl px-4 py-3 shadow-lg glowing-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-400 text-sm mr-2 font-mono tracking-wider\",\n                                children: \"PROCESSING\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-2 text-yellow-400 text-xs font-mono\",\n                                children: \"▶\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useJarvisMemory.ts":
/*!**************************************!*\
  !*** ./src/hooks/useJarvisMemory.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useJarvisMemory: () => (/* binding */ useJarvisMemory)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/memory-system */ \"(ssr)/./src/lib/memory-system.ts\");\n/* __next_internal_client_entry_do_not_use__ useJarvisMemory auto */ \n\nfunction useJarvisMemory() {\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [memoryContext, setMemoryContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Initialize memory system\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initializeMemory = async ()=>{\n            try {\n                const profile = await _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser();\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n                console.log(\"Jarvis memory system initialized\");\n            } catch (error) {\n                console.error(\"Failed to initialize memory system:\", error);\n                setIsInitialized(true) // Still mark as initialized to prevent infinite loading\n                ;\n            }\n        };\n        initializeMemory();\n    }, []);\n    // Add message to memory\n    const addMessage = (message)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.addMessage(message);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n        // Update profile state if it changed\n        const updatedProfile = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext();\n        if (updatedProfile) {\n            // Trigger a re-render by updating the timestamp\n            setUserProfile((prev)=>prev ? {\n                    ...prev,\n                    lastActive: new Date()\n                } : null);\n        }\n    };\n    // Update user information\n    const updateUserInfo = (updates)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.updateUserInfo(updates);\n        setUserProfile((prev)=>prev ? {\n                ...prev,\n                ...updates\n            } : null);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n    };\n    // Get personalized system prompt\n    const getPersonalizedPrompt = ()=>{\n        if (!memoryContext) return \"\";\n        const conversationSummary = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getConversationSummary();\n        return `\n${conversationSummary}\n\nPERSONALIZATION INSTRUCTIONS:\n- Adapt your communication style to match the user's preference: ${memoryContext.userPreferences.communicationStyle}\n- Provide ${memoryContext.userPreferences.responseLength} responses\n- Reference their interests when relevant: ${memoryContext.recentTopics.join(\", \")}\n- Maintain the ${memoryContext.userPreferences.personalityMode} personality mode\n- Use context from previous conversations when appropriate\n- Be more personal and contextually aware in your responses\n    `.trim();\n    };\n    // Clear all memory\n    const clearMemory = ()=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.clearMemory();\n        setUserProfile(null);\n        setMemoryContext(null);\n        setIsInitialized(false);\n        // Reinitialize\n        setTimeout(()=>{\n            _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser().then((profile)=>{\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n            });\n        }, 100);\n    };\n    // Get memory stats for display\n    const getMemoryStats = ()=>{\n        if (!userProfile || !memoryContext) return null;\n        return {\n            interests: userProfile.personalInfo.interests.length,\n            conversationDays: Math.floor((new Date().getTime() - new Date(userProfile.createdAt).getTime()) / (1000 * 60 * 60 * 24)),\n            communicationStyle: userProfile.preferences.communicationStyle,\n            personalityMode: userProfile.preferences.personalityMode,\n            lastActive: userProfile.lastActive\n        };\n    };\n    return {\n        userProfile,\n        memoryContext,\n        isInitialized,\n        addMessage,\n        updateUserInfo,\n        getPersonalizedPrompt,\n        clearMemory,\n        getMemoryStats\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useJarvisMemory.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/memory-system.ts":
/*!**********************************!*\
  !*** ./src/lib/memory-system.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   jarvisMemory: () => (/* binding */ jarvisMemory)\n/* harmony export */ });\n// Jarvis Memory System - Advanced AI Memory and Learning\nclass JarvisMemorySystem {\n    // Initialize or load user profile\n    async initializeUser(userId = \"default\") {\n        // Try to load existing profile from localStorage\n        const stored = localStorage.getItem(`jarvis_profile_${userId}`);\n        if (stored) {\n            this.userProfile = JSON.parse(stored);\n            console.log(\"Loaded existing user profile:\", this.userProfile?.name || \"Anonymous\");\n        } else {\n            // Create new profile\n            this.userProfile = {\n                id: userId,\n                preferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                personalInfo: {\n                    interests: []\n                },\n                conversationHistory: [],\n                learningData: [],\n                createdAt: new Date(),\n                lastActive: new Date()\n            };\n            this.saveProfile();\n            console.log(\"Created new user profile\");\n        }\n        return this.userProfile;\n    }\n    // Save profile to localStorage\n    saveProfile() {\n        if (this.userProfile) {\n            localStorage.setItem(`jarvis_profile_${this.userProfile.id}`, JSON.stringify(this.userProfile));\n        }\n    }\n    // Add message to conversation buffer\n    addMessage(message) {\n        this.conversationBuffer.push(message);\n        // Keep only last 20 messages in buffer\n        if (this.conversationBuffer.length > 20) {\n            this.conversationBuffer = this.conversationBuffer.slice(-20);\n        }\n        // Learn from user messages\n        if (message.role === \"user\") {\n            this.learnFromMessage(message.content);\n        }\n    }\n    // Extract learning patterns from user messages\n    learnFromMessage(content) {\n        if (!this.userProfile) return;\n        const words = content.toLowerCase().split(/\\s+/);\n        const topics = this.extractTopics(content);\n        const mood = this.detectMood(content);\n        // Update topics of interest\n        topics.forEach((topic)=>{\n            if (!this.userProfile.personalInfo.interests.includes(topic)) {\n                this.userProfile.personalInfo.interests.push(topic);\n            }\n        });\n        // Update communication style based on patterns\n        this.updateCommunicationStyle(content);\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Extract topics from message content\n    extractTopics(content) {\n        const topicKeywords = {\n            \"technology\": [\n                \"tech\",\n                \"computer\",\n                \"software\",\n                \"programming\",\n                \"ai\",\n                \"code\",\n                \"development\"\n            ],\n            \"science\": [\n                \"science\",\n                \"research\",\n                \"experiment\",\n                \"theory\",\n                \"physics\",\n                \"chemistry\",\n                \"biology\"\n            ],\n            \"business\": [\n                \"business\",\n                \"work\",\n                \"job\",\n                \"career\",\n                \"company\",\n                \"meeting\",\n                \"project\"\n            ],\n            \"entertainment\": [\n                \"movie\",\n                \"music\",\n                \"game\",\n                \"book\",\n                \"show\",\n                \"entertainment\",\n                \"fun\"\n            ],\n            \"health\": [\n                \"health\",\n                \"fitness\",\n                \"exercise\",\n                \"diet\",\n                \"medical\",\n                \"wellness\"\n            ],\n            \"travel\": [\n                \"travel\",\n                \"trip\",\n                \"vacation\",\n                \"country\",\n                \"city\",\n                \"flight\",\n                \"hotel\"\n            ],\n            \"food\": [\n                \"food\",\n                \"recipe\",\n                \"cooking\",\n                \"restaurant\",\n                \"meal\",\n                \"cuisine\"\n            ],\n            \"sports\": [\n                \"sport\",\n                \"football\",\n                \"basketball\",\n                \"soccer\",\n                \"tennis\",\n                \"game\",\n                \"team\"\n            ]\n        };\n        const topics = [];\n        const lowerContent = content.toLowerCase();\n        Object.entries(topicKeywords).forEach(([topic, keywords])=>{\n            if (keywords.some((keyword)=>lowerContent.includes(keyword))) {\n                topics.push(topic);\n            }\n        });\n        return topics;\n    }\n    // Detect user mood from message\n    detectMood(content) {\n        const positiveWords = [\n            \"great\",\n            \"awesome\",\n            \"excellent\",\n            \"love\",\n            \"amazing\",\n            \"perfect\",\n            \"wonderful\"\n        ];\n        const negativeWords = [\n            \"bad\",\n            \"terrible\",\n            \"awful\",\n            \"hate\",\n            \"frustrated\",\n            \"annoying\",\n            \"problem\"\n        ];\n        const excitedWords = [\n            \"excited\",\n            \"can't wait\",\n            \"!\",\n            \"wow\",\n            \"incredible\",\n            \"fantastic\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (excitedWords.some((word)=>lowerContent.includes(word))) return \"excited\";\n        if (positiveWords.some((word)=>lowerContent.includes(word))) return \"positive\";\n        if (negativeWords.some((word)=>lowerContent.includes(word))) return \"frustrated\";\n        return \"neutral\";\n    }\n    // Update communication style based on user patterns\n    updateCommunicationStyle(content) {\n        if (!this.userProfile) return;\n        const formalIndicators = [\n            \"please\",\n            \"thank you\",\n            \"could you\",\n            \"would you\"\n        ];\n        const casualIndicators = [\n            \"hey\",\n            \"what's up\",\n            \"cool\",\n            \"awesome\",\n            \"yeah\"\n        ];\n        const technicalIndicators = [\n            \"algorithm\",\n            \"function\",\n            \"variable\",\n            \"database\",\n            \"api\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (technicalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"technical\";\n        } else if (formalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"formal\";\n        } else if (casualIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"casual\";\n        }\n    }\n    // Get memory context for AI responses\n    getMemoryContext() {\n        if (!this.userProfile) {\n            return {\n                recentTopics: [],\n                userPreferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                relevantHistory: [],\n                personalContext: []\n            };\n        }\n        const recentTopics = this.userProfile.personalInfo.interests.slice(-5);\n        const relevantHistory = this.conversationBuffer.slice(-5).map((msg)=>`${msg.role}: ${msg.content.substring(0, 100)}`);\n        const personalContext = [\n            this.userProfile.name ? `User's name: ${this.userProfile.name}` : \"\",\n            this.userProfile.personalInfo.occupation ? `Occupation: ${this.userProfile.personalInfo.occupation}` : \"\",\n            `Interests: ${this.userProfile.personalInfo.interests.join(\", \")}`,\n            `Communication style: ${this.userProfile.preferences.communicationStyle}`,\n            `Preferred response length: ${this.userProfile.preferences.responseLength}`\n        ].filter(Boolean);\n        return {\n            recentTopics,\n            userPreferences: this.userProfile.preferences,\n            relevantHistory,\n            personalContext\n        };\n    }\n    // Update user information\n    updateUserInfo(updates) {\n        if (!this.userProfile) return;\n        this.userProfile = {\n            ...this.userProfile,\n            ...updates\n        };\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Get conversation summary\n    getConversationSummary() {\n        const context = this.getMemoryContext();\n        return `\nMEMORY CONTEXT:\n- User interests: ${context.recentTopics.join(\", \") || \"None recorded yet\"}\n- Communication style: ${context.userPreferences.communicationStyle}\n- Preferred response length: ${context.userPreferences.responseLength}\n- Personal context: ${context.personalContext.join(\"; \")}\n- Recent conversation: ${context.relevantHistory.slice(-3).join(\"; \")}\n\nUse this context to provide personalized, contextually aware responses.\n    `.trim();\n    }\n    // Clear memory (for privacy)\n    clearMemory() {\n        if (this.userProfile) {\n            localStorage.removeItem(`jarvis_profile_${this.userProfile.id}`);\n        }\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n    constructor(){\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n}\n// Export singleton instance\nconst jarvisMemory = new JarvisMemorySystem();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (jarvisMemory);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL21lbW9yeS1zeXN0ZW0udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSx5REFBeUQ7QUFrRHpELE1BQU1BO0lBSUosa0NBQWtDO0lBQ2xDLE1BQU1DLGVBQWVDLFNBQWlCLFNBQVMsRUFBd0I7UUFDckUsaURBQWlEO1FBQ2pELE1BQU1DLFNBQVNDLGFBQWFDLE9BQU8sQ0FBQyxDQUFDLGVBQWUsRUFBRUgsT0FBTyxDQUFDO1FBRTlELElBQUlDLFFBQVE7WUFDVixJQUFJLENBQUNHLFdBQVcsR0FBR0MsS0FBS0MsS0FBSyxDQUFDTDtZQUM5Qk0sUUFBUUMsR0FBRyxDQUFDLGlDQUFpQyxJQUFJLENBQUNKLFdBQVcsRUFBRUssUUFBUTtRQUN6RSxPQUFPO1lBQ0wscUJBQXFCO1lBQ3JCLElBQUksQ0FBQ0wsV0FBVyxHQUFHO2dCQUNqQk0sSUFBSVY7Z0JBQ0pXLGFBQWE7b0JBQ1hDLG9CQUFvQjtvQkFDcEJDLGdCQUFnQjtvQkFDaEJDLFFBQVEsRUFBRTtvQkFDVkMsaUJBQWlCO2dCQUNuQjtnQkFDQUMsY0FBYztvQkFDWkMsV0FBVyxFQUFFO2dCQUNmO2dCQUNBQyxxQkFBcUIsRUFBRTtnQkFDdkJDLGNBQWMsRUFBRTtnQkFDaEJDLFdBQVcsSUFBSUM7Z0JBQ2ZDLFlBQVksSUFBSUQ7WUFDbEI7WUFDQSxJQUFJLENBQUNFLFdBQVc7WUFDaEJoQixRQUFRQyxHQUFHLENBQUM7UUFDZDtRQUVBLE9BQU8sSUFBSSxDQUFDSixXQUFXO0lBQ3pCO0lBRUEsK0JBQStCO0lBQ3ZCbUIsY0FBb0I7UUFDMUIsSUFBSSxJQUFJLENBQUNuQixXQUFXLEVBQUU7WUFDcEJGLGFBQWFzQixPQUFPLENBQUMsQ0FBQyxlQUFlLEVBQUUsSUFBSSxDQUFDcEIsV0FBVyxDQUFDTSxFQUFFLENBQUMsQ0FBQyxFQUFFTCxLQUFLb0IsU0FBUyxDQUFDLElBQUksQ0FBQ3JCLFdBQVc7UUFDL0Y7SUFDRjtJQUVBLHFDQUFxQztJQUNyQ3NCLFdBQVdDLE9BQWdCLEVBQVE7UUFDakMsSUFBSSxDQUFDQyxrQkFBa0IsQ0FBQ0MsSUFBSSxDQUFDRjtRQUU3Qix1Q0FBdUM7UUFDdkMsSUFBSSxJQUFJLENBQUNDLGtCQUFrQixDQUFDRSxNQUFNLEdBQUcsSUFBSTtZQUN2QyxJQUFJLENBQUNGLGtCQUFrQixHQUFHLElBQUksQ0FBQ0Esa0JBQWtCLENBQUNHLEtBQUssQ0FBQyxDQUFDO1FBQzNEO1FBRUEsMkJBQTJCO1FBQzNCLElBQUlKLFFBQVFLLElBQUksS0FBSyxRQUFRO1lBQzNCLElBQUksQ0FBQ0MsZ0JBQWdCLENBQUNOLFFBQVFPLE9BQU87UUFDdkM7SUFDRjtJQUVBLCtDQUErQztJQUN2Q0QsaUJBQWlCQyxPQUFlLEVBQVE7UUFDOUMsSUFBSSxDQUFDLElBQUksQ0FBQzlCLFdBQVcsRUFBRTtRQUV2QixNQUFNK0IsUUFBUUQsUUFBUUUsV0FBVyxHQUFHQyxLQUFLLENBQUM7UUFDMUMsTUFBTXZCLFNBQVMsSUFBSSxDQUFDd0IsYUFBYSxDQUFDSjtRQUNsQyxNQUFNSyxPQUFPLElBQUksQ0FBQ0MsVUFBVSxDQUFDTjtRQUU3Qiw0QkFBNEI7UUFDNUJwQixPQUFPMkIsT0FBTyxDQUFDQyxDQUFBQTtZQUNiLElBQUksQ0FBQyxJQUFJLENBQUN0QyxXQUFXLENBQUVZLFlBQVksQ0FBQ0MsU0FBUyxDQUFDMEIsUUFBUSxDQUFDRCxRQUFRO2dCQUM3RCxJQUFJLENBQUN0QyxXQUFXLENBQUVZLFlBQVksQ0FBQ0MsU0FBUyxDQUFDWSxJQUFJLENBQUNhO1lBQ2hEO1FBQ0Y7UUFFQSwrQ0FBK0M7UUFDL0MsSUFBSSxDQUFDRSx3QkFBd0IsQ0FBQ1Y7UUFFOUIsSUFBSSxDQUFDOUIsV0FBVyxDQUFDa0IsVUFBVSxHQUFHLElBQUlEO1FBQ2xDLElBQUksQ0FBQ0UsV0FBVztJQUNsQjtJQUVBLHNDQUFzQztJQUM5QmUsY0FBY0osT0FBZSxFQUFZO1FBQy9DLE1BQU1XLGdCQUFnQjtZQUNwQixjQUFjO2dCQUFDO2dCQUFRO2dCQUFZO2dCQUFZO2dCQUFlO2dCQUFNO2dCQUFRO2FBQWM7WUFDMUYsV0FBVztnQkFBQztnQkFBVztnQkFBWTtnQkFBYztnQkFBVTtnQkFBVztnQkFBYTthQUFVO1lBQzdGLFlBQVk7Z0JBQUM7Z0JBQVk7Z0JBQVE7Z0JBQU87Z0JBQVU7Z0JBQVc7Z0JBQVc7YUFBVTtZQUNsRixpQkFBaUI7Z0JBQUM7Z0JBQVM7Z0JBQVM7Z0JBQVE7Z0JBQVE7Z0JBQVE7Z0JBQWlCO2FBQU07WUFDbkYsVUFBVTtnQkFBQztnQkFBVTtnQkFBVztnQkFBWTtnQkFBUTtnQkFBVzthQUFXO1lBQzFFLFVBQVU7Z0JBQUM7Z0JBQVU7Z0JBQVE7Z0JBQVk7Z0JBQVc7Z0JBQVE7Z0JBQVU7YUFBUTtZQUM5RSxRQUFRO2dCQUFDO2dCQUFRO2dCQUFVO2dCQUFXO2dCQUFjO2dCQUFRO2FBQVU7WUFDdEUsVUFBVTtnQkFBQztnQkFBUztnQkFBWTtnQkFBYztnQkFBVTtnQkFBVTtnQkFBUTthQUFPO1FBQ25GO1FBRUEsTUFBTS9CLFNBQW1CLEVBQUU7UUFDM0IsTUFBTWdDLGVBQWVaLFFBQVFFLFdBQVc7UUFFeENXLE9BQU9DLE9BQU8sQ0FBQ0gsZUFBZUosT0FBTyxDQUFDLENBQUMsQ0FBQ0MsT0FBT08sU0FBUztZQUN0RCxJQUFJQSxTQUFTQyxJQUFJLENBQUNDLENBQUFBLFVBQVdMLGFBQWFILFFBQVEsQ0FBQ1EsV0FBVztnQkFDNURyQyxPQUFPZSxJQUFJLENBQUNhO1lBQ2Q7UUFDRjtRQUVBLE9BQU81QjtJQUNUO0lBRUEsZ0NBQWdDO0lBQ3hCMEIsV0FBV04sT0FBZSxFQUFVO1FBQzFDLE1BQU1rQixnQkFBZ0I7WUFBQztZQUFTO1lBQVc7WUFBYTtZQUFRO1lBQVc7WUFBVztTQUFZO1FBQ2xHLE1BQU1DLGdCQUFnQjtZQUFDO1lBQU87WUFBWTtZQUFTO1lBQVE7WUFBYztZQUFZO1NBQVU7UUFDL0YsTUFBTUMsZUFBZTtZQUFDO1lBQVc7WUFBZTtZQUFLO1lBQU87WUFBYztTQUFZO1FBRXRGLE1BQU1SLGVBQWVaLFFBQVFFLFdBQVc7UUFFeEMsSUFBSWtCLGFBQWFKLElBQUksQ0FBQ0ssQ0FBQUEsT0FBUVQsYUFBYUgsUUFBUSxDQUFDWSxRQUFRLE9BQU87UUFDbkUsSUFBSUgsY0FBY0YsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVEsT0FBTztRQUNwRSxJQUFJRixjQUFjSCxJQUFJLENBQUNLLENBQUFBLE9BQVFULGFBQWFILFFBQVEsQ0FBQ1ksUUFBUSxPQUFPO1FBRXBFLE9BQU87SUFDVDtJQUVBLG9EQUFvRDtJQUM1Q1gseUJBQXlCVixPQUFlLEVBQVE7UUFDdEQsSUFBSSxDQUFDLElBQUksQ0FBQzlCLFdBQVcsRUFBRTtRQUV2QixNQUFNb0QsbUJBQW1CO1lBQUM7WUFBVTtZQUFhO1lBQWE7U0FBWTtRQUMxRSxNQUFNQyxtQkFBbUI7WUFBQztZQUFPO1lBQWM7WUFBUTtZQUFXO1NBQU87UUFDekUsTUFBTUMsc0JBQXNCO1lBQUM7WUFBYTtZQUFZO1lBQVk7WUFBWTtTQUFNO1FBRXBGLE1BQU1aLGVBQWVaLFFBQVFFLFdBQVc7UUFFeEMsSUFBSXNCLG9CQUFvQlIsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDakUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BELE9BQU8sSUFBSTRDLGlCQUFpQk4sSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDckUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BELE9BQU8sSUFBSTZDLGlCQUFpQlAsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDckUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BEO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFDdEMrQyxtQkFBa0M7UUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQ3ZELFdBQVcsRUFBRTtZQUNyQixPQUFPO2dCQUNMd0QsY0FBYyxFQUFFO2dCQUNoQkMsaUJBQWlCO29CQUNmakQsb0JBQW9CO29CQUNwQkMsZ0JBQWdCO29CQUNoQkMsUUFBUSxFQUFFO29CQUNWQyxpQkFBaUI7Z0JBQ25CO2dCQUNBK0MsaUJBQWlCLEVBQUU7Z0JBQ25CQyxpQkFBaUIsRUFBRTtZQUNyQjtRQUNGO1FBRUEsTUFBTUgsZUFBZSxJQUFJLENBQUN4RCxXQUFXLENBQUNZLFlBQVksQ0FBQ0MsU0FBUyxDQUFDYyxLQUFLLENBQUMsQ0FBQztRQUNwRSxNQUFNK0Isa0JBQWtCLElBQUksQ0FBQ2xDLGtCQUFrQixDQUM1Q0csS0FBSyxDQUFDLENBQUMsR0FDUGlDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBTyxDQUFDLEVBQUVBLElBQUlqQyxJQUFJLENBQUMsRUFBRSxFQUFFaUMsSUFBSS9CLE9BQU8sQ0FBQ2dDLFNBQVMsQ0FBQyxHQUFHLEtBQUssQ0FBQztRQUU3RCxNQUFNSCxrQkFBa0I7WUFDdEIsSUFBSSxDQUFDM0QsV0FBVyxDQUFDSyxJQUFJLEdBQUcsQ0FBQyxhQUFhLEVBQUUsSUFBSSxDQUFDTCxXQUFXLENBQUNLLElBQUksQ0FBQyxDQUFDLEdBQUc7WUFDbEUsSUFBSSxDQUFDTCxXQUFXLENBQUNZLFlBQVksQ0FBQ21ELFVBQVUsR0FBRyxDQUFDLFlBQVksRUFBRSxJQUFJLENBQUMvRCxXQUFXLENBQUNZLFlBQVksQ0FBQ21ELFVBQVUsQ0FBQyxDQUFDLEdBQUc7WUFDdkcsQ0FBQyxXQUFXLEVBQUUsSUFBSSxDQUFDL0QsV0FBVyxDQUFDWSxZQUFZLENBQUNDLFNBQVMsQ0FBQ21ELElBQUksQ0FBQyxNQUFNLENBQUM7WUFDbEUsQ0FBQyxxQkFBcUIsRUFBRSxJQUFJLENBQUNoRSxXQUFXLENBQUNPLFdBQVcsQ0FBQ0Msa0JBQWtCLENBQUMsQ0FBQztZQUN6RSxDQUFDLDJCQUEyQixFQUFFLElBQUksQ0FBQ1IsV0FBVyxDQUFDTyxXQUFXLENBQUNFLGNBQWMsQ0FBQyxDQUFDO1NBQzVFLENBQUN3RCxNQUFNLENBQUNDO1FBRVQsT0FBTztZQUNMVjtZQUNBQyxpQkFBaUIsSUFBSSxDQUFDekQsV0FBVyxDQUFDTyxXQUFXO1lBQzdDbUQ7WUFDQUM7UUFDRjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCUSxlQUFlQyxPQUE2QixFQUFRO1FBQ2xELElBQUksQ0FBQyxJQUFJLENBQUNwRSxXQUFXLEVBQUU7UUFFdkIsSUFBSSxDQUFDQSxXQUFXLEdBQUc7WUFBRSxHQUFHLElBQUksQ0FBQ0EsV0FBVztZQUFFLEdBQUdvRSxPQUFPO1FBQUM7UUFDckQsSUFBSSxDQUFDcEUsV0FBVyxDQUFDa0IsVUFBVSxHQUFHLElBQUlEO1FBQ2xDLElBQUksQ0FBQ0UsV0FBVztJQUNsQjtJQUVBLDJCQUEyQjtJQUMzQmtELHlCQUFpQztRQUMvQixNQUFNQyxVQUFVLElBQUksQ0FBQ2YsZ0JBQWdCO1FBRXJDLE9BQU8sQ0FBQzs7a0JBRU0sRUFBRWUsUUFBUWQsWUFBWSxDQUFDUSxJQUFJLENBQUMsU0FBUyxvQkFBb0I7dUJBQ3BELEVBQUVNLFFBQVFiLGVBQWUsQ0FBQ2pELGtCQUFrQixDQUFDOzZCQUN2QyxFQUFFOEQsUUFBUWIsZUFBZSxDQUFDaEQsY0FBYyxDQUFDO29CQUNsRCxFQUFFNkQsUUFBUVgsZUFBZSxDQUFDSyxJQUFJLENBQUMsTUFBTTt1QkFDbEMsRUFBRU0sUUFBUVosZUFBZSxDQUFDL0IsS0FBSyxDQUFDLENBQUMsR0FBR3FDLElBQUksQ0FBQyxNQUFNOzs7SUFHbEUsQ0FBQyxDQUFDTyxJQUFJO0lBQ1I7SUFFQSw2QkFBNkI7SUFDN0JDLGNBQW9CO1FBQ2xCLElBQUksSUFBSSxDQUFDeEUsV0FBVyxFQUFFO1lBQ3BCRixhQUFhMkUsVUFBVSxDQUFDLENBQUMsZUFBZSxFQUFFLElBQUksQ0FBQ3pFLFdBQVcsQ0FBQ00sRUFBRSxDQUFDLENBQUM7UUFDakU7UUFDQSxJQUFJLENBQUNOLFdBQVcsR0FBRztRQUNuQixJQUFJLENBQUN3QixrQkFBa0IsR0FBRyxFQUFFO0lBQzlCOzthQWhOUXhCLGNBQWtDO2FBQ2xDd0IscUJBQWdDLEVBQUU7O0FBZ041QztBQUVBLDRCQUE0QjtBQUNyQixNQUFNa0QsZUFBZSxJQUFJaEYscUJBQW9CO0FBQ3BELGlFQUFlZ0YsWUFBWUEsRUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2phcnZpcy1haS1hc3Npc3RhbnQvLi9zcmMvbGliL21lbW9yeS1zeXN0ZW0udHM/MGFlOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBKYXJ2aXMgTWVtb3J5IFN5c3RlbSAtIEFkdmFuY2VkIEFJIE1lbW9yeSBhbmQgTGVhcm5pbmdcbmltcG9ydCB7IE1lc3NhZ2UgfSBmcm9tICdAL3R5cGVzL2NoYXQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlclByb2ZpbGUge1xuICBpZDogc3RyaW5nXG4gIG5hbWU/OiBzdHJpbmdcbiAgcHJlZmVyZW5jZXM6IHtcbiAgICBjb21tdW5pY2F0aW9uU3R5bGU6ICdmb3JtYWwnIHwgJ2Nhc3VhbCcgfCAndGVjaG5pY2FsJyB8ICdmcmllbmRseSdcbiAgICByZXNwb25zZUxlbmd0aDogJ2JyaWVmJyB8ICdkZXRhaWxlZCcgfCAnY29tcHJlaGVuc2l2ZSdcbiAgICB0b3BpY3M6IHN0cmluZ1tdXG4gICAgcGVyc29uYWxpdHlNb2RlOiAncHJvZmVzc2lvbmFsJyB8ICd3aXR0eScgfCAnaGVscGZ1bCcgfCAnc2FyY2FzdGljJ1xuICB9XG4gIHBlcnNvbmFsSW5mbzoge1xuICAgIG9jY3VwYXRpb24/OiBzdHJpbmdcbiAgICBpbnRlcmVzdHM6IHN0cmluZ1tdXG4gICAgdGltZXpvbmU/OiBzdHJpbmdcbiAgICBsb2NhdGlvbj86IHN0cmluZ1xuICB9XG4gIGNvbnZlcnNhdGlvbkhpc3Rvcnk6IENvbnZlcnNhdGlvblN1bW1hcnlbXVxuICBsZWFybmluZ0RhdGE6IExlYXJuaW5nUGF0dGVybltdXG4gIGNyZWF0ZWRBdDogRGF0ZVxuICBsYXN0QWN0aXZlOiBEYXRlXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ29udmVyc2F0aW9uU3VtbWFyeSB7XG4gIGlkOiBzdHJpbmdcbiAgZGF0ZTogRGF0ZVxuICB0b3BpY3M6IHN0cmluZ1tdXG4gIGtleVBvaW50czogc3RyaW5nW11cbiAgdXNlck1vb2Q6ICdwb3NpdGl2ZScgfCAnbmV1dHJhbCcgfCAnZnJ1c3RyYXRlZCcgfCAnZXhjaXRlZCdcbiAgaW1wb3J0YW50RmFjdHM6IHN0cmluZ1tdXG4gIGZvbGxvd1VwTmVlZGVkOiBib29sZWFuXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTGVhcm5pbmdQYXR0ZXJuIHtcbiAgcGF0dGVybjogc3RyaW5nXG4gIGZyZXF1ZW5jeTogbnVtYmVyXG4gIGNvbnRleHQ6IHN0cmluZ1xuICBsYXN0U2VlbjogRGF0ZVxuICBpbXBvcnRhbmNlOiAnbG93JyB8ICdtZWRpdW0nIHwgJ2hpZ2gnXG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgTWVtb3J5Q29udGV4dCB7XG4gIHJlY2VudFRvcGljczogc3RyaW5nW11cbiAgdXNlclByZWZlcmVuY2VzOiBVc2VyUHJvZmlsZVsncHJlZmVyZW5jZXMnXVxuICByZWxldmFudEhpc3Rvcnk6IHN0cmluZ1tdXG4gIHBlcnNvbmFsQ29udGV4dDogc3RyaW5nW11cbiAgY3VycmVudE1vb2Q/OiBzdHJpbmdcbn1cblxuY2xhc3MgSmFydmlzTWVtb3J5U3lzdGVtIHtcbiAgcHJpdmF0ZSB1c2VyUHJvZmlsZTogVXNlclByb2ZpbGUgfCBudWxsID0gbnVsbFxuICBwcml2YXRlIGNvbnZlcnNhdGlvbkJ1ZmZlcjogTWVzc2FnZVtdID0gW11cblxuICAvLyBJbml0aWFsaXplIG9yIGxvYWQgdXNlciBwcm9maWxlXG4gIGFzeW5jIGluaXRpYWxpemVVc2VyKHVzZXJJZDogc3RyaW5nID0gJ2RlZmF1bHQnKTogUHJvbWlzZTxVc2VyUHJvZmlsZT4ge1xuICAgIC8vIFRyeSB0byBsb2FkIGV4aXN0aW5nIHByb2ZpbGUgZnJvbSBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCBzdG9yZWQgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShgamFydmlzX3Byb2ZpbGVfJHt1c2VySWR9YClcbiAgICBcbiAgICBpZiAoc3RvcmVkKSB7XG4gICAgICB0aGlzLnVzZXJQcm9maWxlID0gSlNPTi5wYXJzZShzdG9yZWQpXG4gICAgICBjb25zb2xlLmxvZygnTG9hZGVkIGV4aXN0aW5nIHVzZXIgcHJvZmlsZTonLCB0aGlzLnVzZXJQcm9maWxlPy5uYW1lIHx8ICdBbm9ueW1vdXMnKVxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBDcmVhdGUgbmV3IHByb2ZpbGVcbiAgICAgIHRoaXMudXNlclByb2ZpbGUgPSB7XG4gICAgICAgIGlkOiB1c2VySWQsXG4gICAgICAgIHByZWZlcmVuY2VzOiB7XG4gICAgICAgICAgY29tbXVuaWNhdGlvblN0eWxlOiAnZnJpZW5kbHknLFxuICAgICAgICAgIHJlc3BvbnNlTGVuZ3RoOiAnZGV0YWlsZWQnLFxuICAgICAgICAgIHRvcGljczogW10sXG4gICAgICAgICAgcGVyc29uYWxpdHlNb2RlOiAncHJvZmVzc2lvbmFsJ1xuICAgICAgICB9LFxuICAgICAgICBwZXJzb25hbEluZm86IHtcbiAgICAgICAgICBpbnRlcmVzdHM6IFtdLFxuICAgICAgICB9LFxuICAgICAgICBjb252ZXJzYXRpb25IaXN0b3J5OiBbXSxcbiAgICAgICAgbGVhcm5pbmdEYXRhOiBbXSxcbiAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLFxuICAgICAgICBsYXN0QWN0aXZlOiBuZXcgRGF0ZSgpXG4gICAgICB9XG4gICAgICB0aGlzLnNhdmVQcm9maWxlKClcbiAgICAgIGNvbnNvbGUubG9nKCdDcmVhdGVkIG5ldyB1c2VyIHByb2ZpbGUnKVxuICAgIH1cbiAgICBcbiAgICByZXR1cm4gdGhpcy51c2VyUHJvZmlsZVxuICB9XG5cbiAgLy8gU2F2ZSBwcm9maWxlIHRvIGxvY2FsU3RvcmFnZVxuICBwcml2YXRlIHNhdmVQcm9maWxlKCk6IHZvaWQge1xuICAgIGlmICh0aGlzLnVzZXJQcm9maWxlKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShgamFydmlzX3Byb2ZpbGVfJHt0aGlzLnVzZXJQcm9maWxlLmlkfWAsIEpTT04uc3RyaW5naWZ5KHRoaXMudXNlclByb2ZpbGUpKVxuICAgIH1cbiAgfVxuXG4gIC8vIEFkZCBtZXNzYWdlIHRvIGNvbnZlcnNhdGlvbiBidWZmZXJcbiAgYWRkTWVzc2FnZShtZXNzYWdlOiBNZXNzYWdlKTogdm9pZCB7XG4gICAgdGhpcy5jb252ZXJzYXRpb25CdWZmZXIucHVzaChtZXNzYWdlKVxuICAgIFxuICAgIC8vIEtlZXAgb25seSBsYXN0IDIwIG1lc3NhZ2VzIGluIGJ1ZmZlclxuICAgIGlmICh0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlci5sZW5ndGggPiAyMCkge1xuICAgICAgdGhpcy5jb252ZXJzYXRpb25CdWZmZXIgPSB0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlci5zbGljZSgtMjApXG4gICAgfVxuXG4gICAgLy8gTGVhcm4gZnJvbSB1c2VyIG1lc3NhZ2VzXG4gICAgaWYgKG1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInKSB7XG4gICAgICB0aGlzLmxlYXJuRnJvbU1lc3NhZ2UobWVzc2FnZS5jb250ZW50KVxuICAgIH1cbiAgfVxuXG4gIC8vIEV4dHJhY3QgbGVhcm5pbmcgcGF0dGVybnMgZnJvbSB1c2VyIG1lc3NhZ2VzXG4gIHByaXZhdGUgbGVhcm5Gcm9tTWVzc2FnZShjb250ZW50OiBzdHJpbmcpOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMudXNlclByb2ZpbGUpIHJldHVyblxuXG4gICAgY29uc3Qgd29yZHMgPSBjb250ZW50LnRvTG93ZXJDYXNlKCkuc3BsaXQoL1xccysvKVxuICAgIGNvbnN0IHRvcGljcyA9IHRoaXMuZXh0cmFjdFRvcGljcyhjb250ZW50KVxuICAgIGNvbnN0IG1vb2QgPSB0aGlzLmRldGVjdE1vb2QoY29udGVudClcblxuICAgIC8vIFVwZGF0ZSB0b3BpY3Mgb2YgaW50ZXJlc3RcbiAgICB0b3BpY3MuZm9yRWFjaCh0b3BpYyA9PiB7XG4gICAgICBpZiAoIXRoaXMudXNlclByb2ZpbGUhLnBlcnNvbmFsSW5mby5pbnRlcmVzdHMuaW5jbHVkZXModG9waWMpKSB7XG4gICAgICAgIHRoaXMudXNlclByb2ZpbGUhLnBlcnNvbmFsSW5mby5pbnRlcmVzdHMucHVzaCh0b3BpYylcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgLy8gVXBkYXRlIGNvbW11bmljYXRpb24gc3R5bGUgYmFzZWQgb24gcGF0dGVybnNcbiAgICB0aGlzLnVwZGF0ZUNvbW11bmljYXRpb25TdHlsZShjb250ZW50KVxuICAgIFxuICAgIHRoaXMudXNlclByb2ZpbGUubGFzdEFjdGl2ZSA9IG5ldyBEYXRlKClcbiAgICB0aGlzLnNhdmVQcm9maWxlKClcbiAgfVxuXG4gIC8vIEV4dHJhY3QgdG9waWNzIGZyb20gbWVzc2FnZSBjb250ZW50XG4gIHByaXZhdGUgZXh0cmFjdFRvcGljcyhjb250ZW50OiBzdHJpbmcpOiBzdHJpbmdbXSB7XG4gICAgY29uc3QgdG9waWNLZXl3b3JkcyA9IHtcbiAgICAgICd0ZWNobm9sb2d5JzogWyd0ZWNoJywgJ2NvbXB1dGVyJywgJ3NvZnR3YXJlJywgJ3Byb2dyYW1taW5nJywgJ2FpJywgJ2NvZGUnLCAnZGV2ZWxvcG1lbnQnXSxcbiAgICAgICdzY2llbmNlJzogWydzY2llbmNlJywgJ3Jlc2VhcmNoJywgJ2V4cGVyaW1lbnQnLCAndGhlb3J5JywgJ3BoeXNpY3MnLCAnY2hlbWlzdHJ5JywgJ2Jpb2xvZ3knXSxcbiAgICAgICdidXNpbmVzcyc6IFsnYnVzaW5lc3MnLCAnd29yaycsICdqb2InLCAnY2FyZWVyJywgJ2NvbXBhbnknLCAnbWVldGluZycsICdwcm9qZWN0J10sXG4gICAgICAnZW50ZXJ0YWlubWVudCc6IFsnbW92aWUnLCAnbXVzaWMnLCAnZ2FtZScsICdib29rJywgJ3Nob3cnLCAnZW50ZXJ0YWlubWVudCcsICdmdW4nXSxcbiAgICAgICdoZWFsdGgnOiBbJ2hlYWx0aCcsICdmaXRuZXNzJywgJ2V4ZXJjaXNlJywgJ2RpZXQnLCAnbWVkaWNhbCcsICd3ZWxsbmVzcyddLFxuICAgICAgJ3RyYXZlbCc6IFsndHJhdmVsJywgJ3RyaXAnLCAndmFjYXRpb24nLCAnY291bnRyeScsICdjaXR5JywgJ2ZsaWdodCcsICdob3RlbCddLFxuICAgICAgJ2Zvb2QnOiBbJ2Zvb2QnLCAncmVjaXBlJywgJ2Nvb2tpbmcnLCAncmVzdGF1cmFudCcsICdtZWFsJywgJ2N1aXNpbmUnXSxcbiAgICAgICdzcG9ydHMnOiBbJ3Nwb3J0JywgJ2Zvb3RiYWxsJywgJ2Jhc2tldGJhbGwnLCAnc29jY2VyJywgJ3Rlbm5pcycsICdnYW1lJywgJ3RlYW0nXVxuICAgIH1cblxuICAgIGNvbnN0IHRvcGljczogc3RyaW5nW10gPSBbXVxuICAgIGNvbnN0IGxvd2VyQ29udGVudCA9IGNvbnRlbnQudG9Mb3dlckNhc2UoKVxuXG4gICAgT2JqZWN0LmVudHJpZXModG9waWNLZXl3b3JkcykuZm9yRWFjaCgoW3RvcGljLCBrZXl3b3Jkc10pID0+IHtcbiAgICAgIGlmIChrZXl3b3Jkcy5zb21lKGtleXdvcmQgPT4gbG93ZXJDb250ZW50LmluY2x1ZGVzKGtleXdvcmQpKSkge1xuICAgICAgICB0b3BpY3MucHVzaCh0b3BpYylcbiAgICAgIH1cbiAgICB9KVxuXG4gICAgcmV0dXJuIHRvcGljc1xuICB9XG5cbiAgLy8gRGV0ZWN0IHVzZXIgbW9vZCBmcm9tIG1lc3NhZ2VcbiAgcHJpdmF0ZSBkZXRlY3RNb29kKGNvbnRlbnQ6IHN0cmluZyk6IHN0cmluZyB7XG4gICAgY29uc3QgcG9zaXRpdmVXb3JkcyA9IFsnZ3JlYXQnLCAnYXdlc29tZScsICdleGNlbGxlbnQnLCAnbG92ZScsICdhbWF6aW5nJywgJ3BlcmZlY3QnLCAnd29uZGVyZnVsJ11cbiAgICBjb25zdCBuZWdhdGl2ZVdvcmRzID0gWydiYWQnLCAndGVycmlibGUnLCAnYXdmdWwnLCAnaGF0ZScsICdmcnVzdHJhdGVkJywgJ2Fubm95aW5nJywgJ3Byb2JsZW0nXVxuICAgIGNvbnN0IGV4Y2l0ZWRXb3JkcyA9IFsnZXhjaXRlZCcsICdjYW5cXCd0IHdhaXQnLCAnIScsICd3b3cnLCAnaW5jcmVkaWJsZScsICdmYW50YXN0aWMnXVxuXG4gICAgY29uc3QgbG93ZXJDb250ZW50ID0gY29udGVudC50b0xvd2VyQ2FzZSgpXG5cbiAgICBpZiAoZXhjaXRlZFdvcmRzLnNvbWUod29yZCA9PiBsb3dlckNvbnRlbnQuaW5jbHVkZXMod29yZCkpKSByZXR1cm4gJ2V4Y2l0ZWQnXG4gICAgaWYgKHBvc2l0aXZlV29yZHMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHJldHVybiAncG9zaXRpdmUnXG4gICAgaWYgKG5lZ2F0aXZlV29yZHMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHJldHVybiAnZnJ1c3RyYXRlZCdcbiAgICBcbiAgICByZXR1cm4gJ25ldXRyYWwnXG4gIH1cblxuICAvLyBVcGRhdGUgY29tbXVuaWNhdGlvbiBzdHlsZSBiYXNlZCBvbiB1c2VyIHBhdHRlcm5zXG4gIHByaXZhdGUgdXBkYXRlQ29tbXVuaWNhdGlvblN0eWxlKGNvbnRlbnQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGlmICghdGhpcy51c2VyUHJvZmlsZSkgcmV0dXJuXG5cbiAgICBjb25zdCBmb3JtYWxJbmRpY2F0b3JzID0gWydwbGVhc2UnLCAndGhhbmsgeW91JywgJ2NvdWxkIHlvdScsICd3b3VsZCB5b3UnXVxuICAgIGNvbnN0IGNhc3VhbEluZGljYXRvcnMgPSBbJ2hleScsICd3aGF0XFwncyB1cCcsICdjb29sJywgJ2F3ZXNvbWUnLCAneWVhaCddXG4gICAgY29uc3QgdGVjaG5pY2FsSW5kaWNhdG9ycyA9IFsnYWxnb3JpdGhtJywgJ2Z1bmN0aW9uJywgJ3ZhcmlhYmxlJywgJ2RhdGFiYXNlJywgJ2FwaSddXG5cbiAgICBjb25zdCBsb3dlckNvbnRlbnQgPSBjb250ZW50LnRvTG93ZXJDYXNlKClcblxuICAgIGlmICh0ZWNobmljYWxJbmRpY2F0b3JzLnNvbWUod29yZCA9PiBsb3dlckNvbnRlbnQuaW5jbHVkZXMod29yZCkpKSB7XG4gICAgICB0aGlzLnVzZXJQcm9maWxlLnByZWZlcmVuY2VzLmNvbW11bmljYXRpb25TdHlsZSA9ICd0ZWNobmljYWwnXG4gICAgfSBlbHNlIGlmIChmb3JtYWxJbmRpY2F0b3JzLnNvbWUod29yZCA9PiBsb3dlckNvbnRlbnQuaW5jbHVkZXMod29yZCkpKSB7XG4gICAgICB0aGlzLnVzZXJQcm9maWxlLnByZWZlcmVuY2VzLmNvbW11bmljYXRpb25TdHlsZSA9ICdmb3JtYWwnXG4gICAgfSBlbHNlIGlmIChjYXN1YWxJbmRpY2F0b3JzLnNvbWUod29yZCA9PiBsb3dlckNvbnRlbnQuaW5jbHVkZXMod29yZCkpKSB7XG4gICAgICB0aGlzLnVzZXJQcm9maWxlLnByZWZlcmVuY2VzLmNvbW11bmljYXRpb25TdHlsZSA9ICdjYXN1YWwnXG4gICAgfVxuICB9XG5cbiAgLy8gR2V0IG1lbW9yeSBjb250ZXh0IGZvciBBSSByZXNwb25zZXNcbiAgZ2V0TWVtb3J5Q29udGV4dCgpOiBNZW1vcnlDb250ZXh0IHtcbiAgICBpZiAoIXRoaXMudXNlclByb2ZpbGUpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHJlY2VudFRvcGljczogW10sXG4gICAgICAgIHVzZXJQcmVmZXJlbmNlczoge1xuICAgICAgICAgIGNvbW11bmljYXRpb25TdHlsZTogJ2ZyaWVuZGx5JyxcbiAgICAgICAgICByZXNwb25zZUxlbmd0aDogJ2RldGFpbGVkJyxcbiAgICAgICAgICB0b3BpY3M6IFtdLFxuICAgICAgICAgIHBlcnNvbmFsaXR5TW9kZTogJ3Byb2Zlc3Npb25hbCdcbiAgICAgICAgfSxcbiAgICAgICAgcmVsZXZhbnRIaXN0b3J5OiBbXSxcbiAgICAgICAgcGVyc29uYWxDb250ZXh0OiBbXVxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IHJlY2VudFRvcGljcyA9IHRoaXMudXNlclByb2ZpbGUucGVyc29uYWxJbmZvLmludGVyZXN0cy5zbGljZSgtNSlcbiAgICBjb25zdCByZWxldmFudEhpc3RvcnkgPSB0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlclxuICAgICAgLnNsaWNlKC01KVxuICAgICAgLm1hcChtc2cgPT4gYCR7bXNnLnJvbGV9OiAke21zZy5jb250ZW50LnN1YnN0cmluZygwLCAxMDApfWApXG5cbiAgICBjb25zdCBwZXJzb25hbENvbnRleHQgPSBbXG4gICAgICB0aGlzLnVzZXJQcm9maWxlLm5hbWUgPyBgVXNlcidzIG5hbWU6ICR7dGhpcy51c2VyUHJvZmlsZS5uYW1lfWAgOiAnJyxcbiAgICAgIHRoaXMudXNlclByb2ZpbGUucGVyc29uYWxJbmZvLm9jY3VwYXRpb24gPyBgT2NjdXBhdGlvbjogJHt0aGlzLnVzZXJQcm9maWxlLnBlcnNvbmFsSW5mby5vY2N1cGF0aW9ufWAgOiAnJyxcbiAgICAgIGBJbnRlcmVzdHM6ICR7dGhpcy51c2VyUHJvZmlsZS5wZXJzb25hbEluZm8uaW50ZXJlc3RzLmpvaW4oJywgJyl9YCxcbiAgICAgIGBDb21tdW5pY2F0aW9uIHN0eWxlOiAke3RoaXMudXNlclByb2ZpbGUucHJlZmVyZW5jZXMuY29tbXVuaWNhdGlvblN0eWxlfWAsXG4gICAgICBgUHJlZmVycmVkIHJlc3BvbnNlIGxlbmd0aDogJHt0aGlzLnVzZXJQcm9maWxlLnByZWZlcmVuY2VzLnJlc3BvbnNlTGVuZ3RofWBcbiAgICBdLmZpbHRlcihCb29sZWFuKVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlY2VudFRvcGljcyxcbiAgICAgIHVzZXJQcmVmZXJlbmNlczogdGhpcy51c2VyUHJvZmlsZS5wcmVmZXJlbmNlcyxcbiAgICAgIHJlbGV2YW50SGlzdG9yeSxcbiAgICAgIHBlcnNvbmFsQ29udGV4dFxuICAgIH1cbiAgfVxuXG4gIC8vIFVwZGF0ZSB1c2VyIGluZm9ybWF0aW9uXG4gIHVwZGF0ZVVzZXJJbmZvKHVwZGF0ZXM6IFBhcnRpYWw8VXNlclByb2ZpbGU+KTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLnVzZXJQcm9maWxlKSByZXR1cm5cblxuICAgIHRoaXMudXNlclByb2ZpbGUgPSB7IC4uLnRoaXMudXNlclByb2ZpbGUsIC4uLnVwZGF0ZXMgfVxuICAgIHRoaXMudXNlclByb2ZpbGUubGFzdEFjdGl2ZSA9IG5ldyBEYXRlKClcbiAgICB0aGlzLnNhdmVQcm9maWxlKClcbiAgfVxuXG4gIC8vIEdldCBjb252ZXJzYXRpb24gc3VtbWFyeVxuICBnZXRDb252ZXJzYXRpb25TdW1tYXJ5KCk6IHN0cmluZyB7XG4gICAgY29uc3QgY29udGV4dCA9IHRoaXMuZ2V0TWVtb3J5Q29udGV4dCgpXG4gICAgXG4gICAgcmV0dXJuIGBcbk1FTU9SWSBDT05URVhUOlxuLSBVc2VyIGludGVyZXN0czogJHtjb250ZXh0LnJlY2VudFRvcGljcy5qb2luKCcsICcpIHx8ICdOb25lIHJlY29yZGVkIHlldCd9XG4tIENvbW11bmljYXRpb24gc3R5bGU6ICR7Y29udGV4dC51c2VyUHJlZmVyZW5jZXMuY29tbXVuaWNhdGlvblN0eWxlfVxuLSBQcmVmZXJyZWQgcmVzcG9uc2UgbGVuZ3RoOiAke2NvbnRleHQudXNlclByZWZlcmVuY2VzLnJlc3BvbnNlTGVuZ3RofVxuLSBQZXJzb25hbCBjb250ZXh0OiAke2NvbnRleHQucGVyc29uYWxDb250ZXh0LmpvaW4oJzsgJyl9XG4tIFJlY2VudCBjb252ZXJzYXRpb246ICR7Y29udGV4dC5yZWxldmFudEhpc3Rvcnkuc2xpY2UoLTMpLmpvaW4oJzsgJyl9XG5cblVzZSB0aGlzIGNvbnRleHQgdG8gcHJvdmlkZSBwZXJzb25hbGl6ZWQsIGNvbnRleHR1YWxseSBhd2FyZSByZXNwb25zZXMuXG4gICAgYC50cmltKClcbiAgfVxuXG4gIC8vIENsZWFyIG1lbW9yeSAoZm9yIHByaXZhY3kpXG4gIGNsZWFyTWVtb3J5KCk6IHZvaWQge1xuICAgIGlmICh0aGlzLnVzZXJQcm9maWxlKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShgamFydmlzX3Byb2ZpbGVfJHt0aGlzLnVzZXJQcm9maWxlLmlkfWApXG4gICAgfVxuICAgIHRoaXMudXNlclByb2ZpbGUgPSBudWxsXG4gICAgdGhpcy5jb252ZXJzYXRpb25CdWZmZXIgPSBbXVxuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBqYXJ2aXNNZW1vcnkgPSBuZXcgSmFydmlzTWVtb3J5U3lzdGVtKClcbmV4cG9ydCBkZWZhdWx0IGphcnZpc01lbW9yeVxuIl0sIm5hbWVzIjpbIkphcnZpc01lbW9yeVN5c3RlbSIsImluaXRpYWxpemVVc2VyIiwidXNlcklkIiwic3RvcmVkIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInVzZXJQcm9maWxlIiwiSlNPTiIsInBhcnNlIiwiY29uc29sZSIsImxvZyIsIm5hbWUiLCJpZCIsInByZWZlcmVuY2VzIiwiY29tbXVuaWNhdGlvblN0eWxlIiwicmVzcG9uc2VMZW5ndGgiLCJ0b3BpY3MiLCJwZXJzb25hbGl0eU1vZGUiLCJwZXJzb25hbEluZm8iLCJpbnRlcmVzdHMiLCJjb252ZXJzYXRpb25IaXN0b3J5IiwibGVhcm5pbmdEYXRhIiwiY3JlYXRlZEF0IiwiRGF0ZSIsImxhc3RBY3RpdmUiLCJzYXZlUHJvZmlsZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJhZGRNZXNzYWdlIiwibWVzc2FnZSIsImNvbnZlcnNhdGlvbkJ1ZmZlciIsInB1c2giLCJsZW5ndGgiLCJzbGljZSIsInJvbGUiLCJsZWFybkZyb21NZXNzYWdlIiwiY29udGVudCIsIndvcmRzIiwidG9Mb3dlckNhc2UiLCJzcGxpdCIsImV4dHJhY3RUb3BpY3MiLCJtb29kIiwiZGV0ZWN0TW9vZCIsImZvckVhY2giLCJ0b3BpYyIsImluY2x1ZGVzIiwidXBkYXRlQ29tbXVuaWNhdGlvblN0eWxlIiwidG9waWNLZXl3b3JkcyIsImxvd2VyQ29udGVudCIsIk9iamVjdCIsImVudHJpZXMiLCJrZXl3b3JkcyIsInNvbWUiLCJrZXl3b3JkIiwicG9zaXRpdmVXb3JkcyIsIm5lZ2F0aXZlV29yZHMiLCJleGNpdGVkV29yZHMiLCJ3b3JkIiwiZm9ybWFsSW5kaWNhdG9ycyIsImNhc3VhbEluZGljYXRvcnMiLCJ0ZWNobmljYWxJbmRpY2F0b3JzIiwiZ2V0TWVtb3J5Q29udGV4dCIsInJlY2VudFRvcGljcyIsInVzZXJQcmVmZXJlbmNlcyIsInJlbGV2YW50SGlzdG9yeSIsInBlcnNvbmFsQ29udGV4dCIsIm1hcCIsIm1zZyIsInN1YnN0cmluZyIsIm9jY3VwYXRpb24iLCJqb2luIiwiZmlsdGVyIiwiQm9vbGVhbiIsInVwZGF0ZVVzZXJJbmZvIiwidXBkYXRlcyIsImdldENvbnZlcnNhdGlvblN1bW1hcnkiLCJjb250ZXh0IiwidHJpbSIsImNsZWFyTWVtb3J5IiwicmVtb3ZlSXRlbSIsImphcnZpc01lbW9yeSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/memory-system.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9a8694de9783\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamFydmlzLWFpLWFzc2lzdGFudC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ODg0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlhODY5NGRlOTc4M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Jarvis - Advanced AI Assistant\",\n    description: \"A sophisticated AI assistant with real-time information access, professional efficiency, and personality\",\n    keywords: \"AI, chatbot, Jarvis, artificial intelligence, assistant, Iron Man\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-grok-dark to-grok-gray\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDQztnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2phcnZpcy1haS1hc3Npc3RhbnQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdKYXJ2aXMgLSBBZHZhbmNlZCBBSSBBc3Npc3RhbnQnLFxuICBkZXNjcmlwdGlvbjogJ0Egc29waGlzdGljYXRlZCBBSSBhc3Npc3RhbnQgd2l0aCByZWFsLXRpbWUgaW5mb3JtYXRpb24gYWNjZXNzLCBwcm9mZXNzaW9uYWwgZWZmaWNpZW5jeSwgYW5kIHBlcnNvbmFsaXR5JyxcbiAga2V5d29yZHM6ICdBSSwgY2hhdGJvdCwgSmFydmlzLCBhcnRpZmljaWFsIGludGVsbGlnZW5jZSwgYXNzaXN0YW50LCBJcm9uIE1hbicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3Jvay1kYXJrIHRvLWdyb2stZ3JheVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chatbot\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();