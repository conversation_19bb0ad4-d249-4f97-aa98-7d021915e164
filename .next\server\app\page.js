/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q21vd2VlJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q2NoYXRib3QlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQStHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamFydmlzLWFpLWFzc2lzdGFudC8/NGY2YiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vd2VlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(ssr)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./src/components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Home() {\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            content: \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n            role: \"assistant\",\n            timestamp: new Date()\n        }\n    ]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            // TODO: Replace with actual API call\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    messages: messages,\n                    onSendMessage: handleSendMessage,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(ssr)/./src/components/MessageBubble.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageInput */ \"(ssr)/./src/components/MessageInput.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatInterface({ messages, onSendMessage, isLoading }) {\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isLoading\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const message = inputValue.trim();\n        setInputValue(\"\");\n        await onSendMessage(message);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full tech-grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto px-4 py-6 space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-red-500/20 bg-grok-gray/50 backdrop-blur-sm shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        value: inputValue,\n                        onChange: setInputValue,\n                        onSend: handleSend,\n                        onKeyPress: handleKeyPress,\n                        disabled: isLoading,\n                        placeholder: \"How may I assist you today? I'm at your service...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scan-line\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-grok-gray border-b border-grok-muted/20 px-4 py-3 tech-grid relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"J\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 17,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-grok-text font-semibold text-xl bg-gradient-to-r from-red-400 to-yellow-400 bg-clip-text text-transparent\",\n                                                children: \"JARVIS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 20,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-grok-muted text-xs font-mono tracking-wider\",\n                                                children: \"ADVANCED AI ASSISTANT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 23,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-2 text-grok-muted text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full holographic-glow\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono tracking-wider\",\n                                                children: \"ONLINE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: \"p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-grok-text\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 6h16M4 12h16M4 18h16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 right-4 bg-grok-gray border border-red-500/30 rounded-lg shadow-lg p-2 z-50 glowing-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"NEW CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"SETTINGS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"ABOUT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageBubble.tsx":
/*!******************************************!*\
  !*** ./src/components/MessageBubble.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageBubble)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction MessageBubble({ message }) {\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex ${isUser ? \"justify-end\" : \"justify-start\"} ${isUser ? \"message-slide-in-right\" : \"message-slide-in-left\"}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex max-w-[80%] ${isUser ? \"flex-row-reverse\" : \"flex-row\"} items-start space-x-3`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${isUser ? \"bg-red-600 ml-3 holographic-glow\" : \"bg-gradient-to-r from-red-600 to-yellow-500 mr-3 arc-reactor avatar-pulse\"}`,\n                    children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-medium\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex flex-col ${isUser ? \"items-end\" : \"items-start\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `rounded-2xl px-4 py-3 shadow-lg transition-all duration-300 ${isUser ? \"bg-gradient-to-r from-red-600 to-red-700 text-white border border-red-500/30 holographic-glow\" : \"bg-grok-gray border border-red-500/20 text-grok-text glowing-border hover:border-red-500/40\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"whitespace-pre-wrap break-words\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                isAssistant && message.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 pt-2 border-t border-red-500/20 text-xs text-grok-muted font-mono\",\n                                    children: [\n                                        message.metadata.sources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-red-400\",\n                                            children: [\n                                                \"SOURCES: \",\n                                                message.metadata.sources.join(\", \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        message.metadata.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-yellow-400\",\n                                            children: [\n                                                \"CONFIDENCE: \",\n                                                Math.round(message.metadata.confidence * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `text-xs text-grok-muted mt-1 font-mono tracking-wider ${isUser ? \"text-right\" : \"text-left\"}`,\n                            children: formatTimeAgo(message.timestamp).toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n// Utility function for date formatting\nfunction formatTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n    return `${Math.floor(diffInSeconds / 86400)}d ago`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageBubble.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/MessageInput.tsx":
/*!*****************************************!*\
  !*** ./src/components/MessageInput.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MessageInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction MessageInput({ value, onChange, onSend, onKeyPress, disabled, placeholder = \"Type your message...\" }) {\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `relative flex items-end space-x-3 p-4 rounded-2xl border transition-all duration-300 shadow-lg ${isFocused ? \"border-red-500 bg-grok-dark/50 glowing-border\" : \"border-red-500/20 bg-grok-gray/30 hover:border-red-500/40\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: value,\n                    onChange: (e)=>onChange(e.target.value),\n                    onKeyPress: onKeyPress,\n                    onFocus: ()=>setIsFocused(true),\n                    onBlur: ()=>setIsFocused(false),\n                    disabled: disabled,\n                    placeholder: placeholder,\n                    rows: 1,\n                    className: \"w-full bg-transparent text-grok-text placeholder-red-400/60 resize-none outline-none min-h-[24px] max-h-32 overflow-y-auto font-mono\",\n                    style: {\n                        height: \"auto\",\n                        minHeight: \"24px\"\n                    },\n                    onInput: (e)=>{\n                        const target = e.target;\n                        target.style.height = \"auto\";\n                        target.style.height = `${Math.min(target.scrollHeight, 128)}px`;\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onSend,\n                disabled: disabled || !value.trim(),\n                className: `flex-shrink-0 p-3 rounded-xl transition-all duration-300 shadow-lg ${disabled || !value.trim() ? \"bg-grok-muted/20 text-grok-muted cursor-not-allowed\" : \"bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 active:scale-95 button-glow border border-red-500/30\"}`,\n                children: disabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 border-2 border-red-400 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            value.length > 500 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-6 right-0 text-xs text-grok-muted\",\n                children: [\n                    value.length,\n                    \"/1000\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MessageInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./src/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TypingIndicator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start message-slide-in-left\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[80%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-red-600 to-yellow-500 flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-grok-gray border border-red-500/20 rounded-2xl px-4 py-3 shadow-lg glowing-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-400 text-sm mr-2 font-mono tracking-wider\",\n                                children: \"PROCESSING\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-2 text-yellow-400 text-xs font-mono\",\n                                children: \"▶\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TypingIndicator.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9a8694de9783\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vamFydmlzLWFpLWFzc2lzdGFudC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ODg0NiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjlhODY5NGRlOTc4M1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"Jarvis - Advanced AI Assistant\",\n    description: \"A sophisticated AI assistant with real-time information access, professional efficiency, and personality\",\n    keywords: \"AI, chatbot, Jarvis, artificial intelligence, assistant, Iron Man\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-gradient-to-br from-grok-dark to-grok-gray\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col min-h-screen\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNkLDRFQUFDQztnQkFBSUQsV0FBVTswQkFDWko7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsid2VicGFjazovL2phcnZpcy1haS1hc3Npc3RhbnQvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCAnLi9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6ICdKYXJ2aXMgLSBBZHZhbmNlZCBBSSBBc3Npc3RhbnQnLFxuICBkZXNjcmlwdGlvbjogJ0Egc29waGlzdGljYXRlZCBBSSBhc3Npc3RhbnQgd2l0aCByZWFsLXRpbWUgaW5mb3JtYXRpb24gYWNjZXNzLCBwcm9mZXNzaW9uYWwgZWZmaWNpZW5jeSwgYW5kIHBlcnNvbmFsaXR5JyxcbiAga2V5d29yZHM6ICdBSSwgY2hhdGJvdCwgSmFydmlzLCBhcnRpZmljaWFsIGludGVsbGlnZW5jZSwgYXNzaXN0YW50LCBJcm9uIE1hbicsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3Jvay1kYXJrIHRvLWdyb2stZ3JheVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuXCI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsImRpdiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\chatbot\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();