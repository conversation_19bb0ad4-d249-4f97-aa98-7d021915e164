"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test-search/route";
exports.ids = ["app/api/test-search/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-search%2Froute&page=%2Fapi%2Ftest-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-search%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-search%2Froute&page=%2Fapi%2Ftest-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-search%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_test_search_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/test-search/route.ts */ \"(rsc)/./src/app/api/test-search/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test-search/route\",\n        pathname: \"/api/test-search\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-search/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\test-search\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_test_search_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/test-search/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-search%2Froute&page=%2Fapi%2Ftest-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-search%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/test-search/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/test-search/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_realtime_info__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/realtime-info */ \"(rsc)/./src/lib/realtime-info.ts\");\n\n\nasync function GET(request) {\n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get(\"q\") || \"test search\";\n    const type = searchParams.get(\"type\") || \"search\";\n    try {\n        console.log(\"Testing real-time info with:\", {\n            query,\n            type\n        });\n        console.log(\"Environment variables:\", {\n            hasGoogleKey: !!\"AIzaSyDVNAiPuWY831r5WPbKoiiC5dowDd-70SE\",\n            hasSearchEngineId: !!\"11662a26b3f7c4373\",\n            hasNewsKey: !!\"c38ce5b592404b7d8adc17b584604db6\",\n            googleKey: \"AIzaSyDVNAiPuWY831r5WPbKoiiC5dowDd-70SE\"?.substring(0, 10) + \"...\",\n            searchEngineId: \"11662a26b3f7c4373\"\n        });\n        let results = {};\n        if (type === \"search\") {\n            console.log(\"Testing Google Search...\");\n            results.searchResults = await (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_1__.searchWeb)(query, 3);\n            console.log(\"Search results:\", results.searchResults);\n        } else if (type === \"news\") {\n            console.log(\"Testing News API...\");\n            results.newsResults = await (0,_lib_realtime_info__WEBPACK_IMPORTED_MODULE_1__.getLatestNews)(query, 3);\n            console.log(\"News results:\", results.newsResults);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            query,\n            type,\n            results,\n            environment: {\n                hasGoogleKey: !!\"AIzaSyDVNAiPuWY831r5WPbKoiiC5dowDd-70SE\",\n                hasSearchEngineId: !!\"11662a26b3f7c4373\",\n                hasNewsKey: !!\"c38ce5b592404b7d8adc17b584604db6\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Test search error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\",\n            query,\n            type\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test-search/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/realtime-info.ts":
/*!**********************************!*\
  !*** ./src/lib/realtime-info.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getLatestNews: () => (/* binding */ getLatestNews),\n/* harmony export */   getWeather: () => (/* binding */ getWeather),\n/* harmony export */   needsRealTimeInfo: () => (/* binding */ needsRealTimeInfo),\n/* harmony export */   scrapeWebContent: () => (/* binding */ scrapeWebContent),\n/* harmony export */   searchWeb: () => (/* binding */ searchWeb)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/lib/axios.js\");\n// Real-time information fetching utilities\n\n// Google Custom Search API\nasync function searchWeb(query, maxResults = 5) {\n    try {\n        const apiKey = \"AIzaSyDVNAiPuWY831r5WPbKoiiC5dowDd-70SE\";\n        const searchEngineId = \"11662a26b3f7c4373\";\n        // Check if API keys are properly configured (not placeholder values)\n        if (!apiKey || !searchEngineId || apiKey.includes(\"your_\") || searchEngineId.includes(\"your_\") || apiKey === \"your_google_search_api_key_here\" || searchEngineId === \"your_search_engine_id_here\") {\n            console.log(\"Google Search API not configured, using fallback\");\n            return getFallbackSearchResults(query);\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"https://www.googleapis.com/customsearch/v1\", {\n            params: {\n                key: apiKey,\n                cx: searchEngineId,\n                q: query,\n                num: maxResults\n            },\n            timeout: 5000 // Add timeout to prevent hanging\n        });\n        return response.data.items?.map((item)=>({\n                title: item.title,\n                link: item.link,\n                snippet: item.snippet,\n                source: new URL(item.link).hostname\n            })) || [];\n    } catch (error) {\n        console.log(\"Web search API call failed, using fallback response\");\n        return getFallbackSearchResults(query);\n    }\n}\n// News API\nasync function getLatestNews(topic, maxResults = 5) {\n    try {\n        const apiKey = \"c38ce5b592404b7d8adc17b584604db6\";\n        // Check if API key is properly configured\n        if (!apiKey || apiKey.includes(\"your_\") || apiKey === \"your_news_api_key_here\") {\n            console.log(\"News API not configured, using fallback\");\n            return getFallbackNews(topic);\n        }\n        const url = topic ? `https://newsapi.org/v2/everything?q=${encodeURIComponent(topic)}&sortBy=publishedAt&pageSize=${maxResults}` : `https://newsapi.org/v2/top-headlines?country=us&pageSize=${maxResults}`;\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            headers: {\n                \"X-API-Key\": apiKey\n            },\n            timeout: 5000\n        });\n        return response.data.articles?.map((article)=>({\n                title: article.title,\n                description: article.description,\n                url: article.url,\n                source: article.source.name,\n                publishedAt: article.publishedAt\n            })) || [];\n    } catch (error) {\n        console.log(\"News API call failed, using fallback response\");\n        return getFallbackNews(topic);\n    }\n}\n// Weather API (OpenWeatherMap)\nasync function getWeather(location) {\n    try {\n        const apiKey = \"your_weather_api_key_here\";\n        if (!apiKey) {\n            console.log(\"Weather API not configured, using fallback\");\n            return getFallbackWeather(location);\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://api.openweathermap.org/data/2.5/weather`, {\n            params: {\n                q: location,\n                appid: apiKey,\n                units: \"metric\"\n            }\n        });\n        const data = response.data;\n        return {\n            location: data.name,\n            temperature: Math.round(data.main.temp),\n            description: data.weather[0].description,\n            humidity: data.main.humidity,\n            windSpeed: data.wind.speed\n        };\n    } catch (error) {\n        console.error(\"Weather API error:\", error);\n        return getFallbackWeather(location);\n    }\n}\n// Simple web scraping for specific information\nasync function scrapeWebContent(url) {\n    try {\n        // Note: This is a basic implementation. In production, you'd want more robust scraping\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n            timeout: 5000,\n            headers: {\n                \"User-Agent\": \"Mozilla/5.0 (compatible; GrokBot/1.0)\"\n            }\n        });\n        // Basic text extraction (you could use cheerio for more sophisticated parsing)\n        const text = response.data.replace(/<[^>]*>/g, \" \").replace(/\\s+/g, \" \").trim();\n        return text.substring(0, 1000) // Limit to 1000 characters\n        ;\n    } catch (error) {\n        console.error(\"Web scraping error:\", error);\n        return \"Unable to fetch content from this URL.\";\n    }\n}\n// Determine if a query needs real-time information\nfunction needsRealTimeInfo(query) {\n    const lowerQuery = query.toLowerCase();\n    // Weather queries\n    if (lowerQuery.includes(\"weather\") || lowerQuery.includes(\"temperature\") || lowerQuery.includes(\"forecast\")) {\n        const locationMatch = lowerQuery.match(/weather.*?(?:in|for|at)\\s+([a-zA-Z\\s]+)/);\n        return {\n            needsInfo: true,\n            type: \"weather\",\n            extractedQuery: locationMatch?.[1]?.trim() || \"current location\"\n        };\n    }\n    // News queries\n    if (lowerQuery.includes(\"news\") || lowerQuery.includes(\"latest\") || lowerQuery.includes(\"recent\") || lowerQuery.includes(\"current events\") || lowerQuery.includes(\"happening now\")) {\n        return {\n            needsInfo: true,\n            type: \"news\",\n            extractedQuery: query\n        };\n    }\n    // Current/recent information queries\n    if (lowerQuery.includes(\"current\") || lowerQuery.includes(\"today\") || lowerQuery.includes(\"now\") || lowerQuery.includes(\"2024\") || lowerQuery.includes(\"2025\") || lowerQuery.includes(\"this year\")) {\n        return {\n            needsInfo: true,\n            type: \"search\",\n            extractedQuery: query\n        };\n    }\n    return {\n        needsInfo: false,\n        type: \"search\"\n    };\n}\n// Fallback functions for when APIs aren't configured\nfunction getFallbackSearchResults(query) {\n    return [\n        {\n            title: \"Real-time search not configured\",\n            link: \"#\",\n            snippet: `I'd love to search for \"${query}\" but the Google Search API isn't set up yet. To enable real-time web search, you'll need to configure the Google Custom Search API.`,\n            source: \"system\"\n        }\n    ];\n}\nfunction getFallbackNews(topic) {\n    return [\n        {\n            title: \"News API not configured\",\n            description: `I'd love to get the latest news${topic ? ` about ${topic}` : \"\"} but the News API isn't set up yet. To enable real-time news, you'll need to configure the News API.`,\n            url: \"#\",\n            source: \"system\",\n            publishedAt: new Date().toISOString()\n        }\n    ];\n}\nfunction getFallbackWeather(location) {\n    return {\n        location: location,\n        temperature: 22,\n        description: \"Weather API not configured - this is placeholder data\",\n        humidity: 60,\n        windSpeed: 5\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/realtime-info.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/axios","vendor-chunks/next","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-search%2Froute&page=%2Fapi%2Ftest-search%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-search%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();