/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        'grok-dark': '#0f0f0f',
        'grok-gray': '#1a1a1a',
        'grok-blue': '#3b82f6', // More refined blue
        'grok-green': '#06b6d4', // <PERSON>an for Jarvis tech feel
        'grok-text': '#e7e9ea',
        'grok-muted': '#71767b',
        'jarvis-blue': '#3b82f6',
        'jarvis-cyan': '#06b6d4',
        'jarvis-gold': '#f59e0b',
      },
      fontFamily: {
        'mono': ['JetBrains Mono', 'monospace'],
      },
    },
  },
  plugins: [],
}
