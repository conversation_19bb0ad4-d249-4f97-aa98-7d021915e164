// Express server for WebSocket support and additional API endpoints
// This will be used for real-time features and backend services

const express = require('express')
const http = require('http')
const socketIo = require('socket.io')
const cors = require('cors')
require('dotenv').config()

const app = express()
const server = http.createServer(app)
const io = socketIo(server, {
  cors: {
    origin: process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
})

const PORT = process.env.PORT || 3001

// Middleware
app.use(cors())
app.use(express.json())

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    message: 'Grok Chatbot Server is running',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  })
})

// WebSocket connection handling
io.on('connection', (socket) => {
  console.log('User connected:', socket.id)

  // Handle chat messages
  socket.on('chat_message', async (data) => {
    try {
      // TODO: Process message with AI service
      // TODO: Store message in database
      // TODO: Emit response back to client
      
      console.log('Received message:', data)
      
      // Placeholder response
      setTimeout(() => {
        socket.emit('ai_response', {
          id: Date.now().toString(),
          content: "This is a WebSocket response! Real AI integration coming soon...",
          role: 'assistant',
          timestamp: new Date(),
          metadata: {
            source: 'websocket',
            processingTime: 1000
          }
        })
      }, 1000)
      
    } catch (error) {
      console.error('Error processing message:', error)
      socket.emit('error', { message: 'Failed to process message' })
    }
  })

  // Handle typing indicators
  socket.on('typing_start', () => {
    socket.broadcast.emit('user_typing', { userId: socket.id })
  })

  socket.on('typing_stop', () => {
    socket.broadcast.emit('user_stopped_typing', { userId: socket.id })
  })

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id)
  })
})

// Start server
server.listen(PORT, () => {
  console.log(`🚀 Grok Chatbot Server running on port ${PORT}`)
  console.log(`📡 WebSocket server ready for real-time communication`)
  console.log(`🔗 Health check: http://localhost:${PORT}/health`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully')
  server.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})
