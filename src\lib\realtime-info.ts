// Real-time information fetching utilities
import axios from 'axios'

export interface SearchResult {
  title: string
  link: string
  snippet: string
  source: string
}

export interface NewsArticle {
  title: string
  description: string
  url: string
  source: string
  publishedAt: string
}

export interface WeatherInfo {
  location: string
  temperature: number
  description: string
  humidity: number
  windSpeed: number
}

// Google Custom Search API
export async function searchWeb(query: string, maxResults: number = 5): Promise<SearchResult[]> {
  try {
    const apiKey = process.env.GOOGLE_SEARCH_API_KEY
    const searchEngineId = process.env.GOOGLE_SEARCH_ENGINE_ID

    // Check if API keys are properly configured (not placeholder values)
    if (!apiKey || !searchEngineId ||
        apiKey.includes('your_') || searchEngineId.includes('your_') ||
        apiKey === 'your_google_search_api_key_here' ||
        searchEngineId === 'your_search_engine_id_here') {
      console.log('Google Search API not configured, using fallback')
      return getFallbackSearchResults(query)
    }

    const response = await axios.get('https://www.googleapis.com/customsearch/v1', {
      params: {
        key: apiKey,
        cx: searchEngineId,
        q: query,
        num: maxResults
      },
      timeout: 5000 // Add timeout to prevent hanging
    })

    return response.data.items?.map((item: any) => ({
      title: item.title,
      link: item.link,
      snippet: item.snippet,
      source: new URL(item.link).hostname
    })) || []

  } catch (error) {
    console.log('Web search API call failed, using fallback response')
    return getFallbackSearchResults(query)
  }
}

// News API
export async function getLatestNews(topic?: string, maxResults: number = 5): Promise<NewsArticle[]> {
  try {
    const apiKey = process.env.NEWS_API_KEY

    // Check if API key is properly configured
    if (!apiKey || apiKey.includes('your_') || apiKey === 'your_news_api_key_here') {
      console.log('News API not configured, using fallback')
      return getFallbackNews(topic)
    }

    const url = topic
      ? `https://newsapi.org/v2/everything?q=${encodeURIComponent(topic)}&sortBy=publishedAt&pageSize=${maxResults}`
      : `https://newsapi.org/v2/top-headlines?country=us&pageSize=${maxResults}`

    const response = await axios.get(url, {
      headers: { 'X-API-Key': apiKey },
      timeout: 5000
    })

    return response.data.articles?.map((article: any) => ({
      title: article.title,
      description: article.description,
      url: article.url,
      source: article.source.name,
      publishedAt: article.publishedAt
    })) || []

  } catch (error) {
    console.log('News API call failed, using fallback response')
    return getFallbackNews(topic)
  }
}

// Weather API (OpenWeatherMap)
export async function getWeather(location: string): Promise<WeatherInfo | null> {
  try {
    const apiKey = process.env.WEATHER_API_KEY
    
    if (!apiKey) {
      console.log('Weather API not configured, using fallback')
      return getFallbackWeather(location)
    }

    const response = await axios.get(`https://api.openweathermap.org/data/2.5/weather`, {
      params: {
        q: location,
        appid: apiKey,
        units: 'metric'
      }
    })

    const data = response.data
    return {
      location: data.name,
      temperature: Math.round(data.main.temp),
      description: data.weather[0].description,
      humidity: data.main.humidity,
      windSpeed: data.wind.speed
    }

  } catch (error) {
    console.error('Weather API error:', error)
    return getFallbackWeather(location)
  }
}

// Simple web scraping for specific information
export async function scrapeWebContent(url: string): Promise<string> {
  try {
    // Note: This is a basic implementation. In production, you'd want more robust scraping
    const response = await axios.get(url, {
      timeout: 5000,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; GrokBot/1.0)'
      }
    })

    // Basic text extraction (you could use cheerio for more sophisticated parsing)
    const text = response.data.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
    return text.substring(0, 1000) // Limit to 1000 characters

  } catch (error) {
    console.error('Web scraping error:', error)
    return 'Unable to fetch content from this URL.'
  }
}

// Determine if a query needs real-time information
export function needsRealTimeInfo(query: string): {
  needsInfo: boolean
  type: 'search' | 'news' | 'weather' | 'current_events'
  extractedQuery?: string
} {
  const lowerQuery = query.toLowerCase()
  
  // Weather queries
  if (lowerQuery.includes('weather') || lowerQuery.includes('temperature') || lowerQuery.includes('forecast')) {
    const locationMatch = lowerQuery.match(/weather.*?(?:in|for|at)\s+([a-zA-Z\s]+)/)
    return {
      needsInfo: true,
      type: 'weather',
      extractedQuery: locationMatch?.[1]?.trim() || 'current location'
    }
  }
  
  // News queries
  if (lowerQuery.includes('news') || lowerQuery.includes('latest') || lowerQuery.includes('recent') || 
      lowerQuery.includes('current events') || lowerQuery.includes('happening now')) {
    return {
      needsInfo: true,
      type: 'news',
      extractedQuery: query
    }
  }
  
  // Current/recent information queries
  if (lowerQuery.includes('current') || lowerQuery.includes('today') || lowerQuery.includes('now') ||
      lowerQuery.includes('2024') || lowerQuery.includes('2025') || lowerQuery.includes('this year')) {
    return {
      needsInfo: true,
      type: 'search',
      extractedQuery: query
    }
  }
  
  return { needsInfo: false, type: 'search' }
}

// Fallback functions for when APIs aren't configured
function getFallbackSearchResults(query: string): SearchResult[] {
  return [{
    title: "Real-time search not configured",
    link: "#",
    snippet: `I'd love to search for "${query}" but the Google Search API isn't set up yet. To enable real-time web search, you'll need to configure the Google Custom Search API.`,
    source: "system"
  }]
}

function getFallbackNews(topic?: string): NewsArticle[] {
  return [{
    title: "News API not configured",
    description: `I'd love to get the latest news${topic ? ` about ${topic}` : ''} but the News API isn't set up yet. To enable real-time news, you'll need to configure the News API.`,
    url: "#",
    source: "system",
    publishedAt: new Date().toISOString()
  }]
}

function getFallbackWeather(location: string): WeatherInfo {
  return {
    location: location,
    temperature: 22,
    description: "Weather API not configured - this is placeholder data",
    humidity: 60,
    windSpeed: 5
  }
}
