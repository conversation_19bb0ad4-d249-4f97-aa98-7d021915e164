export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: Date
  metadata?: {
    sources?: string[]
    confidence?: number
    processingTime?: number
  }
}

export interface ChatSession {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  userId?: string
}

export interface User {
  id: string
  username: string
  email: string
  preferences: UserPreferences
  createdAt: Date
}

export interface UserPreferences {
  personality: 'witty' | 'professional' | 'casual' | 'edgy'
  responseLength: 'short' | 'medium' | 'long'
  realTimeInfo: boolean
  theme: 'dark' | 'light'
  language: string
}

export interface AIResponse {
  message: string
  sources?: string[]
  confidence: number
  processingTime: number
  metadata?: any
}

export interface ChatContextType {
  messages: Message[]
  isLoading: boolean
  sendMessage: (content: string) => Promise<void>
  clearChat: () => void
  currentSession: ChatSession | null
}
