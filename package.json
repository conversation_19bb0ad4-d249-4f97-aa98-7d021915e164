{"name": "grok-chatbot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@prisma/client": "^5.7.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "eslint": "^8", "eslint-config-next": "14.0.4", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "next": "^14.2.30", "openai": "^4.20.1", "postcss": "^8", "prisma": "^5.7.0", "react": "^18", "react-dom": "^18", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwindcss": "^3.3.0", "typescript": "^5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5"}}