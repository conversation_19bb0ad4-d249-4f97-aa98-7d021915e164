// Jarvis Memory System - Advanced AI Memory and Learning
import { Message } from '@/types/chat'

export interface UserProfile {
  id: string
  name?: string
  preferences: {
    communicationStyle: 'formal' | 'casual' | 'technical' | 'friendly'
    responseLength: 'brief' | 'detailed' | 'comprehensive'
    topics: string[]
    personalityMode: 'professional' | 'witty' | 'helpful' | 'sarcastic'
  }
  personalInfo: {
    occupation?: string
    interests: string[]
    timezone?: string
    location?: string
  }
  conversationHistory: ConversationSummary[]
  learningData: LearningPattern[]
  createdAt: Date
  lastActive: Date
}

export interface ConversationSummary {
  id: string
  date: Date
  topics: string[]
  keyPoints: string[]
  userMood: 'positive' | 'neutral' | 'frustrated' | 'excited'
  importantFacts: string[]
  followUpNeeded: boolean
}

export interface LearningPattern {
  pattern: string
  frequency: number
  context: string
  lastSeen: Date
  importance: 'low' | 'medium' | 'high'
}

export interface MemoryContext {
  recentTopics: string[]
  userPreferences: UserProfile['preferences']
  relevantHistory: string[]
  personalContext: string[]
  currentMood?: string
}

class JarvisMemorySystem {
  private userProfile: UserProfile | null = null
  private conversationBuffer: Message[] = []

  // Initialize or load user profile
  async initializeUser(userId: string = 'default'): Promise<UserProfile> {
    // Try to load existing profile from localStorage
    const stored = localStorage.getItem(`jarvis_profile_${userId}`)
    
    if (stored) {
      this.userProfile = JSON.parse(stored)
      console.log('Loaded existing user profile:', this.userProfile?.name || 'Anonymous')
    } else {
      // Create new profile
      this.userProfile = {
        id: userId,
        preferences: {
          communicationStyle: 'friendly',
          responseLength: 'detailed',
          topics: [],
          personalityMode: 'professional'
        },
        personalInfo: {
          interests: [],
        },
        conversationHistory: [],
        learningData: [],
        createdAt: new Date(),
        lastActive: new Date()
      }
      this.saveProfile()
      console.log('Created new user profile')
    }
    
    return this.userProfile
  }

  // Save profile to localStorage
  private saveProfile(): void {
    if (this.userProfile) {
      localStorage.setItem(`jarvis_profile_${this.userProfile.id}`, JSON.stringify(this.userProfile))
    }
  }

  // Add message to conversation buffer
  addMessage(message: Message): void {
    this.conversationBuffer.push(message)
    
    // Keep only last 20 messages in buffer
    if (this.conversationBuffer.length > 20) {
      this.conversationBuffer = this.conversationBuffer.slice(-20)
    }

    // Learn from user messages
    if (message.role === 'user') {
      this.learnFromMessage(message.content)
    }
  }

  // Extract learning patterns from user messages
  private learnFromMessage(content: string): void {
    if (!this.userProfile) return

    const words = content.toLowerCase().split(/\s+/)
    const topics = this.extractTopics(content)
    const mood = this.detectMood(content)

    // Update topics of interest
    topics.forEach(topic => {
      if (!this.userProfile!.personalInfo.interests.includes(topic)) {
        this.userProfile!.personalInfo.interests.push(topic)
      }
    })

    // Update communication style based on patterns
    this.updateCommunicationStyle(content)
    
    this.userProfile.lastActive = new Date()
    this.saveProfile()
  }

  // Extract topics from message content
  private extractTopics(content: string): string[] {
    const topicKeywords = {
      'technology': ['tech', 'computer', 'software', 'programming', 'ai', 'code', 'development'],
      'science': ['science', 'research', 'experiment', 'theory', 'physics', 'chemistry', 'biology'],
      'business': ['business', 'work', 'job', 'career', 'company', 'meeting', 'project'],
      'entertainment': ['movie', 'music', 'game', 'book', 'show', 'entertainment', 'fun'],
      'health': ['health', 'fitness', 'exercise', 'diet', 'medical', 'wellness'],
      'travel': ['travel', 'trip', 'vacation', 'country', 'city', 'flight', 'hotel'],
      'food': ['food', 'recipe', 'cooking', 'restaurant', 'meal', 'cuisine'],
      'sports': ['sport', 'football', 'basketball', 'soccer', 'tennis', 'game', 'team']
    }

    const topics: string[] = []
    const lowerContent = content.toLowerCase()

    Object.entries(topicKeywords).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => lowerContent.includes(keyword))) {
        topics.push(topic)
      }
    })

    return topics
  }

  // Detect user mood from message
  private detectMood(content: string): string {
    const positiveWords = ['great', 'awesome', 'excellent', 'love', 'amazing', 'perfect', 'wonderful']
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'frustrated', 'annoying', 'problem']
    const excitedWords = ['excited', 'can\'t wait', '!', 'wow', 'incredible', 'fantastic']

    const lowerContent = content.toLowerCase()

    if (excitedWords.some(word => lowerContent.includes(word))) return 'excited'
    if (positiveWords.some(word => lowerContent.includes(word))) return 'positive'
    if (negativeWords.some(word => lowerContent.includes(word))) return 'frustrated'
    
    return 'neutral'
  }

  // Update communication style based on user patterns
  private updateCommunicationStyle(content: string): void {
    if (!this.userProfile) return

    const formalIndicators = ['please', 'thank you', 'could you', 'would you']
    const casualIndicators = ['hey', 'what\'s up', 'cool', 'awesome', 'yeah']
    const technicalIndicators = ['algorithm', 'function', 'variable', 'database', 'api']

    const lowerContent = content.toLowerCase()

    if (technicalIndicators.some(word => lowerContent.includes(word))) {
      this.userProfile.preferences.communicationStyle = 'technical'
    } else if (formalIndicators.some(word => lowerContent.includes(word))) {
      this.userProfile.preferences.communicationStyle = 'formal'
    } else if (casualIndicators.some(word => lowerContent.includes(word))) {
      this.userProfile.preferences.communicationStyle = 'casual'
    }
  }

  // Get memory context for AI responses
  getMemoryContext(): MemoryContext {
    if (!this.userProfile) {
      return {
        recentTopics: [],
        userPreferences: {
          communicationStyle: 'friendly',
          responseLength: 'detailed',
          topics: [],
          personalityMode: 'professional'
        },
        relevantHistory: [],
        personalContext: []
      }
    }

    const recentTopics = this.userProfile.personalInfo.interests.slice(-5)
    const relevantHistory = this.conversationBuffer
      .slice(-5)
      .map(msg => `${msg.role}: ${msg.content.substring(0, 100)}`)

    const personalContext = [
      this.userProfile.name ? `User's name: ${this.userProfile.name}` : '',
      this.userProfile.personalInfo.occupation ? `Occupation: ${this.userProfile.personalInfo.occupation}` : '',
      `Interests: ${this.userProfile.personalInfo.interests.join(', ')}`,
      `Communication style: ${this.userProfile.preferences.communicationStyle}`,
      `Preferred response length: ${this.userProfile.preferences.responseLength}`
    ].filter(Boolean)

    return {
      recentTopics,
      userPreferences: this.userProfile.preferences,
      relevantHistory,
      personalContext
    }
  }

  // Update user information
  updateUserInfo(updates: Partial<UserProfile>): void {
    if (!this.userProfile) return

    this.userProfile = { ...this.userProfile, ...updates }
    this.userProfile.lastActive = new Date()
    this.saveProfile()
  }

  // Get conversation summary
  getConversationSummary(): string {
    const context = this.getMemoryContext()
    
    return `
MEMORY CONTEXT:
- User interests: ${context.recentTopics.join(', ') || 'None recorded yet'}
- Communication style: ${context.userPreferences.communicationStyle}
- Preferred response length: ${context.userPreferences.responseLength}
- Personal context: ${context.personalContext.join('; ')}
- Recent conversation: ${context.relevantHistory.slice(-3).join('; ')}

Use this context to provide personalized, contextually aware responses.
    `.trim()
  }

  // Clear memory (for privacy)
  clearMemory(): void {
    if (this.userProfile) {
      localStorage.removeItem(`jarvis_profile_${this.userProfile.id}`)
    }
    this.userProfile = null
    this.conversationBuffer = []
  }
}

// Export singleton instance
export const jarvisMemory = new JarvisMemorySystem()
export default jarvisMemory
