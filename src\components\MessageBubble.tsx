'use client'

import { Message } from '@/types/chat'

interface MessageBubbleProps {
  message: Message
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} ${isUser ? 'message-slide-in-right' : 'message-slide-in-left'}`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg ${
          isUser
            ? 'bg-red-600 ml-3 holographic-glow'
            : 'bg-gradient-to-r from-red-600 to-yellow-500 mr-3 arc-reactor avatar-pulse'
        }`}>
          {isUser ? (
            <span className="text-white text-sm font-medium">U</span>
          ) : (
            <span className="text-white text-sm font-bold">J</span>
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          <div className={`rounded-2xl px-4 py-3 shadow-lg transition-all duration-300 ${
            isUser
              ? 'bg-gradient-to-r from-red-600 to-red-700 text-white border border-red-500/30 holographic-glow'
              : 'bg-grok-gray border border-red-500/20 text-grok-text glowing-border hover:border-red-500/40'
          }`}>
            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>
            
            {/* Metadata for assistant messages */}
            {isAssistant && message.metadata && (
              <div className="mt-2 pt-2 border-t border-red-500/20 text-xs text-grok-muted font-mono">
                {message.metadata.sources && (
                  <div className="mb-1 text-red-400">
                    SOURCES: {message.metadata.sources.join(', ').toUpperCase()}
                  </div>
                )}
                {message.metadata.confidence && (
                  <div className="text-yellow-400">
                    CONFIDENCE: {Math.round(message.metadata.confidence * 100)}%
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Timestamp */}
          <div className={`text-xs text-grok-muted mt-1 font-mono tracking-wider ${isUser ? 'text-right' : 'text-left'}`}>
            {formatTimeAgo(message.timestamp).toUpperCase()}
          </div>
        </div>
      </div>
    </div>
  )
}

// Utility function for date formatting
function formatTimeAgo(date: Date): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return 'just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}
