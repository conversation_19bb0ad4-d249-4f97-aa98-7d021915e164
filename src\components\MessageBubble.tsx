'use client'

import { Message } from '@/types/chat'
import { formatDistanceToNow } from 'date-fns'

interface MessageBubbleProps {
  message: Message
}

export default function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} message-fade-in`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser ? 'bg-grok-blue ml-3' : 'bg-gradient-to-r from-grok-green to-grok-blue mr-3'
        }`}>
          {isUser ? (
            <span className="text-white text-sm font-medium">U</span>
          ) : (
            <span className="text-white text-sm font-bold">G</span>
          )}
        </div>

        {/* Message Content */}
        <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
          <div className={`rounded-2xl px-4 py-3 ${
            isUser 
              ? 'bg-grok-blue text-white' 
              : 'bg-grok-gray border border-grok-muted/20 text-grok-text'
          }`}>
            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>
            
            {/* Metadata for assistant messages */}
            {isAssistant && message.metadata && (
              <div className="mt-2 pt-2 border-t border-grok-muted/20 text-xs text-grok-muted">
                {message.metadata.sources && (
                  <div className="mb-1">
                    Sources: {message.metadata.sources.join(', ')}
                  </div>
                )}
                {message.metadata.confidence && (
                  <div>
                    Confidence: {Math.round(message.metadata.confidence * 100)}%
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Timestamp */}
          <div className={`text-xs text-grok-muted mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
            {formatDistanceToNow(message.timestamp, { addSuffix: true })}
          </div>
        </div>
      </div>
    </div>
  )
}

// Utility function for date formatting (since date-fns might not be installed)
function formatDistanceToNow(date: Date, options?: { addSuffix?: boolean }): string {
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
  
  if (diffInSeconds < 60) return options?.addSuffix ? 'just now' : 'now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ${options?.addSuffix ? 'ago' : ''}`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ${options?.addSuffix ? 'ago' : ''}`
  return `${Math.floor(diffInSeconds / 86400)}d ${options?.addSuffix ? 'ago' : ''}`
}
