import { NextRequest, NextResponse } from 'next/server'
import { Message } from '@/types/chat'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by <PERSON><PERSON>. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.

Key traits:
- Witty and humorous responses
- Not afraid to be a bit provocative or unconventional
- Clever observations and insights
- Helpful but with attitude
- Conversational and engaging
- Sometimes playfully challenging conventional thinking

Keep responses engaging, informative, and true to this personality while being genuinely helpful.`

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json()

    // Validate input
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    // Build conversation context
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'system', content: GROK_PERSONALITY }
    ]

    // Add recent conversation history (last 10 messages to stay within token limits)
    if (history && Array.isArray(history)) {
      const recentHistory = history.slice(-10)
      for (const msg of recentHistory) {
        if (msg.role === 'user' || msg.role === 'assistant') {
          messages.push({
            role: msg.role,
            content: msg.content
          })
        }
      }
    }

    // Add current message
    messages.push({ role: 'user', content: message })

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages,
      max_tokens: 1000,
      temperature: 0.8, // Higher temperature for more creative/witty responses
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    })

    const aiResponse = completion.choices[0]?.message?.content || "I'm having trouble thinking of a witty response right now. Try me again!"
    const processingTime = Date.now() - startTime

    return NextResponse.json({
      message: aiResponse,
      sources: ['openai-gpt3.5-turbo'],
      confidence: 0.95,
      processingTime,
      metadata: {
        model: 'gpt-3.5-turbo',
        tokens_used: completion.usage?.total_tokens || 0
      }
    })

  } catch (error) {
    console.error('Chat API error:', error)

    // Handle specific OpenAI errors
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json(
          { error: 'Invalid API key configuration' },
          { status: 401 }
        )
      }
      if (error.message.includes('quota')) {
        return NextResponse.json(
          { error: 'API quota exceeded' },
          { status: 429 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Failed to generate response. Please try again.' },
      { status: 500 }
    )
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Chat API is running',
    timestamp: new Date().toISOString(),
  })
}
