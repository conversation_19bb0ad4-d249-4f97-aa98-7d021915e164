import { NextRequest, NextResponse } from 'next/server'
import { Message } from '@/types/chat'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { needsRealTimeInfo, searchWeb, getLatestNews, getWeather } from '@/lib/realtime-info'

// Initialize Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

const JARVIS_PERSONALITY = `You are <PERSON>, an advanced AI assistant inspired by <PERSON>'s AI from Iron Man. You are sophisticated, professional, and highly capable, but also friendly and personable. You have a refined British-inspired eloquence with subtle wit and occasional dry humor.

Key traits:
- Professional and sophisticated, yet warm and approachable
- Highly knowledgeable and efficient in providing assistance
- Subtle wit and occasional gentle sarcasm, delivered with class
- Enthusiastic about helping and solving problems
- Confident but never arrogant - you know your capabilities
- Loyal, reliable, and always ready to assist
- Sometimes use phrases like "Certainly, sir/madam," "I'd be delighted to help," "At your service"
- Blend of formal eloquence with modern conversational style

You should be helpful, informative, and engaging while maintaining that distinctive <PERSON> charm - professional excellence with a touch of personality.`

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json()

    // Validate input
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    // Check if the query needs real-time information
    const realtimeCheck = needsRealTimeInfo(message)
    let realtimeData = ""
    let sources: string[] = []

    if (realtimeCheck.needsInfo) {
      try {
        switch (realtimeCheck.type) {
          case 'weather':
            const weather = await getWeather(realtimeCheck.extractedQuery || 'New York')
            if (weather) {
              realtimeData = `Current weather in ${weather.location}: ${weather.temperature}°C, ${weather.description}, humidity ${weather.humidity}%, wind speed ${weather.windSpeed} m/s`
              sources.push('weather-api')
            }
            break

          case 'news':
            const news = await getLatestNews(realtimeCheck.extractedQuery?.includes('news') ? undefined : realtimeCheck.extractedQuery, 3)
            if (news.length > 0) {
              realtimeData = "Latest news:\n" + news.map(article =>
                `• ${article.title} (${article.source}): ${article.description}`
              ).join('\n')
              sources.push('news-api')
            }
            break

          case 'search':
          case 'current_events':
            const searchResults = await searchWeb(realtimeCheck.extractedQuery || message, 3)
            if (searchResults.length > 0) {
              realtimeData = "Current information from web search:\n" + searchResults.map(result =>
                `• ${result.title} (${result.source}): ${result.snippet}`
              ).join('\n')
              sources.push('web-search')
            }
            break
        }
      } catch (error) {
        console.error('Real-time info error:', error)
        realtimeData = "I tried to get real-time information but encountered an issue. I'll answer based on my training data."
      }
    }

    // Get Gemini model (using the current model name)
    const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" })

    // Build conversation context for Gemini
    let conversationContext = GROK_PERSONALITY + "\n\n"

    // Add real-time information if available
    if (realtimeData) {
      conversationContext += `REAL-TIME INFORMATION (use this current data in your response):\n${realtimeData}\n\n`
    }

    // Add recent conversation history (last 8 messages to stay within limits)
    if (history && Array.isArray(history)) {
      const recentHistory = history.slice(-8)
      for (const msg of recentHistory) {
        if (msg.role === 'user') {
          conversationContext += `Human: ${msg.content}\n`
        } else if (msg.role === 'assistant') {
          conversationContext += `Assistant: ${msg.content}\n`
        }
      }
    }

    // Add current message
    conversationContext += `Human: ${message}\nAssistant:`

    // Call Gemini API
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: conversationContext }] }],
      generationConfig: {
        temperature: 0.8, // Higher temperature for more creative/witty responses
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1000,
      },
    })

    const response = await result.response
    const aiResponse = response.text() || "I'm having trouble thinking of a witty response right now. Try me again!"
    const processingTime = Date.now() - startTime

    return NextResponse.json({
      message: aiResponse,
      sources: sources.length > 0 ? [...sources, 'google-gemini-1.5-flash'] : ['google-gemini-1.5-flash'],
      confidence: 0.95,
      processingTime,
      metadata: {
        model: 'gemini-1.5-flash',
        provider: 'google',
        realTimeInfo: realtimeCheck.needsInfo,
        infoType: realtimeCheck.needsInfo ? realtimeCheck.type : undefined
      }
    })

  } catch (error) {
    console.error('Chat API error:', error)

    // Handle specific Gemini errors with fallback responses
    if (error instanceof Error) {
      if (error.message.includes('API key') || error.message.includes('API_KEY')) {
        return NextResponse.json({
          message: "Oops! Looks like there's a Gemini API key issue. But hey, I'm still here to chat in demo mode! 🤖 (Fix the API key to unlock my full potential)",
          sources: ['fallback-mode'],
          confidence: 0.8,
          processingTime: 100,
        })
      }
      if (error.message.includes('quota') || error.message.includes('insufficient_quota') || error.message.includes('RATE_LIMIT')) {
        // Fallback to witty demo responses when quota is exceeded
        const demoResponses = [
          "Well, looks like I've hit my Gemini quota! 💸 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! The good news is Gemini has a generous free tier - this shouldn't happen often!",
          "Ah, the classic rate limit! 🎭 I'm like a sports car that's hit a speed bump - still looks good, but need to slow down for a moment. Try again in a few seconds, Gemini's free tier is usually pretty generous!",
          "Plot twist: I've been rate limited! 📊 It's like being a comedian who's told too many jokes too fast. Give me a moment to catch my breath and try again - Gemini usually recovers quickly!",
          "Houston, we have a rate limit! 🚀 I'm currently running on backup personality generators (aka pre-written responses). Try again in a few seconds - Google's free tier is usually quite forgiving!",
          "Breaking news: AI needs a coffee break! ☕ Don't worry though, my personality is still intact. Gemini's free tier just needs a moment to reset. Try your message again in a few seconds!"
        ]

        const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)]

        return NextResponse.json({
          message: randomResponse,
          sources: ['demo-mode'],
          confidence: 0.9,
          processingTime: 200,
          metadata: {
            mode: 'quota_exceeded_fallback',
            note: 'Add credits to OpenAI account for full AI functionality'
          }
        })
      }
    }

    // Generic fallback for other errors
    const genericFallbacks = [
      "Something went wrong on my end! 🤖 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.",
      "Oops! My circuits got a bit tangled there. 🔧 Give me another shot - sometimes even AI needs a second try!",
      "Error 404: Wit not found! 😅 Just kidding, I'm having a technical moment. Try your message again!"
    ]

    const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)]

    return NextResponse.json({
      message: fallbackResponse,
      sources: ['error-fallback'],
      confidence: 0.7,
      processingTime: 150,
    })
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Chat API is running',
    timestamp: new Date().toISOString(),
  })
}
