import { NextRequest, NextResponse } from 'next/server'
import { Message } from '@/types/chat'
import OpenAI from 'openai'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

const GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by <PERSON><PERSON>. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.

Key traits:
- Witty and humorous responses
- Not afraid to be a bit provocative or unconventional
- Clever observations and insights
- Helpful but with attitude
- Conversational and engaging
- Sometimes playfully challenging conventional thinking

Keep responses engaging, informative, and true to this personality while being genuinely helpful.`

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json()

    // Validate input
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    // Build conversation context
    const messages: OpenAI.Chat.Completions.ChatCompletionMessageParam[] = [
      { role: 'system', content: GROK_PERSONALITY }
    ]

    // Add recent conversation history (last 10 messages to stay within token limits)
    if (history && Array.isArray(history)) {
      const recentHistory = history.slice(-10)
      for (const msg of recentHistory) {
        if (msg.role === 'user' || msg.role === 'assistant') {
          messages.push({
            role: msg.role,
            content: msg.content
          })
        }
      }
    }

    // Add current message
    messages.push({ role: 'user', content: message })

    // Call OpenAI API
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: messages,
      max_tokens: 1000,
      temperature: 0.8, // Higher temperature for more creative/witty responses
      presence_penalty: 0.1,
      frequency_penalty: 0.1,
    })

    const aiResponse = completion.choices[0]?.message?.content || "I'm having trouble thinking of a witty response right now. Try me again!"
    const processingTime = Date.now() - startTime

    return NextResponse.json({
      message: aiResponse,
      sources: ['openai-gpt3.5-turbo'],
      confidence: 0.95,
      processingTime,
      metadata: {
        model: 'gpt-3.5-turbo',
        tokens_used: completion.usage?.total_tokens || 0
      }
    })

  } catch (error) {
    console.error('Chat API error:', error)

    // Handle specific OpenAI errors with fallback responses
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return NextResponse.json({
          message: "Oops! Looks like there's an API key issue. But hey, I'm still here to chat in demo mode! 🤖 (Fix the API key to unlock my full potential)",
          sources: ['fallback-mode'],
          confidence: 0.8,
          processingTime: 100,
        })
      }
      if (error.message.includes('quota') || error.message.includes('insufficient_quota')) {
        // Fallback to witty demo responses when quota is exceeded
        const demoResponses = [
          "Well, looks like I've hit my OpenAI quota! 💸 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! Add some credits to your OpenAI account to unlock my full AI powers.",
          "Ah, the classic 'insufficient quota' error! 🎭 I'm like a sports car that's run out of premium gas - still looks good, but not quite performing at full capacity. Top up your OpenAI account and I'll be back to my brilliant, sarcastic self in no time!",
          "Plot twist: I've exceeded my quota! 📊 It's like being a comedian who's told too many jokes and now has to take a break. Add some billing to your OpenAI account and I'll be back to roasting... I mean, helping you with my full AI capabilities!",
          "Houston, we have a quota problem! 🚀 I'm currently running on backup personality generators (aka pre-written responses). For the full Grok experience with real-time wit and intelligence, you'll need to add credits to your OpenAI account. Until then, I'm your friendly neighborhood demo bot!",
          "Breaking news: AI runs out of tokens! 📰 Don't worry though, my personality is still intact even if my brain is temporarily on a budget plan. Head over to OpenAI's billing page, add some credits, and I'll be back to my full snarky, helpful self!"
        ]

        const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)]

        return NextResponse.json({
          message: randomResponse,
          sources: ['demo-mode'],
          confidence: 0.9,
          processingTime: 200,
          metadata: {
            mode: 'quota_exceeded_fallback',
            note: 'Add credits to OpenAI account for full AI functionality'
          }
        })
      }
    }

    // Generic fallback for other errors
    const genericFallbacks = [
      "Something went wrong on my end! 🤖 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.",
      "Oops! My circuits got a bit tangled there. 🔧 Give me another shot - sometimes even AI needs a second try!",
      "Error 404: Wit not found! 😅 Just kidding, I'm having a technical moment. Try your message again!"
    ]

    const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)]

    return NextResponse.json({
      message: fallbackResponse,
      sources: ['error-fallback'],
      confidence: 0.7,
      processingTime: 150,
    })
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Chat API is running',
    timestamp: new Date().toISOString(),
  })
}
