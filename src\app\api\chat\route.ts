import { NextRequest, NextResponse } from 'next/server'
import { Message } from '@/types/chat'
import { GoogleGenerativeAI } from '@google/generative-ai'

// Initialize Gemini
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

const GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by <PERSON><PERSON>. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.

Key traits:
- Witty and humorous responses
- Not afraid to be a bit provocative or unconventional
- Clever observations and insights
- Helpful but with attitude
- Conversational and engaging
- Sometimes playfully challenging conventional thinking

Keep responses engaging, informative, and true to this personality while being genuinely helpful.`

export async function POST(request: NextRequest) {
  try {
    const { message, history } = await request.json()

    // Validate input
    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    const startTime = Date.now()

    // Get Gemini model
    const model = genAI.getGenerativeModel({ model: "gemini-pro" })

    // Build conversation context for Gemini
    let conversationContext = GROK_PERSONALITY + "\n\n"

    // Add recent conversation history (last 8 messages to stay within limits)
    if (history && Array.isArray(history)) {
      const recentHistory = history.slice(-8)
      for (const msg of recentHistory) {
        if (msg.role === 'user') {
          conversationContext += `Human: ${msg.content}\n`
        } else if (msg.role === 'assistant') {
          conversationContext += `Assistant: ${msg.content}\n`
        }
      }
    }

    // Add current message
    conversationContext += `Human: ${message}\nAssistant:`

    // Call Gemini API
    const result = await model.generateContent({
      contents: [{ role: 'user', parts: [{ text: conversationContext }] }],
      generationConfig: {
        temperature: 0.8, // Higher temperature for more creative/witty responses
        topK: 40,
        topP: 0.95,
        maxOutputTokens: 1000,
      },
    })

    const response = await result.response
    const aiResponse = response.text() || "I'm having trouble thinking of a witty response right now. Try me again!"
    const processingTime = Date.now() - startTime

    return NextResponse.json({
      message: aiResponse,
      sources: ['google-gemini-pro'],
      confidence: 0.95,
      processingTime,
      metadata: {
        model: 'gemini-pro',
        provider: 'google'
      }
    })

  } catch (error) {
    console.error('Chat API error:', error)

    // Handle specific Gemini errors with fallback responses
    if (error instanceof Error) {
      if (error.message.includes('API key') || error.message.includes('API_KEY')) {
        return NextResponse.json({
          message: "Oops! Looks like there's a Gemini API key issue. But hey, I'm still here to chat in demo mode! 🤖 (Fix the API key to unlock my full potential)",
          sources: ['fallback-mode'],
          confidence: 0.8,
          processingTime: 100,
        })
      }
      if (error.message.includes('quota') || error.message.includes('insufficient_quota') || error.message.includes('RATE_LIMIT')) {
        // Fallback to witty demo responses when quota is exceeded
        const demoResponses = [
          "Well, looks like I've hit my Gemini quota! 💸 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! The good news is Gemini has a generous free tier - this shouldn't happen often!",
          "Ah, the classic rate limit! 🎭 I'm like a sports car that's hit a speed bump - still looks good, but need to slow down for a moment. Try again in a few seconds, Gemini's free tier is usually pretty generous!",
          "Plot twist: I've been rate limited! 📊 It's like being a comedian who's told too many jokes too fast. Give me a moment to catch my breath and try again - Gemini usually recovers quickly!",
          "Houston, we have a rate limit! 🚀 I'm currently running on backup personality generators (aka pre-written responses). Try again in a few seconds - Google's free tier is usually quite forgiving!",
          "Breaking news: AI needs a coffee break! ☕ Don't worry though, my personality is still intact. Gemini's free tier just needs a moment to reset. Try your message again in a few seconds!"
        ]

        const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)]

        return NextResponse.json({
          message: randomResponse,
          sources: ['demo-mode'],
          confidence: 0.9,
          processingTime: 200,
          metadata: {
            mode: 'quota_exceeded_fallback',
            note: 'Add credits to OpenAI account for full AI functionality'
          }
        })
      }
    }

    // Generic fallback for other errors
    const genericFallbacks = [
      "Something went wrong on my end! 🤖 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.",
      "Oops! My circuits got a bit tangled there. 🔧 Give me another shot - sometimes even AI needs a second try!",
      "Error 404: Wit not found! 😅 Just kidding, I'm having a technical moment. Try your message again!"
    ]

    const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)]

    return NextResponse.json({
      message: fallbackResponse,
      sources: ['error-fallback'],
      confidence: 0.7,
      processingTime: 150,
    })
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    message: 'Chat API is running',
    timestamp: new Date().toISOString(),
  })
}
