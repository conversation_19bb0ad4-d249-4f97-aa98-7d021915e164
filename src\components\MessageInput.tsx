'use client'

import { useState } from 'react'

interface MessageInputProps {
  value: string
  onChange: (value: string) => void
  onSend: () => void
  onKeyPress: (e: React.KeyboardEvent) => void
  disabled: boolean
  placeholder?: string
}

export default function MessageInput({ 
  value, 
  onChange, 
  onSend, 
  onKeyPress, 
  disabled, 
  placeholder = "Type your message..." 
}: MessageInputProps) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <div className={`relative flex items-end space-x-3 p-3 rounded-2xl border transition-all duration-200 ${
      isFocused 
        ? 'border-grok-blue bg-grok-dark/50' 
        : 'border-grok-muted/20 bg-grok-gray/30'
    }`}>
      {/* Text Input */}
      <div className="flex-1">
        <textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={onKeyPress}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          disabled={disabled}
          placeholder={placeholder}
          rows={1}
          className="w-full bg-transparent text-grok-text placeholder-grok-muted resize-none outline-none min-h-[24px] max-h-32 overflow-y-auto"
          style={{
            height: 'auto',
            minHeight: '24px',
          }}
          onInput={(e) => {
            const target = e.target as HTMLTextAreaElement
            target.style.height = 'auto'
            target.style.height = `${Math.min(target.scrollHeight, 128)}px`
          }}
        />
      </div>

      {/* Send Button */}
      <button
        onClick={onSend}
        disabled={disabled || !value.trim()}
        className={`flex-shrink-0 p-2 rounded-xl transition-all duration-200 ${
          disabled || !value.trim()
            ? 'bg-grok-muted/20 text-grok-muted cursor-not-allowed'
            : 'bg-grok-blue text-white hover:bg-grok-blue/80 active:scale-95'
        }`}
      >
        {disabled ? (
          <div className="w-5 h-5 border-2 border-grok-muted border-t-transparent rounded-full animate-spin" />
        ) : (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
          </svg>
        )}
      </button>

      {/* Character count (optional) */}
      {value.length > 500 && (
        <div className="absolute -top-6 right-0 text-xs text-grok-muted">
          {value.length}/1000
        </div>
      )}
    </div>
  )
}
