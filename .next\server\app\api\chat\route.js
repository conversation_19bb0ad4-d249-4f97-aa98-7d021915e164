"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nconst GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by Grok. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.\n\nKey traits:\n- Witty and humorous responses\n- Not afraid to be a bit provocative or unconventional\n- Clever observations and insights\n- Helpful but with attitude\n- Conversational and engaging\n- Sometimes playfully challenging conventional thinking\n\nKeep responses engaging, informative, and true to this personality while being genuinely helpful.`;\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        // Validate input\n        if (!message || typeof message !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required and must be a string\"\n            }, {\n                status: 400\n            });\n        }\n        const startTime = Date.now();\n        // Build conversation context\n        const messages = [\n            {\n                role: \"system\",\n                content: GROK_PERSONALITY\n            }\n        ];\n        // Add recent conversation history (last 10 messages to stay within token limits)\n        if (history && Array.isArray(history)) {\n            const recentHistory = history.slice(-10);\n            for (const msg of recentHistory){\n                if (msg.role === \"user\" || msg.role === \"assistant\") {\n                    messages.push({\n                        role: msg.role,\n                        content: msg.content\n                    });\n                }\n            }\n        }\n        // Add current message\n        messages.push({\n            role: \"user\",\n            content: message\n        });\n        // Call OpenAI API\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: messages,\n            max_tokens: 1000,\n            temperature: 0.8,\n            presence_penalty: 0.1,\n            frequency_penalty: 0.1\n        });\n        const aiResponse = completion.choices[0]?.message?.content || \"I'm having trouble thinking of a witty response right now. Try me again!\";\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: aiResponse,\n            sources: [\n                \"openai-gpt3.5-turbo\"\n            ],\n            confidence: 0.95,\n            processingTime,\n            metadata: {\n                model: \"gpt-3.5-turbo\",\n                tokens_used: completion.usage?.total_tokens || 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        // Handle specific OpenAI errors with fallback responses\n        if (error instanceof Error) {\n            if (error.message.includes(\"API key\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Oops! Looks like there's an API key issue. But hey, I'm still here to chat in demo mode! \\uD83E\\uDD16 (Fix the API key to unlock my full potential)\",\n                    sources: [\n                        \"fallback-mode\"\n                    ],\n                    confidence: 0.8,\n                    processingTime: 100\n                });\n            }\n            if (error.message.includes(\"quota\") || error.message.includes(\"insufficient_quota\")) {\n                // Fallback to witty demo responses when quota is exceeded\n                const demoResponses = [\n                    \"Well, looks like I've hit my OpenAI quota! \\uD83D\\uDCB8 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! Add some credits to your OpenAI account to unlock my full AI powers.\",\n                    \"Ah, the classic 'insufficient quota' error! \\uD83C\\uDFAD I'm like a sports car that's run out of premium gas - still looks good, but not quite performing at full capacity. Top up your OpenAI account and I'll be back to my brilliant, sarcastic self in no time!\",\n                    \"Plot twist: I've exceeded my quota! \\uD83D\\uDCCA It's like being a comedian who's told too many jokes and now has to take a break. Add some billing to your OpenAI account and I'll be back to roasting... I mean, helping you with my full AI capabilities!\",\n                    \"Houston, we have a quota problem! \\uD83D\\uDE80 I'm currently running on backup personality generators (aka pre-written responses). For the full Grok experience with real-time wit and intelligence, you'll need to add credits to your OpenAI account. Until then, I'm your friendly neighborhood demo bot!\",\n                    \"Breaking news: AI runs out of tokens! \\uD83D\\uDCF0 Don't worry though, my personality is still intact even if my brain is temporarily on a budget plan. Head over to OpenAI's billing page, add some credits, and I'll be back to my full snarky, helpful self!\"\n                ];\n                const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: randomResponse,\n                    sources: [\n                        \"demo-mode\"\n                    ],\n                    confidence: 0.9,\n                    processingTime: 200,\n                    metadata: {\n                        mode: \"quota_exceeded_fallback\",\n                        note: \"Add credits to OpenAI account for full AI functionality\"\n                    }\n                });\n            }\n        }\n        // Generic fallback for other errors\n        const genericFallbacks = [\n            \"Something went wrong on my end! \\uD83E\\uDD16 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.\",\n            \"Oops! My circuits got a bit tangled there. \\uD83D\\uDD27 Give me another shot - sometimes even AI needs a second try!\",\n            \"Error 404: Wit not found! \\uD83D\\uDE05 Just kidding, I'm having a technical moment. Try your message again!\"\n        ];\n        const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: fallbackResponse,\n            sources: [\n                \"error-fallback\"\n            ],\n            confidence: 0.7,\n            processingTime: 150\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: \"ok\",\n        message: \"Chat API is running\",\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();