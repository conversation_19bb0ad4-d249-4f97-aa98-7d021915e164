"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n\nconst openai = new openai__WEBPACK_IMPORTED_MODULE_1__[\"default\"]({\n    apiKey: \"********************************************************************************************************************************************************************\"\n});\nconst GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by Grok. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.\n\nKey traits:\n- Witty and humorous responses\n- Not afraid to be a bit provocative or unconventional\n- Clever observations and insights\n- Helpful but with attitude\n- Conversational and engaging\n- Sometimes playfully challenging conventional thinking\n\nKeep responses engaging, informative, and true to this personality while being genuinely helpful.`;\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        // Validate input\n        if (!message || typeof message !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required and must be a string\"\n            }, {\n                status: 400\n            });\n        }\n        const startTime = Date.now();\n        // Build conversation context\n        const messages = [\n            {\n                role: \"system\",\n                content: GROK_PERSONALITY\n            }\n        ];\n        // Add recent conversation history (last 10 messages to stay within token limits)\n        if (history && Array.isArray(history)) {\n            const recentHistory = history.slice(-10);\n            for (const msg of recentHistory){\n                if (msg.role === \"user\" || msg.role === \"assistant\") {\n                    messages.push({\n                        role: msg.role,\n                        content: msg.content\n                    });\n                }\n            }\n        }\n        // Add current message\n        messages.push({\n            role: \"user\",\n            content: message\n        });\n        // Call OpenAI API\n        const completion = await openai.chat.completions.create({\n            model: \"gpt-3.5-turbo\",\n            messages: messages,\n            max_tokens: 1000,\n            temperature: 0.8,\n            presence_penalty: 0.1,\n            frequency_penalty: 0.1\n        });\n        const aiResponse = completion.choices[0]?.message?.content || \"I'm having trouble thinking of a witty response right now. Try me again!\";\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: aiResponse,\n            sources: [\n                \"openai-gpt3.5-turbo\"\n            ],\n            confidence: 0.95,\n            processingTime,\n            metadata: {\n                model: \"gpt-3.5-turbo\",\n                tokens_used: completion.usage?.total_tokens || 0\n            }\n        });\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        // Handle specific OpenAI errors\n        if (error instanceof Error) {\n            if (error.message.includes(\"API key\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid API key configuration\"\n                }, {\n                    status: 401\n                });\n            }\n            if (error.message.includes(\"quota\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"API quota exceeded\"\n                }, {\n                    status: 429\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to generate response. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: \"ok\",\n        message: \"Chat API is running\",\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/formdata-node","vendor-chunks/openai","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();