"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(\"AIzaSyBlPdNTBNqJZbMttwWMc0wgzFYNHZJ2vHc\" || 0);\nconst GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by Grok. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.\n\nKey traits:\n- Witty and humorous responses\n- Not afraid to be a bit provocative or unconventional\n- Clever observations and insights\n- Helpful but with attitude\n- Conversational and engaging\n- Sometimes playfully challenging conventional thinking\n\nKeep responses engaging, informative, and true to this personality while being genuinely helpful.`;\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        // Validate input\n        if (!message || typeof message !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required and must be a string\"\n            }, {\n                status: 400\n            });\n        }\n        const startTime = Date.now();\n        // Get Gemini model\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-pro\"\n        });\n        // Build conversation context for Gemini\n        let conversationContext = GROK_PERSONALITY + \"\\n\\n\";\n        // Add recent conversation history (last 8 messages to stay within limits)\n        if (history && Array.isArray(history)) {\n            const recentHistory = history.slice(-8);\n            for (const msg of recentHistory){\n                if (msg.role === \"user\") {\n                    conversationContext += `Human: ${msg.content}\\n`;\n                } else if (msg.role === \"assistant\") {\n                    conversationContext += `Assistant: ${msg.content}\\n`;\n                }\n            }\n        }\n        // Add current message\n        conversationContext += `Human: ${message}\\nAssistant:`;\n        // Call Gemini API\n        const result = await model.generateContent({\n            contents: [\n                {\n                    role: \"user\",\n                    parts: [\n                        {\n                            text: conversationContext\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.8,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 1000\n            }\n        });\n        const response = await result.response;\n        const aiResponse = response.text() || \"I'm having trouble thinking of a witty response right now. Try me again!\";\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: aiResponse,\n            sources: [\n                \"google-gemini-pro\"\n            ],\n            confidence: 0.95,\n            processingTime,\n            metadata: {\n                model: \"gemini-pro\",\n                provider: \"google\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        // Handle specific Gemini errors with fallback responses\n        if (error instanceof Error) {\n            if (error.message.includes(\"API key\") || error.message.includes(\"API_KEY\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Oops! Looks like there's a Gemini API key issue. But hey, I'm still here to chat in demo mode! \\uD83E\\uDD16 (Fix the API key to unlock my full potential)\",\n                    sources: [\n                        \"fallback-mode\"\n                    ],\n                    confidence: 0.8,\n                    processingTime: 100\n                });\n            }\n            if (error.message.includes(\"quota\") || error.message.includes(\"insufficient_quota\") || error.message.includes(\"RATE_LIMIT\")) {\n                // Fallback to witty demo responses when quota is exceeded\n                const demoResponses = [\n                    \"Well, looks like I've hit my Gemini quota! \\uD83D\\uDCB8 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! The good news is Gemini has a generous free tier - this shouldn't happen often!\",\n                    \"Ah, the classic rate limit! \\uD83C\\uDFAD I'm like a sports car that's hit a speed bump - still looks good, but need to slow down for a moment. Try again in a few seconds, Gemini's free tier is usually pretty generous!\",\n                    \"Plot twist: I've been rate limited! \\uD83D\\uDCCA It's like being a comedian who's told too many jokes too fast. Give me a moment to catch my breath and try again - Gemini usually recovers quickly!\",\n                    \"Houston, we have a rate limit! \\uD83D\\uDE80 I'm currently running on backup personality generators (aka pre-written responses). Try again in a few seconds - Google's free tier is usually quite forgiving!\",\n                    \"Breaking news: AI needs a coffee break! ☕ Don't worry though, my personality is still intact. Gemini's free tier just needs a moment to reset. Try your message again in a few seconds!\"\n                ];\n                const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: randomResponse,\n                    sources: [\n                        \"demo-mode\"\n                    ],\n                    confidence: 0.9,\n                    processingTime: 200,\n                    metadata: {\n                        mode: \"quota_exceeded_fallback\",\n                        note: \"Add credits to OpenAI account for full AI functionality\"\n                    }\n                });\n            }\n        }\n        // Generic fallback for other errors\n        const genericFallbacks = [\n            \"Something went wrong on my end! \\uD83E\\uDD16 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.\",\n            \"Oops! My circuits got a bit tangled there. \\uD83D\\uDD27 Give me another shot - sometimes even AI needs a second try!\",\n            \"Error 404: Wit not found! \\uD83D\\uDE05 Just kidding, I'm having a technical moment. Try your message again!\"\n        ];\n        const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: fallbackResponse,\n            sources: [\n                \"error-fallback\"\n            ],\n            confidence: 0.7,\n            processingTime: 150\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: \"ok\",\n        message: \"Chat API is running\",\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();