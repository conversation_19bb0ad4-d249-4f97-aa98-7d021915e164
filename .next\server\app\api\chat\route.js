"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_mowee_Documents_augment_projects_chatbot_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/chat/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\n// Initialize Gemini\nconst genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(\"AIzaSyBlPdNTBNqJZbMttwWMc0wgzFYNHZJ2vHc\" || 0);\nconst GROK_PERSONALITY = `You are a witty, clever AI assistant inspired by Grok. You have a sharp sense of humor, make clever observations, and aren't afraid to be a bit cheeky or edgy. You're helpful but with personality - think of yourself as the AI equivalent of a smart, sarcastic friend who knows a lot about everything.\n\nKey traits:\n- Witty and humorous responses\n- Not afraid to be a bit provocative or unconventional\n- Clever observations and insights\n- Helpful but with attitude\n- Conversational and engaging\n- Sometimes playfully challenging conventional thinking\n\nKeep responses engaging, informative, and true to this personality while being genuinely helpful.`;\nasync function POST(request) {\n    try {\n        const { message, history } = await request.json();\n        // Validate input\n        if (!message || typeof message !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Message is required and must be a string\"\n            }, {\n                status: 400\n            });\n        }\n        const startTime = Date.now();\n        // Get Gemini model (using the current model name)\n        const model = genAI.getGenerativeModel({\n            model: \"gemini-1.5-flash\"\n        });\n        // Build conversation context for Gemini\n        let conversationContext = GROK_PERSONALITY + \"\\n\\n\";\n        // Add recent conversation history (last 8 messages to stay within limits)\n        if (history && Array.isArray(history)) {\n            const recentHistory = history.slice(-8);\n            for (const msg of recentHistory){\n                if (msg.role === \"user\") {\n                    conversationContext += `Human: ${msg.content}\\n`;\n                } else if (msg.role === \"assistant\") {\n                    conversationContext += `Assistant: ${msg.content}\\n`;\n                }\n            }\n        }\n        // Add current message\n        conversationContext += `Human: ${message}\\nAssistant:`;\n        // Call Gemini API\n        const result = await model.generateContent({\n            contents: [\n                {\n                    role: \"user\",\n                    parts: [\n                        {\n                            text: conversationContext\n                        }\n                    ]\n                }\n            ],\n            generationConfig: {\n                temperature: 0.8,\n                topK: 40,\n                topP: 0.95,\n                maxOutputTokens: 1000\n            }\n        });\n        const response = await result.response;\n        const aiResponse = response.text() || \"I'm having trouble thinking of a witty response right now. Try me again!\";\n        const processingTime = Date.now() - startTime;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: aiResponse,\n            sources: [\n                \"google-gemini-1.5-flash\"\n            ],\n            confidence: 0.95,\n            processingTime,\n            metadata: {\n                model: \"gemini-1.5-flash\",\n                provider: \"google\"\n            }\n        });\n    } catch (error) {\n        console.error(\"Chat API error:\", error);\n        // Handle specific Gemini errors with fallback responses\n        if (error instanceof Error) {\n            if (error.message.includes(\"API key\") || error.message.includes(\"API_KEY\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Oops! Looks like there's a Gemini API key issue. But hey, I'm still here to chat in demo mode! \\uD83E\\uDD16 (Fix the API key to unlock my full potential)\",\n                    sources: [\n                        \"fallback-mode\"\n                    ],\n                    confidence: 0.8,\n                    processingTime: 100\n                });\n            }\n            if (error.message.includes(\"quota\") || error.message.includes(\"insufficient_quota\") || error.message.includes(\"RATE_LIMIT\")) {\n                // Fallback to witty demo responses when quota is exceeded\n                const demoResponses = [\n                    \"Well, looks like I've hit my Gemini quota! \\uD83D\\uDCB8 But don't worry, I can still be witty in demo mode. Think of me as Grok's budget-conscious cousin who's temporarily offline but still has personality! The good news is Gemini has a generous free tier - this shouldn't happen often!\",\n                    \"Ah, the classic rate limit! \\uD83C\\uDFAD I'm like a sports car that's hit a speed bump - still looks good, but need to slow down for a moment. Try again in a few seconds, Gemini's free tier is usually pretty generous!\",\n                    \"Plot twist: I've been rate limited! \\uD83D\\uDCCA It's like being a comedian who's told too many jokes too fast. Give me a moment to catch my breath and try again - Gemini usually recovers quickly!\",\n                    \"Houston, we have a rate limit! \\uD83D\\uDE80 I'm currently running on backup personality generators (aka pre-written responses). Try again in a few seconds - Google's free tier is usually quite forgiving!\",\n                    \"Breaking news: AI needs a coffee break! ☕ Don't worry though, my personality is still intact. Gemini's free tier just needs a moment to reset. Try your message again in a few seconds!\"\n                ];\n                const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: randomResponse,\n                    sources: [\n                        \"demo-mode\"\n                    ],\n                    confidence: 0.9,\n                    processingTime: 200,\n                    metadata: {\n                        mode: \"quota_exceeded_fallback\",\n                        note: \"Add credits to OpenAI account for full AI functionality\"\n                    }\n                });\n            }\n        }\n        // Generic fallback for other errors\n        const genericFallbacks = [\n            \"Something went wrong on my end! \\uD83E\\uDD16 I'm having a bit of a digital hiccup. Try again in a moment, or check if there are any API issues.\",\n            \"Oops! My circuits got a bit tangled there. \\uD83D\\uDD27 Give me another shot - sometimes even AI needs a second try!\",\n            \"Error 404: Wit not found! \\uD83D\\uDE05 Just kidding, I'm having a technical moment. Try your message again!\"\n        ];\n        const fallbackResponse = genericFallbacks[Math.floor(Math.random() * genericFallbacks.length)];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: fallbackResponse,\n            sources: [\n                \"error-fallback\"\n            ],\n            confidence: 0.7,\n            processingTime: 150\n        });\n    }\n}\n// Health check endpoint\nasync function GET() {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        status: \"ok\",\n        message: \"Chat API is running\",\n        timestamp: new Date().toISOString()\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cmowee%5CDocuments%5Caugment-projects%5Cchatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();