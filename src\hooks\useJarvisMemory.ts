'use client'

import { useState, useEffect } from 'react'
import { Message } from '@/types/chat'
import { jarvisMemory, UserProfile, MemoryContext } from '@/lib/memory-system'

export function useJarvisMemory() {
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [memoryContext, setMemoryContext] = useState<MemoryContext | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // Initialize memory system
  useEffect(() => {
    const initializeMemory = async () => {
      try {
        const profile = await jarvisMemory.initializeUser()
        setUserProfile(profile)
        setMemoryContext(jarvisMemory.getMemoryContext())
        setIsInitialized(true)
        console.log('Jarvis memory system initialized')
      } catch (error) {
        console.error('Failed to initialize memory system:', error)
        setIsInitialized(true) // Still mark as initialized to prevent infinite loading
      }
    }

    initializeMemory()
  }, [])

  // Add message to memory
  const addMessage = (message: Message) => {
    jarvisMemory.addMessage(message)
    setMemoryContext(jarvisMemory.getMemoryContext())
    
    // Update profile state if it changed
    const updatedProfile = jarvisMemory.getMemoryContext()
    if (updatedProfile) {
      // Trigger a re-render by updating the timestamp
      setUserProfile(prev => prev ? { ...prev, lastActive: new Date() } : null)
    }
  }

  // Update user information
  const updateUserInfo = (updates: Partial<UserProfile>) => {
    jarvisMemory.updateUserInfo(updates)
    setUserProfile(prev => prev ? { ...prev, ...updates } : null)
    setMemoryContext(jarvisMemory.getMemoryContext())
  }

  // Get personalized system prompt
  const getPersonalizedPrompt = (): string => {
    if (!memoryContext) return ''
    
    const conversationSummary = jarvisMemory.getConversationSummary()
    
    return `
${conversationSummary}

PERSONALIZATION INSTRUCTIONS:
- Adapt your communication style to match the user's preference: ${memoryContext.userPreferences.communicationStyle}
- Provide ${memoryContext.userPreferences.responseLength} responses
- Reference their interests when relevant: ${memoryContext.recentTopics.join(', ')}
- Maintain the ${memoryContext.userPreferences.personalityMode} personality mode
- Use context from previous conversations when appropriate
- Be more personal and contextually aware in your responses
    `.trim()
  }

  // Clear all memory
  const clearMemory = () => {
    jarvisMemory.clearMemory()
    setUserProfile(null)
    setMemoryContext(null)
    setIsInitialized(false)
    
    // Reinitialize
    setTimeout(() => {
      jarvisMemory.initializeUser().then(profile => {
        setUserProfile(profile)
        setMemoryContext(jarvisMemory.getMemoryContext())
        setIsInitialized(true)
      })
    }, 100)
  }

  // Get memory stats for display
  const getMemoryStats = () => {
    if (!userProfile || !memoryContext) return null

    return {
      interests: userProfile.personalInfo.interests.length,
      conversationDays: Math.floor(
        (new Date().getTime() - new Date(userProfile.createdAt).getTime()) / (1000 * 60 * 60 * 24)
      ),
      communicationStyle: userProfile.preferences.communicationStyle,
      personalityMode: userProfile.preferences.personalityMode,
      lastActive: userProfile.lastActive
    }
  }

  return {
    userProfile,
    memoryContext,
    isInitialized,
    addMessage,
    updateUserInfo,
    getPersonalizedPrompt,
    clearMemory,
    getMemoryStats
  }
}
