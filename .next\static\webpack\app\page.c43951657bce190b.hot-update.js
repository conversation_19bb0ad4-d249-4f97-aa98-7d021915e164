"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(app-pages-browser)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useJarvisMemory */ \"(app-pages-browser)/./src/hooks/useJarvisMemory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const { userProfile, memoryContext, isInitialized, addMessage, updateUserInfo, getPersonalizedPrompt, getMemoryStats } = (0,_hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemoryPanel, setShowMemoryPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize welcome message based on memory\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && messages.length === 0) {\n            const welcomeMessage = {\n                id: \"1\",\n                content: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) ? \"Welcome back, \".concat(userProfile.name, \"! I'm Jarvis, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?\") : \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n            addMessage(welcomeMessage);\n        }\n    }, [\n        isInitialized,\n        userProfile\n    ]);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        addMessage(userMessage) // Add to memory system\n        ;\n        setIsLoading(true);\n        try {\n            // Include memory context in the request\n            const personalizedPrompt = getPersonalizedPrompt();\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages,\n                    memoryContext: personalizedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    messages: messages,\n                    onSendMessage: handleSendMessage,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 100,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"3dB+vyKJP+vxT+qSB199HXUCEXw=\", false, function() {\n    return [\n        _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ1c7QUFDZDtBQUdpQjtBQUUxQyxTQUFTSzs7SUFDdEIsTUFBTSxFQUNKQyxXQUFXLEVBQ1hDLGFBQWEsRUFDYkMsYUFBYSxFQUNiQyxVQUFVLEVBQ1ZDLGNBQWMsRUFDZEMscUJBQXFCLEVBQ3JCQyxjQUFjLEVBQ2YsR0FBR1IsdUVBQWVBO0lBRW5CLE1BQU0sQ0FBQ1MsVUFBVUMsWUFBWSxHQUFHZCwrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ2UsV0FBV0MsYUFBYSxHQUFHaEIsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDaUIsaUJBQWlCQyxtQkFBbUIsR0FBR2xCLCtDQUFRQSxDQUFDO0lBRXZELDZDQUE2QztJQUM3Q0MsZ0RBQVNBLENBQUM7UUFDUixJQUFJTyxpQkFBaUJLLFNBQVNNLE1BQU0sS0FBSyxHQUFHO1lBQzFDLE1BQU1DLGlCQUEwQjtnQkFDOUJDLElBQUk7Z0JBQ0pDLFNBQVNoQixDQUFBQSx3QkFBQUEsa0NBQUFBLFlBQWFpQixJQUFJLElBQ3RCLGlCQUFrQyxPQUFqQmpCLFlBQVlpQixJQUFJLEVBQUMsNkxBQ2xDO2dCQUNKQyxNQUFNO2dCQUNOQyxXQUFXLElBQUlDO1lBQ2pCO1lBQ0FaLFlBQVk7Z0JBQUNNO2FBQWU7WUFDNUJYLFdBQVdXO1FBQ2I7SUFDRixHQUFHO1FBQUNaO1FBQWVGO0tBQVk7SUFFL0IsTUFBTXFCLG9CQUFvQixPQUFPTDtRQUMvQixNQUFNTSxjQUF1QjtZQUMzQlAsSUFBSUssS0FBS0csR0FBRyxHQUFHQyxRQUFRO1lBQ3ZCUjtZQUNBRSxNQUFNO1lBQ05DLFdBQVcsSUFBSUM7UUFDakI7UUFFQVosWUFBWWlCLENBQUFBLE9BQVE7bUJBQUlBO2dCQUFNSDthQUFZO1FBQzFDbkIsV0FBV21CLGFBQWEsdUJBQXVCOztRQUMvQ1osYUFBYTtRQUViLElBQUk7WUFDRix3Q0FBd0M7WUFDeEMsTUFBTWdCLHFCQUFxQnJCO1lBRTNCLE1BQU1zQixXQUFXLE1BQU1DLE1BQU0sYUFBYTtnQkFDeENDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUNuQkMsU0FBU2xCO29CQUNUbUIsU0FBUzVCO29CQUNUTixlQUFleUI7Z0JBQ2pCO1lBQ0Y7WUFFQSxJQUFJLENBQUNDLFNBQVNTLEVBQUUsRUFBRTtnQkFDaEIsTUFBTSxJQUFJQyxNQUFNO1lBQ2xCO1lBRUEsTUFBTUMsT0FBTyxNQUFNWCxTQUFTWSxJQUFJO1lBRWhDLE1BQU1DLG1CQUE0QjtnQkFDaEN6QixJQUFJLENBQUNLLEtBQUtHLEdBQUcsS0FBSyxHQUFHQyxRQUFRO2dCQUM3QlIsU0FBU3NCLEtBQUtKLE9BQU8sSUFBSTtnQkFDekJoQixNQUFNO2dCQUNOQyxXQUFXLElBQUlDO1lBQ2pCO1lBRUFaLFlBQVlpQixDQUFBQSxPQUFRO3VCQUFJQTtvQkFBTWU7aUJBQWlCO1FBQ2pELEVBQUUsT0FBT0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4QyxNQUFNRSxlQUF3QjtnQkFDNUI1QixJQUFJLENBQUNLLEtBQUtHLEdBQUcsS0FBSyxHQUFHQyxRQUFRO2dCQUM3QlIsU0FBUztnQkFDVEUsTUFBTTtnQkFDTkMsV0FBVyxJQUFJQztZQUNqQjtZQUNBWixZQUFZaUIsQ0FBQUEsT0FBUTt1QkFBSUE7b0JBQU1rQjtpQkFBYTtRQUM3QyxTQUFVO1lBQ1JqQyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLHFCQUNFLDhEQUFDa0M7UUFBS0MsV0FBVTs7MEJBQ2QsOERBQUNoRCwwREFBTUE7Ozs7OzBCQUNQLDhEQUFDaUQ7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNqRCxpRUFBYUE7b0JBQ1pXLFVBQVVBO29CQUNWd0MsZUFBZTFCO29CQUNmWixXQUFXQTs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLckI7R0FuR3dCVjs7UUFTbEJELG1FQUFlQTs7O0tBVEdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvcGFnZS50c3g/ZjY4YSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IENoYXRJbnRlcmZhY2UgZnJvbSAnQC9jb21wb25lbnRzL0NoYXRJbnRlcmZhY2UnXG5pbXBvcnQgSGVhZGVyIGZyb20gJ0AvY29tcG9uZW50cy9IZWFkZXInXG5pbXBvcnQgTWVtb3J5UGFuZWwgZnJvbSAnQC9jb21wb25lbnRzL01lbW9yeVBhbmVsJ1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ0AvdHlwZXMvY2hhdCdcbmltcG9ydCB7IHVzZUphcnZpc01lbW9yeSB9IGZyb20gJ0AvaG9va3MvdXNlSmFydmlzTWVtb3J5J1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBIb21lKCkge1xuICBjb25zdCB7XG4gICAgdXNlclByb2ZpbGUsXG4gICAgbWVtb3J5Q29udGV4dCxcbiAgICBpc0luaXRpYWxpemVkLFxuICAgIGFkZE1lc3NhZ2UsXG4gICAgdXBkYXRlVXNlckluZm8sXG4gICAgZ2V0UGVyc29uYWxpemVkUHJvbXB0LFxuICAgIGdldE1lbW9yeVN0YXRzXG4gIH0gPSB1c2VKYXJ2aXNNZW1vcnkoKVxuXG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGU8TWVzc2FnZVtdPihbXSlcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2hvd01lbW9yeVBhbmVsLCBzZXRTaG93TWVtb3J5UGFuZWxdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgLy8gSW5pdGlhbGl6ZSB3ZWxjb21lIG1lc3NhZ2UgYmFzZWQgb24gbWVtb3J5XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGlzSW5pdGlhbGl6ZWQgJiYgbWVzc2FnZXMubGVuZ3RoID09PSAwKSB7XG4gICAgICBjb25zdCB3ZWxjb21lTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6ICcxJyxcbiAgICAgICAgY29udGVudDogdXNlclByb2ZpbGU/Lm5hbWVcbiAgICAgICAgICA/IGBXZWxjb21lIGJhY2ssICR7dXNlclByb2ZpbGUubmFtZX0hIEknbSBKYXJ2aXMsIGFuZCBJIHJlbWVtYmVyIG91ciBwcmV2aW91cyBjb252ZXJzYXRpb25zLiBJJ20gcmVhZHkgdG8gYXNzaXN0IHlvdSB3aXRoIHRoZSBzYW1lIHByb2Zlc3Npb25hbCBlZmZpY2llbmN5IGFuZCBwZXJzb25hbGl0eSB5b3UndmUgY29tZSB0byBleHBlY3QuIEhvdyBtYXkgSSBoZWxwIHlvdSB0b2RheT9gXG4gICAgICAgICAgOiBcIkdvb2QgZGF5ISBJJ20gSmFydmlzLCB5b3VyIGFkdmFuY2VkIEFJIGFzc2lzdGFudC4gSSdtIGhlcmUgdG8gaGVscCB5b3Ugd2l0aCBhbnkgcXVlc3Rpb25zLCBwcm92aWRlIHJlYWwtdGltZSBpbmZvcm1hdGlvbiwgYW5kIGFzc2lzdCB3aXRoIHZhcmlvdXMgdGFza3MuIEkgY29tYmluZSBwcm9mZXNzaW9uYWwgZWZmaWNpZW5jeSB3aXRoIGEgdG91Y2ggb2YgcGVyc29uYWxpdHkgLSB0aGluayBvZiBtZSBhcyB5b3VyIHJlbGlhYmxlIGRpZ2l0YWwgY29tcGFuaW9uLiBIb3cgbWF5IEkgYmUgb2Ygc2VydmljZSB0b2RheT9cIixcbiAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXG4gICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSxcbiAgICAgIH1cbiAgICAgIHNldE1lc3NhZ2VzKFt3ZWxjb21lTWVzc2FnZV0pXG4gICAgICBhZGRNZXNzYWdlKHdlbGNvbWVNZXNzYWdlKVxuICAgIH1cbiAgfSwgW2lzSW5pdGlhbGl6ZWQsIHVzZXJQcm9maWxlXSlcblxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IGFzeW5jIChjb250ZW50OiBzdHJpbmcpID0+IHtcbiAgICBjb25zdCB1c2VyTWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgIGlkOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBjb250ZW50LFxuICAgICAgcm9sZTogJ3VzZXInLFxuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgIH1cblxuICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSlcbiAgICBhZGRNZXNzYWdlKHVzZXJNZXNzYWdlKSAvLyBBZGQgdG8gbWVtb3J5IHN5c3RlbVxuICAgIHNldElzTG9hZGluZyh0cnVlKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIEluY2x1ZGUgbWVtb3J5IGNvbnRleHQgaW4gdGhlIHJlcXVlc3RcbiAgICAgIGNvbnN0IHBlcnNvbmFsaXplZFByb21wdCA9IGdldFBlcnNvbmFsaXplZFByb21wdCgpXG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvY2hhdCcsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgbWVzc2FnZTogY29udGVudCxcbiAgICAgICAgICBoaXN0b3J5OiBtZXNzYWdlcyxcbiAgICAgICAgICBtZW1vcnlDb250ZXh0OiBwZXJzb25hbGl6ZWRQcm9tcHRcbiAgICAgICAgfSksXG4gICAgICB9KVxuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignRmFpbGVkIHRvIGdldCByZXNwb25zZScpXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIFxuICAgICAgY29uc3QgYXNzaXN0YW50TWVzc2FnZTogTWVzc2FnZSA9IHtcbiAgICAgICAgaWQ6IChEYXRlLm5vdygpICsgMSkudG9TdHJpbmcoKSxcbiAgICAgICAgY29udGVudDogZGF0YS5tZXNzYWdlIHx8IFwiSSdtIHN0aWxsIGxlYXJuaW5nISBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgcmVzcG9uc2UuXCIsXG4gICAgICAgIHJvbGU6ICdhc3Npc3RhbnQnLFxuICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksXG4gICAgICB9XG5cbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGFzc2lzdGFudE1lc3NhZ2VdKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzZW5kaW5nIG1lc3NhZ2U6JywgZXJyb3IpXG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2U6IE1lc3NhZ2UgPSB7XG4gICAgICAgIGlkOiAoRGF0ZS5ub3coKSArIDEpLnRvU3RyaW5nKCksXG4gICAgICAgIGNvbnRlbnQ6IFwiT29wcyEgU29tZXRoaW5nIHdlbnQgd3JvbmcuIEknbSBoYXZpbmcgdHJvdWJsZSBjb25uZWN0aW5nIHJpZ2h0IG5vdy4gVHJ5IGFnYWluIGluIGEgbW9tZW50IVwiLFxuICAgICAgICByb2xlOiAnYXNzaXN0YW50JyxcbiAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgfVxuICAgICAgc2V0TWVzc2FnZXMocHJldiA9PiBbLi4ucHJldiwgZXJyb3JNZXNzYWdlXSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWdyb2stZGFyayB2aWEtZ3Jvay1ncmF5IHRvLWdyb2stZGFya1wiPlxuICAgICAgPEhlYWRlciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgb3ZlcmZsb3ctaGlkZGVuIHJlbGF0aXZlXCI+XG4gICAgICAgIDxDaGF0SW50ZXJmYWNlXG4gICAgICAgICAgbWVzc2FnZXM9e21lc3NhZ2VzfVxuICAgICAgICAgIG9uU2VuZE1lc3NhZ2U9e2hhbmRsZVNlbmRNZXNzYWdlfVxuICAgICAgICAgIGlzTG9hZGluZz17aXNMb2FkaW5nfVxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9tYWluPlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDaGF0SW50ZXJmYWNlIiwiSGVhZGVyIiwidXNlSmFydmlzTWVtb3J5IiwiSG9tZSIsInVzZXJQcm9maWxlIiwibWVtb3J5Q29udGV4dCIsImlzSW5pdGlhbGl6ZWQiLCJhZGRNZXNzYWdlIiwidXBkYXRlVXNlckluZm8iLCJnZXRQZXJzb25hbGl6ZWRQcm9tcHQiLCJnZXRNZW1vcnlTdGF0cyIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJzaG93TWVtb3J5UGFuZWwiLCJzZXRTaG93TWVtb3J5UGFuZWwiLCJsZW5ndGgiLCJ3ZWxjb21lTWVzc2FnZSIsImlkIiwiY29udGVudCIsIm5hbWUiLCJyb2xlIiwidGltZXN0YW1wIiwiRGF0ZSIsImhhbmRsZVNlbmRNZXNzYWdlIiwidXNlck1lc3NhZ2UiLCJub3ciLCJ0b1N0cmluZyIsInByZXYiLCJwZXJzb25hbGl6ZWRQcm9tcHQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwibWVzc2FnZSIsImhpc3RvcnkiLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJhc3Npc3RhbnRNZXNzYWdlIiwiZXJyb3IiLCJjb25zb2xlIiwiZXJyb3JNZXNzYWdlIiwibWFpbiIsImNsYXNzTmFtZSIsImRpdiIsIm9uU2VuZE1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});