# AI API Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
GEMINI_API_KEY=your_gemini_api_key_here

# AI Provider Selection (openai or gemini)
AI_PROVIDER=gemini

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/grok_chatbot"

# JWT Secret for authentication
JWT_SECRET=your_super_secret_jwt_key_here

# Server Configuration
PORT=3001
NEXT_PUBLIC_API_URL=http://localhost:3001

# Real-time features
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
