'use client'

import { useState } from 'react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-grok-gray border-b border-grok-muted/20 px-4 py-3">
      <div className="max-w-6xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-r from-grok-blue to-grok-green rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">G</span>
          </div>
          <div>
            <h1 className="text-grok-text font-semibold text-lg">Grok Chatbot</h1>
            <p className="text-grok-muted text-xs">Your Personal AI Assistant</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="hidden md:flex items-center space-x-2 text-grok-muted text-sm">
            <div className="w-2 h-2 bg-grok-green rounded-full animate-pulse"></div>
            <span>Online</span>
          </div>
          
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="p-2 hover:bg-grok-dark rounded-lg transition-colors"
          >
            <svg className="w-5 h-5 text-grok-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="absolute top-16 right-4 bg-grok-gray border border-grok-muted/20 rounded-lg shadow-lg p-2 z-50">
          <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm">
            New Chat
          </button>
          <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm">
            Settings
          </button>
          <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm">
            About
          </button>
        </div>
      )}
    </header>
  )
}
