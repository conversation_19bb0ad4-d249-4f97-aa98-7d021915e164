'use client'

import { useState } from 'react'

interface HeaderProps {
  onMemoryClick?: () => void
}

export default function Header({ onMemoryClick }: HeaderProps = {}) {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <>
      {/* Scan Line Effect */}
      <div className="scan-line"></div>

      <header className="bg-grok-gray border-b border-grok-muted/20 px-4 py-3 tech-grid relative">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg arc-reactor avatar-pulse">
              <span className="text-white font-bold text-sm">J</span>
            </div>
            <div>
              <h1 className="text-grok-text font-semibold text-xl bg-gradient-to-r from-red-400 to-yellow-400 bg-clip-text text-transparent">
                JARVIS
              </h1>
              <p className="text-grok-muted text-xs font-mono tracking-wider">
                ADVANCED AI ASSISTANT
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2 text-grok-muted text-sm">
              <div className="w-2 h-2 bg-red-500 rounded-full holographic-glow"></div>
              <span className="font-mono tracking-wider">ONLINE</span>
            </div>

            {/* Memory Button */}
            {onMemoryClick && (
              <button
                onClick={onMemoryClick}
                className="p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30"
                title="Memory System"
              >
                <svg className="w-5 h-5 text-grok-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </button>
            )}

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30"
            >
              <svg className="w-5 h-5 text-grok-text" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {isMenuOpen && (
          <div className="absolute top-16 right-4 bg-grok-gray border border-red-500/30 rounded-lg shadow-lg p-2 z-50 glowing-border">
            <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400">
              NEW CHAT
            </button>
            <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400">
              SETTINGS
            </button>
            <button className="w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400">
              ABOUT
            </button>
          </div>
        )}
      </header>
    </>
  )
}
