"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(app-pages-browser)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useJarvisMemory */ \"(app-pages-browser)/./src/hooks/useJarvisMemory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const { userProfile, memoryContext, isInitialized, addMessage, updateUserInfo, getPersonalizedPrompt, getMemoryStats } = (0,_hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemoryPanel, setShowMemoryPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize welcome message based on memory\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && messages.length === 0) {\n            const welcomeMessage = {\n                id: \"1\",\n                content: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) ? \"Welcome back, \".concat(userProfile.name, \"! I'm Jarvis, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?\") : \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n            addMessage(welcomeMessage);\n        }\n    }, [\n        isInitialized,\n        userProfile\n    ]);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        addMessage(userMessage) // Add to memory system\n        ;\n        setIsLoading(true);\n        try {\n            // Include memory context in the request\n            const personalizedPrompt = getPersonalizedPrompt();\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages,\n                    memoryContext: personalizedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date(),\n                metadata: data.metadata\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            addMessage(assistantMessage) // Add to memory system\n            ;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    messages: messages,\n                    onSendMessage: handleSendMessage,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"3dB+vyKJP+vxT+qSB199HXUCEXw=\", false, function() {\n    return [\n        _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});