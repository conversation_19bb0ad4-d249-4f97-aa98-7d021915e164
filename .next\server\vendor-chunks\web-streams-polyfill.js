"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/web-streams-polyfill";
exports.ids = ["vendor-chunks/web-streams-polyfill"];
exports.modules = {

/***/ "(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/web-streams-polyfill/dist/ponyfill.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ByteLengthQueuingStrategy: () => (/* binding */ ByteLengthQueuingStrategy),\n/* harmony export */   CountQueuingStrategy: () => (/* binding */ CountQueuingStrategy),\n/* harmony export */   ReadableByteStreamController: () => (/* binding */ ReadableByteStreamController),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   ReadableStreamBYOBReader: () => (/* binding */ ReadableStreamBYOBReader),\n/* harmony export */   ReadableStreamBYOBRequest: () => (/* binding */ ReadableStreamBYOBRequest),\n/* harmony export */   ReadableStreamDefaultController: () => (/* binding */ ReadableStreamDefaultController),\n/* harmony export */   ReadableStreamDefaultReader: () => (/* binding */ ReadableStreamDefaultReader),\n/* harmony export */   TransformStream: () => (/* binding */ TransformStream),\n/* harmony export */   TransformStreamDefaultController: () => (/* binding */ TransformStreamDefaultController),\n/* harmony export */   WritableStream: () => (/* binding */ WritableStream),\n/* harmony export */   WritableStreamDefaultController: () => (/* binding */ WritableStreamDefaultController),\n/* harmony export */   WritableStreamDefaultWriter: () => (/* binding */ WritableStreamDefaultWriter)\n/* harmony export */ });\n/**\n * @license\n * web-streams-polyfill v4.0.0-beta.3\n * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.\n * This code is released under the MIT license.\n * SPDX-License-Identifier: MIT\n */\nconst e=\"function\"==typeof Symbol&&\"symbol\"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function t(){}function r(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}const o=t;function n(e,t){try{Object.defineProperty(e,\"name\",{value:t,configurable:!0})}catch(e){}}const a=Promise,i=Promise.prototype.then,l=Promise.resolve.bind(a),s=Promise.reject.bind(a);function u(e){return new a(e)}function c(e){return l(e)}function d(e){return s(e)}function f(e,t,r){return i.call(e,t,r)}function b(e,t,r){f(f(e,t,r),void 0,o)}function h(e,t){b(e,t)}function _(e,t){b(e,void 0,t)}function p(e,t,r){return f(e,t,r)}function m(e){f(e,void 0,o)}let y=e=>{if(\"function\"==typeof queueMicrotask)y=queueMicrotask;else{const e=c(void 0);y=t=>f(e,t)}return y(e)};function g(e,t,r){if(\"function\"!=typeof e)throw new TypeError(\"Argument is not a function\");return Function.prototype.apply.call(e,t,r)}function w(e,t,r){try{return c(g(e,t,r))}catch(e){return d(e)}}class S{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){const t=this._back;let r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){const e=this._front;let t=e;const r=this._cursor;let o=r+1;const n=e._elements,a=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,a}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(r=r._next,o=r._elements,t=0,0===o.length));)e(o[t]),++t}peek(){const e=this._front,t=this._cursor;return e._elements[t]}}const v=e(\"[[AbortSteps]]\"),R=e(\"[[ErrorSteps]]\"),T=e(\"[[CancelSteps]]\"),q=e(\"[[PullSteps]]\"),C=e(\"[[ReleaseSteps]]\");function E(e,t){e._ownerReadableStream=t,t._reader=e,\"readable\"===t._state?O(e):\"closed\"===t._state?function(e){O(e),j(e)}(e):B(e,t._storedError)}function P(e,t){return Gt(e._ownerReadableStream,t)}function W(e){const t=e._ownerReadableStream;\"readable\"===t._state?A(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")):function(e,t){B(e,t)}(e,new TypeError(\"Reader was released and can no longer be used to monitor the stream's closedness\")),t._readableStreamController[C](),t._reader=void 0,e._ownerReadableStream=void 0}function k(e){return new TypeError(\"Cannot \"+e+\" a stream using a released reader\")}function O(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r}))}function B(e,t){O(e),A(e,t)}function A(e,t){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function j(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}const z=Number.isFinite||function(e){return\"number\"==typeof e&&isFinite(e)},L=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function F(e,t){if(void 0!==e&&(\"object\"!=typeof(r=e)&&\"function\"!=typeof r))throw new TypeError(`${t} is not an object.`);var r}function I(e,t){if(\"function\"!=typeof e)throw new TypeError(`${t} is not a function.`)}function D(e,t){if(!function(e){return\"object\"==typeof e&&null!==e||\"function\"==typeof e}(e))throw new TypeError(`${t} is not an object.`)}function $(e,t,r){if(void 0===e)throw new TypeError(`Parameter ${t} is required in '${r}'.`)}function M(e,t,r){if(void 0===e)throw new TypeError(`${t} is required in '${r}'.`)}function Y(e){return Number(e)}function Q(e){return 0===e?0:e}function N(e,t){const r=Number.MAX_SAFE_INTEGER;let o=Number(e);if(o=Q(o),!z(o))throw new TypeError(`${t} is not a finite number`);if(o=function(e){return Q(L(e))}(o),o<0||o>r)throw new TypeError(`${t} is outside the accepted range of 0 to ${r}, inclusive`);return z(o)&&0!==o?o:0}function H(e){if(!r(e))return!1;if(\"function\"!=typeof e.getReader)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function x(e){if(!r(e))return!1;if(\"function\"!=typeof e.getWriter)return!1;try{return\"boolean\"==typeof e.locked}catch(e){return!1}}function V(e,t){if(!Vt(e))throw new TypeError(`${t} is not a ReadableStream.`)}function U(e,t){e._reader._readRequests.push(t)}function G(e,t,r){const o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function X(e){return e._reader._readRequests.length}function J(e){const t=e._reader;return void 0!==t&&!!K(t)}class ReadableStreamDefaultReader{constructor(e){if($(e,1,\"ReadableStreamDefaultReader\"),V(e,\"First parameter\"),Ut(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");E(this,e),this._readRequests=new S}get closed(){return K(this)?this._closedPromise:d(ee(\"closed\"))}cancel(e){return K(this)?void 0===this._ownerReadableStream?d(k(\"cancel\")):P(this,e):d(ee(\"cancel\"))}read(){if(!K(this))return d(ee(\"read\"));if(void 0===this._ownerReadableStream)return d(k(\"read from\"));let e,t;const r=u(((r,o)=>{e=r,t=o}));return function(e,t){const r=e._ownerReadableStream;r._disturbed=!0,\"closed\"===r._state?t._closeSteps():\"errored\"===r._state?t._errorSteps(r._storedError):r._readableStreamController[q](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!K(this))throw ee(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError(\"Reader was released\");Z(e,t)}(this)}}function K(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readRequests\")&&e instanceof ReadableStreamDefaultReader)}function Z(e,t){const r=e._readRequests;e._readRequests=new S,r.forEach((e=>{e._errorSteps(t)}))}function ee(e){return new TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(ReadableStreamDefaultReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamDefaultReader.prototype.cancel,\"cancel\"),n(ReadableStreamDefaultReader.prototype.read,\"read\"),n(ReadableStreamDefaultReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamDefaultReader.prototype,e.toStringTag,{value:\"ReadableStreamDefaultReader\",configurable:!0});class te{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?p(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){const t=()=>this._returnSteps(e);return this._ongoingPromise?p(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const e=this._reader;return void 0===e?d(k(\"iterate\")):f(e.read(),(e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0),e}),(e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0,e}))}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;const t=this._reader;if(void 0===t)return d(k(\"finish iterating\"));if(this._reader=void 0,!this._preventCancel){const r=t.cancel(e);return t.releaseLock(),p(r,(()=>({value:e,done:!0})))}return t.releaseLock(),c({value:e,done:!0})}}const re={next(){return oe(this)?this._asyncIteratorImpl.next():d(ne(\"next\"))},return(e){return oe(this)?this._asyncIteratorImpl.return(e):d(ne(\"return\"))}};function oe(e){if(!r(e))return!1;if(!Object.prototype.hasOwnProperty.call(e,\"_asyncIteratorImpl\"))return!1;try{return e._asyncIteratorImpl instanceof te}catch(e){return!1}}function ne(e){return new TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}\"symbol\"==typeof e.asyncIterator&&Object.defineProperty(re,e.asyncIterator,{value(){return this},writable:!0,configurable:!0});const ae=Number.isNaN||function(e){return e!=e};function ie(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function le(e){const t=function(e,t,r){if(e.slice)return e.slice(t,r);const o=r-t,n=new ArrayBuffer(o);return ie(n,0,e,t,o),n}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function se(e){const t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ue(e,t,r){if(\"number\"!=typeof(o=r)||ae(o)||o<0||r===1/0)throw new RangeError(\"Size must be a finite, non-NaN, non-negative number.\");var o;e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ce(e){e._queue=new S,e._queueTotalSize=0}class ReadableStreamBYOBRequest{constructor(){throw new TypeError(\"Illegal constructor\")}get view(){if(!fe(this))throw Be(\"view\");return this._view}respond(e){if(!fe(this))throw Be(\"respond\");if($(e,1,\"respond\"),e=N(e,\"First parameter\"),void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");this._view.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t)throw new TypeError(\"bytesWritten must be 0 when calling respond() on a closed stream\")}else{if(0===t)throw new TypeError(\"bytesWritten must be greater than 0 when calling respond() on a readable stream\");if(r.bytesFilled+t>r.byteLength)throw new RangeError(\"bytesWritten out of range\")}r.buffer=r.buffer,qe(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!fe(this))throw Be(\"respondWithNewView\");if($(e,1,\"respondWithNewView\"),!ArrayBuffer.isView(e))throw new TypeError(\"You can only respond with array buffer views\");if(void 0===this._associatedReadableByteStreamController)throw new TypeError(\"This BYOB request has been invalidated\");e.buffer,function(e,t){const r=e._pendingPullIntos.peek();if(\"closed\"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw new TypeError(\"The view's length must be 0 when calling respondWithNewView() on a closed stream\")}else if(0===t.byteLength)throw new TypeError(\"The view's length must be greater than 0 when calling respondWithNewView() on a readable stream\");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw new RangeError(\"The region specified by view does not match byobRequest\");if(r.bufferByteLength!==t.buffer.byteLength)throw new RangeError(\"The buffer of view has different capacity than byobRequest\");if(r.bytesFilled+t.byteLength>r.byteLength)throw new RangeError(\"The region specified by view is larger than byobRequest\");const o=t.byteLength;r.buffer=t.buffer,qe(e,o)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(ReadableStreamBYOBRequest.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),n(ReadableStreamBYOBRequest.prototype.respond,\"respond\"),n(ReadableStreamBYOBRequest.prototype.respondWithNewView,\"respondWithNewView\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamBYOBRequest.prototype,e.toStringTag,{value:\"ReadableStreamBYOBRequest\",configurable:!0});class ReadableByteStreamController{constructor(){throw new TypeError(\"Illegal constructor\")}get byobRequest(){if(!de(this))throw Ae(\"byobRequest\");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(ReadableStreamBYOBRequest.prototype);!function(e,t,r){e._associatedReadableByteStreamController=t,e._view=r}(o,e,r),e._byobRequest=o}return e._byobRequest}(this)}get desiredSize(){if(!de(this))throw Ae(\"desiredSize\");return ke(this)}close(){if(!de(this))throw Ae(\"close\");if(this._closeRequested)throw new TypeError(\"The stream has already been closed; do not close it again!\");const e=this._controlledReadableByteStream._state;if(\"readable\"!==e)throw new TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){const t=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==t._state)return;if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0){if(e._pendingPullIntos.peek().bytesFilled>0){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");throw Pe(e,t),t}}Ee(e),Xt(t)}(this)}enqueue(e){if(!de(this))throw Ae(\"enqueue\");if($(e,1,\"enqueue\"),!ArrayBuffer.isView(e))throw new TypeError(\"chunk must be an array buffer view\");if(0===e.byteLength)throw new TypeError(\"chunk must have non-zero byteLength\");if(0===e.buffer.byteLength)throw new TypeError(\"chunk's buffer must have non-zero byteLength\");if(this._closeRequested)throw new TypeError(\"stream is closed or draining\");const t=this._controlledReadableByteStream._state;if(\"readable\"!==t)throw new TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){const r=e._controlledReadableByteStream;if(e._closeRequested||\"readable\"!==r._state)return;const o=t.buffer,n=t.byteOffset,a=t.byteLength,i=o;if(e._pendingPullIntos.length>0){const t=e._pendingPullIntos.peek();t.buffer,0,Re(e),t.buffer=t.buffer,\"none\"===t.readerType&&ge(e,t)}if(J(r))if(function(e){const t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;We(e,t._readRequests.shift())}}(e),0===X(r))me(e,i,n,a);else{e._pendingPullIntos.length>0&&Ce(e);G(r,new Uint8Array(i,n,a),!1)}else Le(r)?(me(e,i,n,a),Te(e)):me(e,i,n,a);be(e)}(this,e)}error(e){if(!de(this))throw Ae(\"error\");Pe(this,e)}[T](e){he(this),ce(this);const t=this._cancelAlgorithm(e);return Ee(this),t}[q](e){const t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void We(this,e);const r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}const o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:\"default\"};this._pendingPullIntos.push(o)}U(t,e),be(this)}[C](){if(this._pendingPullIntos.length>0){const e=this._pendingPullIntos.peek();e.readerType=\"none\",this._pendingPullIntos=new S,this._pendingPullIntos.push(e)}}}function de(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableByteStream\")&&e instanceof ReadableByteStreamController)}function fe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_associatedReadableByteStreamController\")&&e instanceof ReadableStreamBYOBRequest)}function be(e){const t=function(e){const t=e._controlledReadableByteStream;if(\"readable\"!==t._state)return!1;if(e._closeRequested)return!1;if(!e._started)return!1;if(J(t)&&X(t)>0)return!0;if(Le(t)&&ze(t)>0)return!0;if(ke(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,be(e)),null)),(t=>(Pe(e,t),null)))}function he(e){Re(e),e._pendingPullIntos=new S}function _e(e,t){let r=!1;\"closed\"===e._state&&(r=!0);const o=pe(t);\"default\"===t.readerType?G(e,o,r):function(e,t,r){const o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function pe(e){const t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function me(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function ye(e,t,r,o){let n;try{n=t.slice(r,r+o)}catch(t){throw Pe(e,t),t}me(e,n,0,o)}function ge(e,t){t.bytesFilled>0&&ye(e,t.buffer,t.byteOffset,t.bytesFilled),Ce(e)}function we(e,t){const r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),a=t.bytesFilled+n,i=a-a%r;let l=n,s=!1;i>o&&(l=i-t.bytesFilled,s=!0);const u=e._queue;for(;l>0;){const r=u.peek(),o=Math.min(l,r.byteLength),n=t.byteOffset+t.bytesFilled;ie(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,Se(e,o,t),l-=o}return s}function Se(e,t,r){r.bytesFilled+=t}function ve(e){0===e._queueTotalSize&&e._closeRequested?(Ee(e),Xt(e._controlledReadableByteStream)):be(e)}function Re(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function Te(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;const t=e._pendingPullIntos.peek();we(e,t)&&(Ce(e),_e(e._controlledReadableByteStream,t))}}function qe(e,t){const r=e._pendingPullIntos.peek();Re(e);\"closed\"===e._controlledReadableByteStream._state?function(e,t){\"none\"===t.readerType&&Ce(e);const r=e._controlledReadableByteStream;if(Le(r))for(;ze(r)>0;)_e(r,Ce(e))}(e,r):function(e,t,r){if(Se(0,t,r),\"none\"===r.readerType)return ge(e,r),void Te(e);if(r.bytesFilled<r.elementSize)return;Ce(e);const o=r.bytesFilled%r.elementSize;if(o>0){const t=r.byteOffset+r.bytesFilled;ye(e,r.buffer,t-o,o)}r.bytesFilled-=o,_e(e._controlledReadableByteStream,r),Te(e)}(e,t,r),be(e)}function Ce(e){return e._pendingPullIntos.shift()}function Ee(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function Pe(e,t){const r=e._controlledReadableByteStream;\"readable\"===r._state&&(he(e),ce(e),Ee(e),Jt(r,t))}function We(e,t){const r=e._queue.shift();e._queueTotalSize-=r.byteLength,ve(e);const o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function ke(e){const t=e._controlledReadableByteStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Oe(e,t,r){const o=Object.create(ReadableByteStreamController.prototype);let n,a,i;n=void 0!==t.start?()=>t.start(o):()=>{},a=void 0!==t.pull?()=>t.pull(o):()=>c(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0);const l=t.autoAllocateChunkSize;if(0===l)throw new TypeError(\"autoAllocateChunkSize must be greater than 0\");!function(e,t,r,o,n,a,i){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ce(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=i,t._pendingPullIntos=new S,e._readableStreamController=t,b(c(r()),(()=>(t._started=!0,be(t),null)),(e=>(Pe(t,e),null)))}(e,o,n,a,i,r,l)}function Be(e){return new TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function Ae(e){return new TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function je(e,t){e._reader._readIntoRequests.push(t)}function ze(e){return e._reader._readIntoRequests.length}function Le(e){const t=e._reader;return void 0!==t&&!!Fe(t)}Object.defineProperties(ReadableByteStreamController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableByteStreamController.prototype.close,\"close\"),n(ReadableByteStreamController.prototype.enqueue,\"enqueue\"),n(ReadableByteStreamController.prototype.error,\"error\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableByteStreamController.prototype,e.toStringTag,{value:\"ReadableByteStreamController\",configurable:!0});class ReadableStreamBYOBReader{constructor(e){if($(e,1,\"ReadableStreamBYOBReader\"),V(e,\"First parameter\"),Ut(e))throw new TypeError(\"This stream has already been locked for exclusive reading by another reader\");if(!de(e._readableStreamController))throw new TypeError(\"Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source\");E(this,e),this._readIntoRequests=new S}get closed(){return Fe(this)?this._closedPromise:d(De(\"closed\"))}cancel(e){return Fe(this)?void 0===this._ownerReadableStream?d(k(\"cancel\")):P(this,e):d(De(\"cancel\"))}read(e){if(!Fe(this))return d(De(\"read\"));if(!ArrayBuffer.isView(e))return d(new TypeError(\"view must be an array buffer view\"));if(0===e.byteLength)return d(new TypeError(\"view must have non-zero byteLength\"));if(0===e.buffer.byteLength)return d(new TypeError(\"view's buffer must have non-zero byteLength\"));if(e.buffer,void 0===this._ownerReadableStream)return d(k(\"read from\"));let t,r;const o=u(((e,o)=>{t=e,r=o}));return function(e,t,r){const o=e._ownerReadableStream;o._disturbed=!0,\"errored\"===o._state?r._errorSteps(o._storedError):function(e,t,r){const o=e._controlledReadableByteStream;let n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);const a=t.constructor,i=t.buffer,l={buffer:i,bufferByteLength:i.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:a,readerType:\"byob\"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(l),void je(o,r);if(\"closed\"!==o._state){if(e._queueTotalSize>0){if(we(e,l)){const t=pe(l);return ve(e),void r._chunkSteps(t)}if(e._closeRequested){const t=new TypeError(\"Insufficient bytes to fill elements in the given buffer\");return Pe(e,t),void r._errorSteps(t)}}e._pendingPullIntos.push(l),je(o,r),be(e)}else{const e=new a(l.buffer,l.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!Fe(this))throw De(\"releaseLock\");void 0!==this._ownerReadableStream&&function(e){W(e);const t=new TypeError(\"Reader was released\");Ie(e,t)}(this)}}function Fe(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readIntoRequests\")&&e instanceof ReadableStreamBYOBReader)}function Ie(e,t){const r=e._readIntoRequests;e._readIntoRequests=new S,r.forEach((e=>{e._errorSteps(t)}))}function De(e){return new TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function $e(e,t){const{highWaterMark:r}=e;if(void 0===r)return t;if(ae(r)||r<0)throw new RangeError(\"Invalid highWaterMark\");return r}function Me(e){const{size:t}=e;return t||(()=>1)}function Ye(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:Y(r),size:void 0===o?void 0:Qe(o,`${t} has member 'size' that`)}}function Qe(e,t){return I(e,t),t=>Y(e(t))}function Ne(e,t,r){return I(e,r),r=>w(e,t,[r])}function He(e,t,r){return I(e,r),()=>w(e,t,[])}function xe(e,t,r){return I(e,r),r=>g(e,t,[r])}function Ve(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(ReadableStreamBYOBReader.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),n(ReadableStreamBYOBReader.prototype.cancel,\"cancel\"),n(ReadableStreamBYOBReader.prototype.read,\"read\"),n(ReadableStreamBYOBReader.prototype.releaseLock,\"releaseLock\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamBYOBReader.prototype,e.toStringTag,{value:\"ReadableStreamBYOBReader\",configurable:!0});const Ue=\"function\"==typeof AbortController;class WritableStream{constructor(e={},t={}){void 0===e?e=null:D(e,\"First parameter\");const r=Ye(t,\"Second parameter\"),o=function(e,t){F(e,t);const r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,a=null==e?void 0:e.type,i=null==e?void 0:e.write;return{abort:void 0===r?void 0:Ne(r,e,`${t} has member 'abort' that`),close:void 0===o?void 0:He(o,e,`${t} has member 'close' that`),start:void 0===n?void 0:xe(n,e,`${t} has member 'start' that`),write:void 0===i?void 0:Ve(i,e,`${t} has member 'write' that`),type:a}}(e,\"First parameter\");var n;(n=this)._state=\"writable\",n._storedError=void 0,n._writer=void 0,n._writableStreamController=void 0,n._writeRequests=new S,n._inFlightWriteRequest=void 0,n._closeRequest=void 0,n._inFlightCloseRequest=void 0,n._pendingAbortRequest=void 0,n._backpressure=!1;if(void 0!==o.type)throw new RangeError(\"Invalid type is specified\");const a=Me(r);!function(e,t,r,o){const n=Object.create(WritableStreamDefaultController.prototype);let a,i,l,s;a=void 0!==t.start?()=>t.start(n):()=>{};i=void 0!==t.write?e=>t.write(e,n):()=>c(void 0);l=void 0!==t.close?()=>t.close():()=>c(void 0);s=void 0!==t.abort?e=>t.abort(e):()=>c(void 0);!function(e,t,r,o,n,a,i,l){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ce(t),t._abortReason=void 0,t._abortController=function(){if(Ue)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=l,t._strategyHWM=i,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=a;const s=bt(t);nt(e,s);const u=r();b(c(u),(()=>(t._started=!0,dt(t),null)),(r=>(t._started=!0,Ze(e,r),null)))}(e,n,a,i,l,s,r,o)}(this,o,$e(r,1),a)}get locked(){if(!Ge(this))throw _t(\"locked\");return Xe(this)}abort(e){return Ge(this)?Xe(this)?d(new TypeError(\"Cannot abort a stream that already has a writer\")):Je(this,e):d(_t(\"abort\"))}close(){return Ge(this)?Xe(this)?d(new TypeError(\"Cannot close a stream that already has a writer\")):rt(this)?d(new TypeError(\"Cannot close an already-closing stream\")):Ke(this):d(_t(\"close\"))}getWriter(){if(!Ge(this))throw _t(\"getWriter\");return new WritableStreamDefaultWriter(this)}}function Ge(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_writableStreamController\")&&e instanceof WritableStream)}function Xe(e){return void 0!==e._writer}function Je(e,t){var r;if(\"closed\"===e._state||\"errored\"===e._state)return c(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);const o=e._state;if(\"closed\"===o||\"errored\"===o)return c(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;\"erroring\"===o&&(n=!0,t=void 0);const a=u(((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}}));return e._pendingAbortRequest._promise=a,n||et(e,t),a}function Ke(e){const t=e._state;if(\"closed\"===t||\"errored\"===t)return d(new TypeError(`The stream (in ${t} state) is not in the writable state and cannot be closed`));const r=u(((t,r)=>{const o={_resolve:t,_reject:r};e._closeRequest=o})),o=e._writer;var n;return void 0!==o&&e._backpressure&&\"writable\"===t&&Et(o),ue(n=e._writableStreamController,lt,0),dt(n),r}function Ze(e,t){\"writable\"!==e._state?tt(e):et(e,t)}function et(e,t){const r=e._writableStreamController;e._state=\"erroring\",e._storedError=t;const o=e._writer;void 0!==o&&it(o,t),!function(e){if(void 0===e._inFlightWriteRequest&&void 0===e._inFlightCloseRequest)return!1;return!0}(e)&&r._started&&tt(e)}function tt(e){e._state=\"errored\",e._writableStreamController[R]();const t=e._storedError;if(e._writeRequests.forEach((e=>{e._reject(t)})),e._writeRequests=new S,void 0===e._pendingAbortRequest)return void ot(e);const r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void ot(e);b(e._writableStreamController[v](r._reason),(()=>(r._resolve(),ot(e),null)),(t=>(r._reject(t),ot(e),null)))}function rt(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ot(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);const t=e._writer;void 0!==t&&St(t,e._storedError)}function nt(e,t){const r=e._writer;void 0!==r&&t!==e._backpressure&&(t?function(e){Rt(e)}(r):Et(r)),e._backpressure=t}Object.defineProperties(WritableStream.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),n(WritableStream.prototype.abort,\"abort\"),n(WritableStream.prototype.close,\"close\"),n(WritableStream.prototype.getWriter,\"getWriter\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStream.prototype,e.toStringTag,{value:\"WritableStream\",configurable:!0});class WritableStreamDefaultWriter{constructor(e){if($(e,1,\"WritableStreamDefaultWriter\"),function(e,t){if(!Ge(e))throw new TypeError(`${t} is not a WritableStream.`)}(e,\"First parameter\"),Xe(e))throw new TypeError(\"This stream has already been locked for exclusive writing by another writer\");this._ownerWritableStream=e,e._writer=this;const t=e._state;if(\"writable\"===t)!rt(e)&&e._backpressure?Rt(this):qt(this),gt(this);else if(\"erroring\"===t)Tt(this,e._storedError),gt(this);else if(\"closed\"===t)qt(this),gt(r=this),vt(r);else{const t=e._storedError;Tt(this,t),wt(this,t)}var r}get closed(){return at(this)?this._closedPromise:d(mt(\"closed\"))}get desiredSize(){if(!at(this))throw mt(\"desiredSize\");if(void 0===this._ownerWritableStream)throw yt(\"desiredSize\");return function(e){const t=e._ownerWritableStream,r=t._state;if(\"errored\"===r||\"erroring\"===r)return null;if(\"closed\"===r)return 0;return ct(t._writableStreamController)}(this)}get ready(){return at(this)?this._readyPromise:d(mt(\"ready\"))}abort(e){return at(this)?void 0===this._ownerWritableStream?d(yt(\"abort\")):function(e,t){return Je(e._ownerWritableStream,t)}(this,e):d(mt(\"abort\"))}close(){if(!at(this))return d(mt(\"close\"));const e=this._ownerWritableStream;return void 0===e?d(yt(\"close\")):rt(e)?d(new TypeError(\"Cannot close an already-closing stream\")):Ke(this._ownerWritableStream)}releaseLock(){if(!at(this))throw mt(\"releaseLock\");void 0!==this._ownerWritableStream&&function(e){const t=e._ownerWritableStream,r=new TypeError(\"Writer was released and can no longer be used to monitor the stream's closedness\");it(e,r),function(e,t){\"pending\"===e._closedPromiseState?St(e,t):function(e,t){wt(e,t)}(e,t)}(e,r),t._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return at(this)?void 0===this._ownerWritableStream?d(yt(\"write to\")):function(e,t){const r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return ft(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return d(yt(\"write to\"));const a=r._state;if(\"errored\"===a)return d(r._storedError);if(rt(r)||\"closed\"===a)return d(new TypeError(\"The stream is closing or closed and cannot be written to\"));if(\"erroring\"===a)return d(r._storedError);const i=function(e){return u(((t,r)=>{const o={_resolve:t,_reject:r};e._writeRequests.push(o)}))}(r);return function(e,t,r){try{ue(e,t,r)}catch(t){return void ft(e,t)}const o=e._controlledWritableStream;if(!rt(o)&&\"writable\"===o._state){nt(o,bt(e))}dt(e)}(o,t,n),i}(this,e):d(mt(\"write\"))}}function at(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_ownerWritableStream\")&&e instanceof WritableStreamDefaultWriter)}function it(e,t){\"pending\"===e._readyPromiseState?Ct(e,t):function(e,t){Tt(e,t)}(e,t)}Object.defineProperties(WritableStreamDefaultWriter.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),n(WritableStreamDefaultWriter.prototype.abort,\"abort\"),n(WritableStreamDefaultWriter.prototype.close,\"close\"),n(WritableStreamDefaultWriter.prototype.releaseLock,\"releaseLock\"),n(WritableStreamDefaultWriter.prototype.write,\"write\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStreamDefaultWriter.prototype,e.toStringTag,{value:\"WritableStreamDefaultWriter\",configurable:!0});const lt={};class WritableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get abortReason(){if(!st(this))throw pt(\"abortReason\");return this._abortReason}get signal(){if(!st(this))throw pt(\"signal\");if(void 0===this._abortController)throw new TypeError(\"WritableStreamDefaultController.prototype.signal is not supported\");return this._abortController.signal}error(e){if(!st(this))throw pt(\"error\");\"writable\"===this._controlledWritableStream._state&&ht(this,e)}[v](e){const t=this._abortAlgorithm(e);return ut(this),t}[R](){ce(this)}}function st(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledWritableStream\")&&e instanceof WritableStreamDefaultController)}function ut(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function ct(e){return e._strategyHWM-e._queueTotalSize}function dt(e){const t=e._controlledWritableStream;if(!e._started)return;if(void 0!==t._inFlightWriteRequest)return;if(\"erroring\"===t._state)return void tt(t);if(0===e._queue.length)return;const r=e._queue.peek().value;r===lt?function(e){const t=e._controlledWritableStream;(function(e){e._inFlightCloseRequest=e._closeRequest,e._closeRequest=void 0})(t),se(e);const r=e._closeAlgorithm();ut(e),b(r,(()=>(function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,\"erroring\"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state=\"closed\";const t=e._writer;void 0!==t&&vt(t)}(t),null)),(e=>(function(e,t){e._inFlightCloseRequest._reject(t),e._inFlightCloseRequest=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t),e._pendingAbortRequest=void 0),Ze(e,t)}(t,e),null)))}(e):function(e,t){const r=e._controlledWritableStream;!function(e){e._inFlightWriteRequest=e._writeRequests.shift()}(r);b(e._writeAlgorithm(t),(()=>{!function(e){e._inFlightWriteRequest._resolve(void 0),e._inFlightWriteRequest=void 0}(r);const t=r._state;if(se(e),!rt(r)&&\"writable\"===t){const t=bt(e);nt(r,t)}return dt(e),null}),(t=>(\"writable\"===r._state&&ut(e),function(e,t){e._inFlightWriteRequest._reject(t),e._inFlightWriteRequest=void 0,Ze(e,t)}(r,t),null)))}(e,r)}function ft(e,t){\"writable\"===e._controlledWritableStream._state&&ht(e,t)}function bt(e){return ct(e)<=0}function ht(e,t){const r=e._controlledWritableStream;ut(e),et(r,t)}function _t(e){return new TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function pt(e){return new TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function mt(e){return new TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function yt(e){return new TypeError(\"Cannot \"+e+\" a stream using a released writer\")}function gt(e){e._closedPromise=u(((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState=\"pending\"}))}function wt(e,t){gt(e),St(e,t)}function St(e,t){void 0!==e._closedPromise_reject&&(m(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"rejected\")}function vt(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState=\"resolved\")}function Rt(e){e._readyPromise=u(((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r})),e._readyPromiseState=\"pending\"}function Tt(e,t){Rt(e),Ct(e,t)}function qt(e){Rt(e),Et(e)}function Ct(e,t){void 0!==e._readyPromise_reject&&(m(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"rejected\")}function Et(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState=\"fulfilled\")}Object.defineProperties(WritableStreamDefaultController.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(WritableStreamDefaultController.prototype,e.toStringTag,{value:\"WritableStreamDefaultController\",configurable:!0});const Pt=\"undefined\"!=typeof DOMException?DOMException:void 0;const Wt=function(e){if(\"function\"!=typeof e&&\"object\"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(Pt)?Pt:function(){const e=function(e,t){this.message=e||\"\",this.name=t||\"Error\",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,\"constructor\",{value:e,writable:!0,configurable:!0}),e}();function kt(e,t,r,o,n,a){const i=e.getReader(),l=t.getWriter();Vt(e)&&(e._disturbed=!0);let s,_,g,w=!1,S=!1,v=\"readable\",R=\"writable\",T=!1,q=!1;const C=u((e=>{g=e}));let E=Promise.resolve(void 0);return u(((P,W)=>{let k;function O(){if(w)return;const e=u(((e,t)=>{!function r(o){o?e():f(function(){if(w)return c(!0);return f(l.ready,(()=>f(i.read(),(e=>!!e.done||(E=l.write(e.value),m(E),!1)))))}(),r,t)}(!1)}));m(e)}function B(){return v=\"closed\",r?L():z((()=>(Ge(t)&&(T=rt(t),R=t._state),T||\"closed\"===R?c(void 0):\"erroring\"===R||\"errored\"===R?d(_):(T=!0,l.close()))),!1,void 0),null}function A(e){return w||(v=\"errored\",s=e,o?L(!0,e):z((()=>l.abort(e)),!0,e)),null}function j(e){return S||(R=\"errored\",_=e,n?L(!0,e):z((()=>i.cancel(e)),!0,e)),null}if(void 0!==a&&(k=()=>{const e=void 0!==a.reason?a.reason:new Wt(\"Aborted\",\"AbortError\"),t=[];o||t.push((()=>\"writable\"===R?l.abort(e):c(void 0))),n||t.push((()=>\"readable\"===v?i.cancel(e):c(void 0))),z((()=>Promise.all(t.map((e=>e())))),!0,e)},a.aborted?k():a.addEventListener(\"abort\",k)),Vt(e)&&(v=e._state,s=e._storedError),Ge(t)&&(R=t._state,_=t._storedError,T=rt(t)),Vt(e)&&Ge(t)&&(q=!0,g()),\"errored\"===v)A(s);else if(\"erroring\"===R||\"errored\"===R)j(_);else if(\"closed\"===v)B();else if(T||\"closed\"===R){const e=new TypeError(\"the destination writable stream closed before all data could be piped to it\");n?L(!0,e):z((()=>i.cancel(e)),!0,e)}function z(e,t,r){function o(){return\"writable\"!==R||T?n():h(function(){let e;return c(function t(){if(e!==E)return e=E,p(E,t,t)}())}(),n),null}function n(){return e?b(e(),(()=>F(t,r)),(e=>F(!0,e))):F(t,r),null}w||(w=!0,q?o():h(C,o))}function L(e,t){z(void 0,e,t)}function F(e,t){return S=!0,l.releaseLock(),i.releaseLock(),void 0!==a&&a.removeEventListener(\"abort\",k),e?W(t):P(void 0),null}w||(b(i.closed,B,A),b(l.closed,(function(){return S||(R=\"closed\"),null}),j)),q?O():y((()=>{q=!0,g(),O()}))}))}function Ot(e,t){return function(e){try{return e.getReader({mode:\"byob\"}).releaseLock(),!0}catch(e){return!1}}(e)?function(e){let t,r,o,n,a,i=e.getReader(),l=!1,s=!1,d=!1,f=!1,h=!1,p=!1;const m=u((e=>{a=e}));function y(e){_(e.closed,(t=>(e!==i||(o.error(t),n.error(t),h&&p||a(void 0)),null)))}function g(){l&&(i.releaseLock(),i=e.getReader(),y(i),l=!1),b(i.read(),(e=>{var t,r;if(d=!1,f=!1,e.done)return h||o.close(),p||n.close(),null===(t=o.byobRequest)||void 0===t||t.respond(0),null===(r=n.byobRequest)||void 0===r||r.respond(0),h&&p||a(void 0),null;const l=e.value,u=l;let c=l;if(!h&&!p)try{c=le(l)}catch(e){return o.error(e),n.error(e),a(i.cancel(e)),null}return h||o.enqueue(u),p||n.enqueue(c),s=!1,d?S():f&&v(),null}),(()=>(s=!1,null)))}function w(t,r){l||(i.releaseLock(),i=e.getReader({mode:\"byob\"}),y(i),l=!0);const u=r?n:o,c=r?o:n;b(i.read(t),(e=>{var t;d=!1,f=!1;const o=r?p:h,n=r?h:p;if(e.done){o||u.close(),n||c.close();const r=e.value;return void 0!==r&&(o||u.byobRequest.respondWithNewView(r),n||null===(t=c.byobRequest)||void 0===t||t.respond(0)),o&&n||a(void 0),null}const l=e.value;if(n)o||u.byobRequest.respondWithNewView(l);else{let e;try{e=le(l)}catch(e){return u.error(e),c.error(e),a(i.cancel(e)),null}o||u.byobRequest.respondWithNewView(l),c.enqueue(e)}return s=!1,d?S():f&&v(),null}),(()=>(s=!1,null)))}function S(){if(s)return d=!0,c(void 0);s=!0;const e=o.byobRequest;return null===e?g():w(e.view,!1),c(void 0)}function v(){if(s)return f=!0,c(void 0);s=!0;const e=n.byobRequest;return null===e?g():w(e.view,!0),c(void 0)}function R(e){if(h=!0,t=e,p){const e=[t,r],o=i.cancel(e);a(o)}return m}function T(e){if(p=!0,r=e,h){const e=[t,r],o=i.cancel(e);a(o)}return m}const q=new ReadableStream({type:\"bytes\",start(e){o=e},pull:S,cancel:R}),C=new ReadableStream({type:\"bytes\",start(e){n=e},pull:v,cancel:T});return y(i),[q,C]}(e):function(e,t){const r=e.getReader();let o,n,a,i,l,s=!1,d=!1,f=!1,h=!1;const p=u((e=>{l=e}));function m(){return s?(d=!0,c(void 0)):(s=!0,b(r.read(),(e=>{if(d=!1,e.done)return f||a.close(),h||i.close(),f&&h||l(void 0),null;const t=e.value,r=t,o=t;return f||a.enqueue(r),h||i.enqueue(o),s=!1,d&&m(),null}),(()=>(s=!1,null))),c(void 0))}function y(e){if(f=!0,o=e,h){const e=[o,n],t=r.cancel(e);l(t)}return p}function g(e){if(h=!0,n=e,f){const e=[o,n],t=r.cancel(e);l(t)}return p}const w=new ReadableStream({start(e){a=e},pull:m,cancel:y}),S=new ReadableStream({start(e){i=e},pull:m,cancel:g});return _(r.closed,(e=>(a.error(e),i.error(e),f&&h||l(void 0),null))),[w,S]}(e)}class ReadableStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!Bt(this))throw Dt(\"desiredSize\");return Lt(this)}close(){if(!Bt(this))throw Dt(\"close\");if(!Ft(this))throw new TypeError(\"The stream is not in a state that permits close\");!function(e){if(!Ft(e))return;const t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(jt(e),Xt(t))}(this)}enqueue(e){if(!Bt(this))throw Dt(\"enqueue\");if(!Ft(this))throw new TypeError(\"The stream is not in a state that permits enqueue\");return function(e,t){if(!Ft(e))return;const r=e._controlledReadableStream;if(Ut(r)&&X(r)>0)G(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw zt(e,t),t}try{ue(e,t,r)}catch(t){throw zt(e,t),t}}At(e)}(this,e)}error(e){if(!Bt(this))throw Dt(\"error\");zt(this,e)}[T](e){ce(this);const t=this._cancelAlgorithm(e);return jt(this),t}[q](e){const t=this._controlledReadableStream;if(this._queue.length>0){const r=se(this);this._closeRequested&&0===this._queue.length?(jt(this),Xt(t)):At(this),e._chunkSteps(r)}else U(t,e),At(this)}[C](){}}function Bt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledReadableStream\")&&e instanceof ReadableStreamDefaultController)}function At(e){const t=function(e){const t=e._controlledReadableStream;if(!Ft(e))return!1;if(!e._started)return!1;if(Ut(t)&&X(t)>0)return!0;if(Lt(e)>0)return!0;return!1}(e);if(!t)return;if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0;b(e._pullAlgorithm(),(()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,At(e)),null)),(t=>(zt(e,t),null)))}function jt(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function zt(e,t){const r=e._controlledReadableStream;\"readable\"===r._state&&(ce(e),jt(e),Jt(r,t))}function Lt(e){const t=e._controlledReadableStream._state;return\"errored\"===t?null:\"closed\"===t?0:e._strategyHWM-e._queueTotalSize}function Ft(e){return!e._closeRequested&&\"readable\"===e._controlledReadableStream._state}function It(e,t,r,o){const n=Object.create(ReadableStreamDefaultController.prototype);let a,i,l;a=void 0!==t.start?()=>t.start(n):()=>{},i=void 0!==t.pull?()=>t.pull(n):()=>c(void 0),l=void 0!==t.cancel?e=>t.cancel(e):()=>c(void 0),function(e,t,r,o,n,a,i){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ce(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=i,t._strategyHWM=a,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,b(c(r()),(()=>(t._started=!0,At(t),null)),(e=>(zt(t,e),null)))}(e,n,a,i,l,r,o)}function Dt(e){return new TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function $t(e,t,r){return I(e,r),r=>w(e,t,[r])}function Mt(e,t,r){return I(e,r),r=>w(e,t,[r])}function Yt(e,t,r){return I(e,r),r=>g(e,t,[r])}function Qt(e,t){if(\"bytes\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}function Nt(e,t){if(\"byob\"!==(e=`${e}`))throw new TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}function Ht(e,t){F(e,t);const r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,a=null==e?void 0:e.signal;return void 0!==a&&function(e,t){if(!function(e){if(\"object\"!=typeof e||null===e)return!1;try{return\"boolean\"==typeof e.aborted}catch(e){return!1}}(e))throw new TypeError(`${t} is not an AbortSignal.`)}(a,`${t} has member 'signal' that`),{preventAbort:Boolean(r),preventCancel:Boolean(o),preventClose:Boolean(n),signal:a}}function xt(e,t){F(e,t);const r=null==e?void 0:e.readable;M(r,\"readable\",\"ReadableWritablePair\"),function(e,t){if(!H(e))throw new TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);const o=null==e?void 0:e.writable;return M(o,\"writable\",\"ReadableWritablePair\"),function(e,t){if(!x(e))throw new TypeError(`${t} is not a WritableStream.`)}(o,`${t} has member 'writable' that`),{readable:r,writable:o}}Object.defineProperties(ReadableStreamDefaultController.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),n(ReadableStreamDefaultController.prototype.close,\"close\"),n(ReadableStreamDefaultController.prototype.enqueue,\"enqueue\"),n(ReadableStreamDefaultController.prototype.error,\"error\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStreamDefaultController.prototype,e.toStringTag,{value:\"ReadableStreamDefaultController\",configurable:!0});class ReadableStream{constructor(e={},t={}){void 0===e?e=null:D(e,\"First parameter\");const r=Ye(t,\"Second parameter\"),o=function(e,t){F(e,t);const r=e,o=null==r?void 0:r.autoAllocateChunkSize,n=null==r?void 0:r.cancel,a=null==r?void 0:r.pull,i=null==r?void 0:r.start,l=null==r?void 0:r.type;return{autoAllocateChunkSize:void 0===o?void 0:N(o,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===n?void 0:$t(n,r,`${t} has member 'cancel' that`),pull:void 0===a?void 0:Mt(a,r,`${t} has member 'pull' that`),start:void 0===i?void 0:Yt(i,r,`${t} has member 'start' that`),type:void 0===l?void 0:Qt(l,`${t} has member 'type' that`)}}(e,\"First parameter\");var n;if((n=this)._state=\"readable\",n._reader=void 0,n._storedError=void 0,n._disturbed=!1,\"bytes\"===o.type){if(void 0!==r.size)throw new RangeError(\"The strategy for a byte stream cannot have a size function\");Oe(this,o,$e(r,0))}else{const e=Me(r);It(this,o,$e(r,1),e)}}get locked(){if(!Vt(this))throw Kt(\"locked\");return Ut(this)}cancel(e){return Vt(this)?Ut(this)?d(new TypeError(\"Cannot cancel a stream that already has a reader\")):Gt(this,e):d(Kt(\"cancel\"))}getReader(e){if(!Vt(this))throw Kt(\"getReader\");return void 0===function(e,t){F(e,t);const r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:Nt(r,`${t} has member 'mode' that`)}}(e,\"First parameter\").mode?new ReadableStreamDefaultReader(this):function(e){return new ReadableStreamBYOBReader(e)}(this)}pipeThrough(e,t={}){if(!H(this))throw Kt(\"pipeThrough\");$(e,1,\"pipeThrough\");const r=xt(e,\"First parameter\"),o=Ht(t,\"Second parameter\");if(this.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream\");if(r.writable.locked)throw new TypeError(\"ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream\");return m(kt(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){if(!H(this))return d(Kt(\"pipeTo\"));if(void 0===e)return d(\"Parameter 1 is required in 'pipeTo'.\");if(!x(e))return d(new TypeError(\"ReadableStream.prototype.pipeTo's first argument must be a WritableStream\"));let r;try{r=Ht(t,\"Second parameter\")}catch(e){return d(e)}return this.locked?d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream\")):e.locked?d(new TypeError(\"ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream\")):kt(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!H(this))throw Kt(\"tee\");if(this.locked)throw new TypeError(\"Cannot tee a stream that already has a reader\");return Ot(this)}values(e){if(!H(this))throw Kt(\"values\");return function(e,t){const r=e.getReader(),o=new te(r,t),n=Object.create(re);return n._asyncIteratorImpl=o,n}(this,function(e,t){F(e,t);const r=null==e?void 0:e.preventCancel;return{preventCancel:Boolean(r)}}(e,\"First parameter\").preventCancel)}}function Vt(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_readableStreamController\")&&e instanceof ReadableStream)}function Ut(e){return void 0!==e._reader}function Gt(e,r){if(e._disturbed=!0,\"closed\"===e._state)return c(void 0);if(\"errored\"===e._state)return d(e._storedError);Xt(e);const o=e._reader;if(void 0!==o&&Fe(o)){const e=o._readIntoRequests;o._readIntoRequests=new S,e.forEach((e=>{e._closeSteps(void 0)}))}return p(e._readableStreamController[T](r),t)}function Xt(e){e._state=\"closed\";const t=e._reader;if(void 0!==t&&(j(t),K(t))){const e=t._readRequests;t._readRequests=new S,e.forEach((e=>{e._closeSteps()}))}}function Jt(e,t){e._state=\"errored\",e._storedError=t;const r=e._reader;void 0!==r&&(A(r,t),K(r)?Z(r,t):Ie(r,t))}function Kt(e){return new TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function Zt(e,t){F(e,t);const r=null==e?void 0:e.highWaterMark;return M(r,\"highWaterMark\",\"QueuingStrategyInit\"),{highWaterMark:Y(r)}}Object.defineProperties(ReadableStream.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),n(ReadableStream.prototype.cancel,\"cancel\"),n(ReadableStream.prototype.getReader,\"getReader\"),n(ReadableStream.prototype.pipeThrough,\"pipeThrough\"),n(ReadableStream.prototype.pipeTo,\"pipeTo\"),n(ReadableStream.prototype.tee,\"tee\"),n(ReadableStream.prototype.values,\"values\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ReadableStream.prototype,e.toStringTag,{value:\"ReadableStream\",configurable:!0}),\"symbol\"==typeof e.asyncIterator&&Object.defineProperty(ReadableStream.prototype,e.asyncIterator,{value:ReadableStream.prototype.values,writable:!0,configurable:!0});const er=e=>e.byteLength;n(er,\"size\");class ByteLengthQueuingStrategy{constructor(e){$(e,1,\"ByteLengthQueuingStrategy\"),e=Zt(e,\"First parameter\"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!rr(this))throw tr(\"highWaterMark\");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!rr(this))throw tr(\"size\");return er}}function tr(e){return new TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function rr(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_byteLengthQueuingStrategyHighWaterMark\")&&e instanceof ByteLengthQueuingStrategy)}Object.defineProperties(ByteLengthQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(ByteLengthQueuingStrategy.prototype,e.toStringTag,{value:\"ByteLengthQueuingStrategy\",configurable:!0});const or=()=>1;n(or,\"size\");class CountQueuingStrategy{constructor(e){$(e,1,\"CountQueuingStrategy\"),e=Zt(e,\"First parameter\"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!ar(this))throw nr(\"highWaterMark\");return this._countQueuingStrategyHighWaterMark}get size(){if(!ar(this))throw nr(\"size\");return or}}function nr(e){return new TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function ar(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_countQueuingStrategyHighWaterMark\")&&e instanceof CountQueuingStrategy)}function ir(e,t,r){return I(e,r),r=>w(e,t,[r])}function lr(e,t,r){return I(e,r),r=>g(e,t,[r])}function sr(e,t,r){return I(e,r),(r,o)=>w(e,t,[r,o])}Object.defineProperties(CountQueuingStrategy.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(CountQueuingStrategy.prototype,e.toStringTag,{value:\"CountQueuingStrategy\",configurable:!0});class TransformStream{constructor(e={},t={},r={}){void 0===e&&(e=null);const o=Ye(t,\"Second parameter\"),n=Ye(r,\"Third parameter\"),a=function(e,t){F(e,t);const r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,a=null==e?void 0:e.transform,i=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:ir(r,e,`${t} has member 'flush' that`),readableType:o,start:void 0===n?void 0:lr(n,e,`${t} has member 'start' that`),transform:void 0===a?void 0:sr(a,e,`${t} has member 'transform' that`),writableType:i}}(e,\"First parameter\");if(void 0!==a.readableType)throw new RangeError(\"Invalid readableType specified\");if(void 0!==a.writableType)throw new RangeError(\"Invalid writableType specified\");const i=$e(n,0),l=Me(n),s=$e(o,1),f=Me(o);let b;!function(e,t,r,o,n,a){function i(){return t}function l(t){return function(e,t){const r=e._transformStreamController;if(e._backpressure){return p(e._backpressureChangePromise,(()=>{if(\"erroring\"===(Ge(e._writable)?e._writable._state:e._writableState))throw Ge(e._writable)?e._writable._storedError:e._writableStoredError;return pr(r,t)}))}return pr(r,t)}(e,t)}function s(t){return function(e,t){return cr(e,t),c(void 0)}(e,t)}function u(){return function(e){const t=e._transformStreamController,r=t._flushAlgorithm();return hr(t),p(r,(()=>{if(\"errored\"===e._readableState)throw e._readableStoredError;gr(e)&&wr(e)}),(t=>{throw cr(e,t),e._readableStoredError}))}(e)}function d(){return function(e){return fr(e,!1),e._backpressureChangePromise}(e)}function f(t){return dr(e,t),c(void 0)}e._writableState=\"writable\",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=function(e,t,r,o,n,a,i){return new WritableStream({start(r){e._writableController=r;try{const t=r.signal;void 0!==t&&t.addEventListener(\"abort\",(()=>{\"writable\"===e._writableState&&(e._writableState=\"erroring\",t.reason&&(e._writableStoredError=t.reason))}))}catch(e){}return p(t(),(()=>(e._writableStarted=!0,Cr(e),null)),(t=>{throw e._writableStarted=!0,Rr(e,t),t}))},write:t=>(function(e){e._writableHasInFlightOperation=!0}(e),p(r(t),(()=>(function(e){e._writableHasInFlightOperation=!1}(e),Cr(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,Rr(e,t)}(e,t),t}))),close:()=>(function(e){e._writableHasInFlightOperation=!0}(e),p(o(),(()=>(function(e){e._writableHasInFlightOperation=!1;\"erroring\"===e._writableState&&(e._writableStoredError=void 0);e._writableState=\"closed\"}(e),null)),(t=>{throw function(e,t){e._writableHasInFlightOperation=!1,e._writableState,Rr(e,t)}(e,t),t}))),abort:t=>(e._writableState=\"errored\",e._writableStoredError=t,n(t))},{highWaterMark:a,size:i})}(e,i,l,u,s,r,o),e._readableState=\"readable\",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=function(e,t,r,o,n,a){return new ReadableStream({start:r=>(e._readableController=r,t().catch((t=>{Sr(e,t)}))),pull:()=>(e._readablePulling=!0,r().catch((t=>{Sr(e,t)}))),cancel:t=>(e._readableState=\"closed\",o(t))},{highWaterMark:n,size:a})}(e,i,d,f,n,a),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,fr(e,!0),e._transformStreamController=void 0}(this,u((e=>{b=e})),s,f,i,l),function(e,t){const r=Object.create(TransformStreamDefaultController.prototype);let o,n;o=void 0!==t.transform?e=>t.transform(e,r):e=>{try{return _r(r,e),c(void 0)}catch(e){return d(e)}};n=void 0!==t.flush?()=>t.flush(r):()=>c(void 0);!function(e,t,r,o){t._controlledTransformStream=e,e._transformStreamController=t,t._transformAlgorithm=r,t._flushAlgorithm=o}(e,r,o,n)}(this,a),void 0!==a.start?b(a.start(this._transformStreamController)):b(void 0)}get readable(){if(!ur(this))throw yr(\"readable\");return this._readable}get writable(){if(!ur(this))throw yr(\"writable\");return this._writable}}function ur(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_transformStreamController\")&&e instanceof TransformStream)}function cr(e,t){Sr(e,t),dr(e,t)}function dr(e,t){hr(e._transformStreamController),function(e,t){e._writableController.error(t);\"writable\"===e._writableState&&Tr(e,t)}(e,t),e._backpressure&&fr(e,!1)}function fr(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=u((t=>{e._backpressureChangePromise_resolve=t})),e._backpressure=t}Object.defineProperties(TransformStream.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(TransformStream.prototype,e.toStringTag,{value:\"TransformStream\",configurable:!0});class TransformStreamDefaultController{constructor(){throw new TypeError(\"Illegal constructor\")}get desiredSize(){if(!br(this))throw mr(\"desiredSize\");return vr(this._controlledTransformStream)}enqueue(e){if(!br(this))throw mr(\"enqueue\");_r(this,e)}error(e){if(!br(this))throw mr(\"error\");var t;t=e,cr(this._controlledTransformStream,t)}terminate(){if(!br(this))throw mr(\"terminate\");!function(e){const t=e._controlledTransformStream;gr(t)&&wr(t);const r=new TypeError(\"TransformStream terminated\");dr(t,r)}(this)}}function br(e){return!!r(e)&&(!!Object.prototype.hasOwnProperty.call(e,\"_controlledTransformStream\")&&e instanceof TransformStreamDefaultController)}function hr(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function _r(e,t){const r=e._controlledTransformStream;if(!gr(r))throw new TypeError(\"Readable side is not in a state that permits enqueue\");try{!function(e,t){e._readablePulling=!1;try{e._readableController.enqueue(t)}catch(t){throw Sr(e,t),t}}(r,t)}catch(e){throw dr(r,e),r._readableStoredError}const o=function(e){return!function(e){if(!gr(e))return!1;if(e._readablePulling)return!0;if(vr(e)>0)return!0;return!1}(e)}(r);o!==r._backpressure&&fr(r,!0)}function pr(e,t){return p(e._transformAlgorithm(t),void 0,(t=>{throw cr(e._controlledTransformStream,t),t}))}function mr(e){return new TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function yr(e){return new TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function gr(e){return!e._readableCloseRequested&&\"readable\"===e._readableState}function wr(e){e._readableState=\"closed\",e._readableCloseRequested=!0,e._readableController.close()}function Sr(e,t){\"readable\"===e._readableState&&(e._readableState=\"errored\",e._readableStoredError=t),e._readableController.error(t)}function vr(e){return e._readableController.desiredSize}function Rr(e,t){\"writable\"!==e._writableState?qr(e):Tr(e,t)}function Tr(e,t){e._writableState=\"erroring\",e._writableStoredError=t,!function(e){return e._writableHasInFlightOperation}(e)&&e._writableStarted&&qr(e)}function qr(e){e._writableState=\"errored\"}function Cr(e){\"erroring\"===e._writableState&&qr(e)}Object.defineProperties(TransformStreamDefaultController.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),n(TransformStreamDefaultController.prototype.enqueue,\"enqueue\"),n(TransformStreamDefaultController.prototype.error,\"error\"),n(TransformStreamDefaultController.prototype.terminate,\"terminate\"),\"symbol\"==typeof e.toStringTag&&Object.defineProperty(TransformStreamDefaultController.prototype,e.toStringTag,{value:\"TransformStreamDefaultController\",configurable:!0});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/web-streams-polyfill/dist/ponyfill.mjs\n");

/***/ })

};
;