/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDbW93ZWUlNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDY2hhdGJvdCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj1mYWxzZSEiLCJtYXBwaW5ncyI6IkFBQUEsOEpBQStHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8/ZDJmYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXG1vd2VlXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXGNoYXRib3RcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(app-pages-browser)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _components_MemoryPanel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/MemoryPanel */ \"(app-pages-browser)/./src/components/MemoryPanel.tsx\");\n/* harmony import */ var _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useJarvisMemory */ \"(app-pages-browser)/./src/hooks/useJarvisMemory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction Home() {\n    _s();\n    const { userProfile, memoryContext, isInitialized, addMessage, updateUserInfo, getPersonalizedPrompt, getMemoryStats } = (0,_hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_5__.useJarvisMemory)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemoryPanel, setShowMemoryPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize welcome message based on memory\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && messages.length === 0) {\n            const welcomeMessage = {\n                id: \"1\",\n                content: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) ? \"Welcome back, \".concat(userProfile.name, \"! I'm Jarvis, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?\") : \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n            addMessage(welcomeMessage);\n        }\n    }, [\n        isInitialized,\n        userProfile\n    ]);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        addMessage(userMessage) // Add to memory system\n        ;\n        setIsLoading(true);\n        try {\n            // Include memory context in the request\n            const personalizedPrompt = getPersonalizedPrompt();\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages,\n                    memoryContext: personalizedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date(),\n                metadata: data.metadata\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            addMessage(assistantMessage) // Add to memory system\n            ;\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                onMemoryClick: ()=>setShowMemoryPanel(!showMemoryPanel)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-1 overflow-hidden relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            messages: messages,\n                            onSendMessage: handleSendMessage,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    showMemoryPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MemoryPanel__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        userProfile: userProfile,\n                        memoryStats: getMemoryStats(),\n                        onUpdateProfile: updateUserInfo,\n                        onClose: ()=>setShowMemoryPanel(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"3dB+vyKJP+vxT+qSB199HXUCEXw=\", false, function() {\n    return [\n        _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_5__.useJarvisMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ChatInterface; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"(app-pages-browser)/./src/components/MessageBubble.tsx\");\n/* harmony import */ var _MessageInput__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./MessageInput */ \"(app-pages-browser)/./src/components/MessageInput.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatInterface(param) {\n    let { messages, onSendMessage, isLoading } = param;\n    _s();\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [inputValue, setInputValue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isLoading\n    ]);\n    const handleSend = async ()=>{\n        if (!inputValue.trim() || isLoading) return;\n        const message = inputValue.trim();\n        setInputValue(\"\");\n        await onSendMessage(message);\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSend();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full tech-grid\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto px-4 py-6 space-y-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                message: message\n                            }, message.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 13\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 25\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            ref: messagesEndRef\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-red-500/20 bg-grok-gray/50 backdrop-blur-sm shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageInput__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        value: inputValue,\n                        onChange: setInputValue,\n                        onSend: handleSend,\n                        onKeyPress: handleKeyPress,\n                        disabled: isLoading,\n                        placeholder: \"How may I assist you today? I'm at your service...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\ChatInterface.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"oZ9YAC6ANh8M4025Uy3aLtWLW+4=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatInterface.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Header.tsx":
/*!***********************************!*\
  !*** ./src/components/Header.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction Header() {\n    let { onMemoryClick } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"scan-line\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-grok-gray border-b border-grok-muted/20 px-4 py-3 tech-grid relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-gradient-to-r from-red-600 to-yellow-500 rounded-full flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"J\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 21,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-grok-text font-semibold text-xl bg-gradient-to-r from-red-400 to-yellow-400 bg-clip-text text-transparent\",\n                                                children: \"JARVIS\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 24,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-grok-muted text-xs font-mono tracking-wider\",\n                                                children: \"ADVANCED AI ASSISTANT\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 27,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-2 text-grok-muted text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-red-500 rounded-full holographic-glow\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-mono tracking-wider\",\n                                                children: \"ONLINE\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, this),\n                                    onMemoryClick && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: onMemoryClick,\n                                        className: \"p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30\",\n                                        title: \"Memory System\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-grok-text\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: \"p-2 hover:bg-grok-dark rounded-lg transition-all duration-300 button-glow border border-transparent hover:border-red-500/30\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 text-grok-text\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 6h16M4 12h16M4 18h16\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                                lineNumber: 57,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-16 right-4 bg-grok-gray border border-red-500/30 rounded-lg shadow-lg p-2 z-50 glowing-border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"NEW CHAT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"SETTINGS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full text-left px-3 py-2 text-grok-text hover:bg-grok-dark rounded text-sm font-mono transition-all duration-200 hover:text-red-400\",\n                                children: \"ABOUT\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\Header.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"vK10R+uCyHfZ4DZVnxbYkMWJB8g=\");\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MemoryPanel.tsx":
/*!****************************************!*\
  !*** ./src/components/MemoryPanel.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MemoryPanel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction MemoryPanel(param) {\n    let { userProfile, memoryStats, onUpdateProfile, onClose } = param;\n    var _memoryStats_communicationStyle, _memoryStats_personalityMode;\n    _s();\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"\",\n        occupation: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.personalInfo.occupation) || \"\",\n        communicationStyle: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.preferences.communicationStyle) || \"friendly\",\n        personalityMode: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.preferences.personalityMode) || \"professional\"\n    });\n    const handleSave = ()=>{\n        onUpdateProfile({\n            name: editForm.name,\n            personalInfo: {\n                ...userProfile === null || userProfile === void 0 ? void 0 : userProfile.personalInfo,\n                occupation: editForm.occupation\n            },\n            preferences: {\n                ...userProfile === null || userProfile === void 0 ? void 0 : userProfile.preferences,\n                communicationStyle: editForm.communicationStyle,\n                personalityMode: editForm.personalityMode\n            }\n        });\n        setIsEditing(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-grok-gray border-l border-red-500/20 shadow-lg overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-b border-red-500/20 bg-gradient-to-r from-red-900/20 to-yellow-900/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold text-red-400 font-mono tracking-wider\",\n                                children: \"MEMORY CORE\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onClose,\n                                className: \"p-1 hover:bg-red-500/20 rounded transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 text-grok-text\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M6 18L18 6M6 6l12 12\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xs text-grok-muted font-mono mt-1\",\n                        children: \"ADVANCED AI LEARNING SYSTEM\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"SYSTEM STATUS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-xs font-mono\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"INTERESTS LEARNED:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: (memoryStats === null || memoryStats === void 0 ? void 0 : memoryStats.interests) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"ACTIVE DAYS:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: (memoryStats === null || memoryStats === void 0 ? void 0 : memoryStats.conversationDays) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"COMM STYLE:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 82,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats === null || memoryStats === void 0 ? void 0 : (_memoryStats_communicationStyle = memoryStats.communicationStyle) === null || _memoryStats_communicationStyle === void 0 ? void 0 : _memoryStats_communicationStyle.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"PERSONALITY:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: memoryStats === null || memoryStats === void 0 ? void 0 : (_memoryStats_personalityMode = memoryStats.personalityMode) === null || _memoryStats_personalityMode === void 0 ? void 0 : _memoryStats_personalityMode.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-sm font-bold text-yellow-400 font-mono\",\n                                        children: \"USER PROFILE\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsEditing(!isEditing),\n                                        className: \"text-xs text-red-400 hover:text-red-300 font-mono\",\n                                        children: isEditing ? \"CANCEL\" : \"EDIT\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"NAME:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: editForm.name,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            name: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                placeholder: \"Enter your name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"OCCUPATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: editForm.occupation,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            occupation: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                placeholder: \"Your occupation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"COMMUNICATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 129,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: editForm.communicationStyle,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            communicationStyle: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"formal\",\n                                                        children: \"FORMAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"casual\",\n                                                        children: \"CASUAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"friendly\",\n                                                        children: \"FRIENDLY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"technical\",\n                                                        children: \"TECHNICAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-xs text-grok-muted font-mono mb-1\",\n                                                children: \"PERSONALITY:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: editForm.personalityMode,\n                                                onChange: (e)=>setEditForm((prev)=>({\n                                                            ...prev,\n                                                            personalityMode: e.target.value\n                                                        })),\n                                                className: \"w-full bg-grok-dark border border-red-500/30 rounded px-2 py-1 text-xs text-grok-text font-mono\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"professional\",\n                                                        children: \"PROFESSIONAL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"witty\",\n                                                        children: \"WITTY\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"helpful\",\n                                                        children: \"HELPFUL\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"sarcastic\",\n                                                        children: \"SARCASTIC\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleSave,\n                                        className: \"w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"SAVE CHANGES\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2 text-xs font-mono\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"NAME:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) || \"NOT SET\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-grok-muted\",\n                                                children: \"OCCUPATION:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-red-400\",\n                                                children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.personalInfo.occupation) || \"NOT SET\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"LEARNED INTERESTS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-1\",\n                                children: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.personalInfo.interests.map((interest, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-2 py-1 bg-red-500/20 text-red-400 rounded text-xs font-mono\",\n                                        children: interest.toUpperCase()\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this))) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-grok-muted text-xs font-mono\",\n                                    children: \"NO INTERESTS LEARNED YET\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-grok-dark/50 rounded-lg p-3 border border-red-500/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-sm font-bold text-yellow-400 font-mono mb-2\",\n                                children: \"MEMORY ACTIONS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-yellow-600/20 hover:bg-yellow-600/30 text-yellow-400 py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"EXPORT MEMORY DATA\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"w-full bg-red-600/20 hover:bg-red-600/30 text-red-400 py-2 rounded text-xs font-mono transition-colors\",\n                                        children: \"CLEAR ALL MEMORY\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MemoryPanel.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(MemoryPanel, \"1pF2f3yS2z7DVj96guzS2s7xPZo=\");\n_c = MemoryPanel;\nvar _c;\n$RefreshReg$(_c, \"MemoryPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MemoryPanel.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MessageBubble.tsx":
/*!******************************************!*\
  !*** ./src/components/MessageBubble.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MessageBubble; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction MessageBubble(param) {\n    let { message } = param;\n    const isUser = message.role === \"user\";\n    const isAssistant = message.role === \"assistant\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isUser ? \"justify-end\" : \"justify-start\", \" \").concat(isUser ? \"message-slide-in-right\" : \"message-slide-in-left\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex max-w-[80%] \".concat(isUser ? \"flex-row-reverse\" : \"flex-row\", \" items-start space-x-3\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center shadow-lg \".concat(isUser ? \"bg-red-600 ml-3 holographic-glow\" : \"bg-gradient-to-r from-red-600 to-yellow-500 mr-3 arc-reactor avatar-pulse\"),\n                    children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-medium\",\n                        children: \"U\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col \".concat(isUser ? \"items-end\" : \"items-start\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-2xl px-4 py-3 shadow-lg transition-all duration-300 \".concat(isUser ? \"bg-gradient-to-r from-red-600 to-red-700 text-white border border-red-500/30 holographic-glow\" : \"bg-grok-gray border border-red-500/20 text-grok-text glowing-border hover:border-red-500/40\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"whitespace-pre-wrap break-words\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 13\n                                }, this),\n                                isAssistant && message.metadata && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 pt-2 border-t border-red-500/20 text-xs text-grok-muted font-mono\",\n                                    children: [\n                                        message.metadata.sources && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-1 text-red-400\",\n                                            children: [\n                                                \"SOURCES: \",\n                                                message.metadata.sources.join(\", \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 19\n                                        }, this),\n                                        message.metadata.confidence && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-yellow-400\",\n                                            children: [\n                                                \"CONFIDENCE: \",\n                                                Math.round(message.metadata.confidence * 100),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-grok-muted mt-1 font-mono tracking-wider \".concat(isUser ? \"text-right\" : \"text-left\"),\n                            children: formatTimeAgo(message.timestamp).toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageBubble.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageBubble;\n// Utility function for date formatting\nfunction formatTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return \"just now\";\n    if (diffInSeconds < 3600) return \"\".concat(Math.floor(diffInSeconds / 60), \"m ago\");\n    if (diffInSeconds < 86400) return \"\".concat(Math.floor(diffInSeconds / 3600), \"h ago\");\n    return \"\".concat(Math.floor(diffInSeconds / 86400), \"d ago\");\n}\nvar _c;\n$RefreshReg$(_c, \"MessageBubble\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MessageBubble.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/MessageInput.tsx":
/*!*****************************************!*\
  !*** ./src/components/MessageInput.tsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MessageInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction MessageInput(param) {\n    let { value, onChange, onSend, onKeyPress, disabled, placeholder = \"Type your message...\" } = param;\n    _s();\n    const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex items-end space-x-3 p-4 rounded-2xl border transition-all duration-300 shadow-lg \".concat(isFocused ? \"border-red-500 bg-grok-dark/50 glowing-border\" : \"border-red-500/20 bg-grok-gray/30 hover:border-red-500/40\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: value,\n                    onChange: (e)=>onChange(e.target.value),\n                    onKeyPress: onKeyPress,\n                    onFocus: ()=>setIsFocused(true),\n                    onBlur: ()=>setIsFocused(false),\n                    disabled: disabled,\n                    placeholder: placeholder,\n                    rows: 1,\n                    className: \"w-full bg-transparent text-grok-text placeholder-red-400/60 resize-none outline-none min-h-[24px] max-h-32 overflow-y-auto font-mono\",\n                    style: {\n                        height: \"auto\",\n                        minHeight: \"24px\"\n                    },\n                    onInput: (e)=>{\n                        const target = e.target;\n                        target.style.height = \"auto\";\n                        target.style.height = \"\".concat(Math.min(target.scrollHeight, 128), \"px\");\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onSend,\n                disabled: disabled || !value.trim(),\n                className: \"flex-shrink-0 p-3 rounded-xl transition-all duration-300 shadow-lg \".concat(disabled || !value.trim() ? \"bg-grok-muted/20 text-grok-muted cursor-not-allowed\" : \"bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 active:scale-95 button-glow border border-red-500/30\"),\n                children: disabled ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-5 h-5 border-2 border-red-400 border-t-transparent rounded-full animate-spin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            value.length > 500 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute -top-6 right-0 text-xs text-grok-muted\",\n                children: [\n                    value.length,\n                    \"/1000\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\MessageInput.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n_s(MessageInput, \"taoS6m9NZex5dx3pinefKTdpShE=\");\n_c = MessageInput;\nvar _c;\n$RefreshReg$(_c, \"MessageInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MessageInput.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./src/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ TypingIndicator; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction TypingIndicator() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start message-slide-in-left\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[80%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r from-red-600 to-yellow-500 flex items-center justify-center shadow-lg arc-reactor avatar-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-white text-sm font-bold\",\n                        children: \"J\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-grok-gray border border-red-500/20 rounded-2xl px-4 py-3 shadow-lg glowing-border\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-400 text-sm mr-2 font-mono tracking-wider\",\n                                children: \"PROCESSING\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 15,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full energy-dot shadow-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-2 text-yellow-400 text-xs font-mono\",\n                                children: \"▶\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n_c = TypingIndicator;\nvar _c;\n$RefreshReg$(_c, \"TypingIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TypingIndicator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useJarvisMemory.ts":
/*!**************************************!*\
  !*** ./src/hooks/useJarvisMemory.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useJarvisMemory: function() { return /* binding */ useJarvisMemory; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/memory-system */ \"(app-pages-browser)/./src/lib/memory-system.ts\");\n/* __next_internal_client_entry_do_not_use__ useJarvisMemory auto */ \n\nfunction useJarvisMemory() {\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [memoryContext, setMemoryContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Initialize memory system\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initializeMemory = async ()=>{\n            try {\n                const profile = await _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser();\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n                console.log(\"Jarvis memory system initialized\");\n            } catch (error) {\n                console.error(\"Failed to initialize memory system:\", error);\n                setIsInitialized(true) // Still mark as initialized to prevent infinite loading\n                ;\n            }\n        };\n        initializeMemory();\n    }, []);\n    // Add message to memory\n    const addMessage = (message)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.addMessage(message);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n        // Update profile state if it changed\n        const updatedProfile = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext();\n        if (updatedProfile) {\n            // Trigger a re-render by updating the timestamp\n            setUserProfile((prev)=>prev ? {\n                    ...prev,\n                    lastActive: new Date()\n                } : null);\n        }\n    };\n    // Update user information\n    const updateUserInfo = (updates)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.updateUserInfo(updates);\n        setUserProfile((prev)=>prev ? {\n                ...prev,\n                ...updates\n            } : null);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n    };\n    // Get personalized system prompt\n    const getPersonalizedPrompt = ()=>{\n        if (!memoryContext) return \"\";\n        const conversationSummary = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getConversationSummary();\n        return \"\\n\".concat(conversationSummary, \"\\n\\nPERSONALIZATION INSTRUCTIONS:\\n- Adapt your communication style to match the user's preference: \").concat(memoryContext.userPreferences.communicationStyle, \"\\n- Provide \").concat(memoryContext.userPreferences.responseLength, \" responses\\n- Reference their interests when relevant: \").concat(memoryContext.recentTopics.join(\", \"), \"\\n- Maintain the \").concat(memoryContext.userPreferences.personalityMode, \" personality mode\\n- Use context from previous conversations when appropriate\\n- Be more personal and contextually aware in your responses\\n    \").trim();\n    };\n    // Clear all memory\n    const clearMemory = ()=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.clearMemory();\n        setUserProfile(null);\n        setMemoryContext(null);\n        setIsInitialized(false);\n        // Reinitialize\n        setTimeout(()=>{\n            _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser().then((profile)=>{\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n            });\n        }, 100);\n    };\n    // Get memory stats for display\n    const getMemoryStats = ()=>{\n        if (!userProfile || !memoryContext) return null;\n        return {\n            interests: userProfile.personalInfo.interests.length,\n            conversationDays: Math.floor((new Date().getTime() - new Date(userProfile.createdAt).getTime()) / (1000 * 60 * 60 * 24)),\n            communicationStyle: userProfile.preferences.communicationStyle,\n            personalityMode: userProfile.preferences.personalityMode,\n            lastActive: userProfile.lastActive\n        };\n    };\n    return {\n        userProfile,\n        memoryContext,\n        isInitialized,\n        addMessage,\n        updateUserInfo,\n        getPersonalizedPrompt,\n        clearMemory,\n        getMemoryStats\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useJarvisMemory.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/memory-system.ts":
/*!**********************************!*\
  !*** ./src/lib/memory-system.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jarvisMemory: function() { return /* binding */ jarvisMemory; }\n/* harmony export */ });\n// Jarvis Memory System - Advanced AI Memory and Learning\nclass JarvisMemorySystem {\n    // Initialize or load user profile\n    async initializeUser() {\n        let userId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"default\";\n        // Try to load existing profile from localStorage\n        const stored = localStorage.getItem(\"jarvis_profile_\".concat(userId));\n        if (stored) {\n            var _this_userProfile;\n            this.userProfile = JSON.parse(stored);\n            console.log(\"Loaded existing user profile:\", ((_this_userProfile = this.userProfile) === null || _this_userProfile === void 0 ? void 0 : _this_userProfile.name) || \"Anonymous\");\n        } else {\n            // Create new profile\n            this.userProfile = {\n                id: userId,\n                preferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                personalInfo: {\n                    interests: []\n                },\n                conversationHistory: [],\n                learningData: [],\n                createdAt: new Date(),\n                lastActive: new Date()\n            };\n            this.saveProfile();\n            console.log(\"Created new user profile\");\n        }\n        return this.userProfile;\n    }\n    // Save profile to localStorage\n    saveProfile() {\n        if (this.userProfile) {\n            localStorage.setItem(\"jarvis_profile_\".concat(this.userProfile.id), JSON.stringify(this.userProfile));\n        }\n    }\n    // Add message to conversation buffer\n    addMessage(message) {\n        this.conversationBuffer.push(message);\n        // Keep only last 20 messages in buffer\n        if (this.conversationBuffer.length > 20) {\n            this.conversationBuffer = this.conversationBuffer.slice(-20);\n        }\n        // Learn from user messages\n        if (message.role === \"user\") {\n            this.learnFromMessage(message.content);\n        }\n    }\n    // Extract learning patterns from user messages\n    learnFromMessage(content) {\n        if (!this.userProfile) return;\n        const words = content.toLowerCase().split(/\\s+/);\n        const topics = this.extractTopics(content);\n        const mood = this.detectMood(content);\n        // Update topics of interest\n        topics.forEach((topic)=>{\n            if (!this.userProfile.personalInfo.interests.includes(topic)) {\n                this.userProfile.personalInfo.interests.push(topic);\n            }\n        });\n        // Update communication style based on patterns\n        this.updateCommunicationStyle(content);\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Extract topics from message content\n    extractTopics(content) {\n        const topicKeywords = {\n            \"technology\": [\n                \"tech\",\n                \"computer\",\n                \"software\",\n                \"programming\",\n                \"ai\",\n                \"code\",\n                \"development\"\n            ],\n            \"science\": [\n                \"science\",\n                \"research\",\n                \"experiment\",\n                \"theory\",\n                \"physics\",\n                \"chemistry\",\n                \"biology\"\n            ],\n            \"business\": [\n                \"business\",\n                \"work\",\n                \"job\",\n                \"career\",\n                \"company\",\n                \"meeting\",\n                \"project\"\n            ],\n            \"entertainment\": [\n                \"movie\",\n                \"music\",\n                \"game\",\n                \"book\",\n                \"show\",\n                \"entertainment\",\n                \"fun\"\n            ],\n            \"health\": [\n                \"health\",\n                \"fitness\",\n                \"exercise\",\n                \"diet\",\n                \"medical\",\n                \"wellness\"\n            ],\n            \"travel\": [\n                \"travel\",\n                \"trip\",\n                \"vacation\",\n                \"country\",\n                \"city\",\n                \"flight\",\n                \"hotel\"\n            ],\n            \"food\": [\n                \"food\",\n                \"recipe\",\n                \"cooking\",\n                \"restaurant\",\n                \"meal\",\n                \"cuisine\"\n            ],\n            \"sports\": [\n                \"sport\",\n                \"football\",\n                \"basketball\",\n                \"soccer\",\n                \"tennis\",\n                \"game\",\n                \"team\"\n            ]\n        };\n        const topics = [];\n        const lowerContent = content.toLowerCase();\n        Object.entries(topicKeywords).forEach((param)=>{\n            let [topic, keywords] = param;\n            if (keywords.some((keyword)=>lowerContent.includes(keyword))) {\n                topics.push(topic);\n            }\n        });\n        return topics;\n    }\n    // Detect user mood from message\n    detectMood(content) {\n        const positiveWords = [\n            \"great\",\n            \"awesome\",\n            \"excellent\",\n            \"love\",\n            \"amazing\",\n            \"perfect\",\n            \"wonderful\"\n        ];\n        const negativeWords = [\n            \"bad\",\n            \"terrible\",\n            \"awful\",\n            \"hate\",\n            \"frustrated\",\n            \"annoying\",\n            \"problem\"\n        ];\n        const excitedWords = [\n            \"excited\",\n            \"can't wait\",\n            \"!\",\n            \"wow\",\n            \"incredible\",\n            \"fantastic\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (excitedWords.some((word)=>lowerContent.includes(word))) return \"excited\";\n        if (positiveWords.some((word)=>lowerContent.includes(word))) return \"positive\";\n        if (negativeWords.some((word)=>lowerContent.includes(word))) return \"frustrated\";\n        return \"neutral\";\n    }\n    // Update communication style based on user patterns\n    updateCommunicationStyle(content) {\n        if (!this.userProfile) return;\n        const formalIndicators = [\n            \"please\",\n            \"thank you\",\n            \"could you\",\n            \"would you\"\n        ];\n        const casualIndicators = [\n            \"hey\",\n            \"what's up\",\n            \"cool\",\n            \"awesome\",\n            \"yeah\"\n        ];\n        const technicalIndicators = [\n            \"algorithm\",\n            \"function\",\n            \"variable\",\n            \"database\",\n            \"api\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (technicalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"technical\";\n        } else if (formalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"formal\";\n        } else if (casualIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"casual\";\n        }\n    }\n    // Get memory context for AI responses\n    getMemoryContext() {\n        if (!this.userProfile) {\n            return {\n                recentTopics: [],\n                userPreferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                relevantHistory: [],\n                personalContext: []\n            };\n        }\n        const recentTopics = this.userProfile.personalInfo.interests.slice(-5);\n        const relevantHistory = this.conversationBuffer.slice(-5).map((msg)=>\"\".concat(msg.role, \": \").concat(msg.content.substring(0, 100)));\n        const personalContext = [\n            this.userProfile.name ? \"User's name: \".concat(this.userProfile.name) : \"\",\n            this.userProfile.personalInfo.occupation ? \"Occupation: \".concat(this.userProfile.personalInfo.occupation) : \"\",\n            \"Interests: \".concat(this.userProfile.personalInfo.interests.join(\", \")),\n            \"Communication style: \".concat(this.userProfile.preferences.communicationStyle),\n            \"Preferred response length: \".concat(this.userProfile.preferences.responseLength)\n        ].filter(Boolean);\n        return {\n            recentTopics,\n            userPreferences: this.userProfile.preferences,\n            relevantHistory,\n            personalContext\n        };\n    }\n    // Update user information\n    updateUserInfo(updates) {\n        if (!this.userProfile) return;\n        this.userProfile = {\n            ...this.userProfile,\n            ...updates\n        };\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Get conversation summary\n    getConversationSummary() {\n        const context = this.getMemoryContext();\n        return \"\\nMEMORY CONTEXT:\\n- User interests: \".concat(context.recentTopics.join(\", \") || \"None recorded yet\", \"\\n- Communication style: \").concat(context.userPreferences.communicationStyle, \"\\n- Preferred response length: \").concat(context.userPreferences.responseLength, \"\\n- Personal context: \").concat(context.personalContext.join(\"; \"), \"\\n- Recent conversation: \").concat(context.relevantHistory.slice(-3).join(\"; \"), \"\\n\\nUse this context to provide personalized, contextually aware responses.\\n    \").trim();\n    }\n    // Clear memory (for privacy)\n    clearMemory() {\n        if (this.userProfile) {\n            localStorage.removeItem(\"jarvis_profile_\".concat(this.userProfile.id));\n        }\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n    constructor(){\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n}\n// Export singleton instance\nconst jarvisMemory = new JarvisMemorySystem();\n/* harmony default export */ __webpack_exports__[\"default\"] = (jarvisMemory);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/memory-system.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7O0FBRWIsSUFBSSxJQUFxQztBQUN6QztBQUNBOztBQUVBLFlBQVksbUJBQU8sQ0FBQyxzR0FBMEI7O0FBRTlDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdEQUF3RDs7QUFFeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGlHQUFpRyxlQUFlO0FBQ2hIO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLE1BQU07OztBQUdOO0FBQ0E7QUFDQSxLQUFLLEdBQUc7O0FBRVIsa0RBQWtEO0FBQ2xEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLDRCQUE0QjtBQUM1QjtBQUNBLHFDQUFxQzs7QUFFckMsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsRUFBRTs7O0FBR0Y7QUFDQTtBQUNBOztBQUVBLHFFQUFxRTs7QUFFckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0dBQWtHOztBQUVsRztBQUNBO0FBQ0EsRUFBRTs7O0FBR0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0NBQXdDO0FBQ3hDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7OztBQUdKO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxpQ0FBaUM7O0FBRWpDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1Qzs7QUFFdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTOztBQUVUO0FBQ0Esc0JBQXNCO0FBQ3RCO0FBQ0EsU0FBUztBQUNULHVCQUF1QjtBQUN2QjtBQUNBLFNBQVM7QUFDVCx1QkFBdUI7QUFDdkI7QUFDQSxTQUFTO0FBQ1Qsd0JBQXdCO0FBQ3hCO0FBQ0EsU0FBUztBQUNULHdCQUF3QjtBQUN4QjtBQUNBLFNBQVM7QUFDVCxpQ0FBaUM7QUFDakM7QUFDQSxTQUFTO0FBQ1QsMkJBQTJCO0FBQzNCO0FBQ0EsU0FBUztBQUNULE9BQU87QUFDUDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxNQUFNOzs7QUFHTjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkRBQTJEOztBQUUzRDtBQUNBOztBQUVBO0FBQ0EseURBQXlEO0FBQ3pEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhOzs7QUFHYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXOztBQUVYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0EsY0FBYzs7O0FBR2Q7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7OztBQUdBLG1DQUFtQztBQUNuQztBQUNBO0FBQ0E7O0FBRUE7QUFDQSw2Q0FBNkM7QUFDN0M7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0EsZ0hBQWdIOztBQUVoSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsYUFBYSxrQkFBa0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjs7QUFFQTtBQUNBO0FBQ0EsZ0ZBQWdGO0FBQ2hGO0FBQ0E7OztBQUdBO0FBQ0E7QUFDQTs7QUFFQSxvQkFBb0IsSUFBSTtBQUN4QjtBQUNBO0FBQ0E7QUFDQSxrQkFBa0I7OztBQUdsQjtBQUNBO0FBQ0EsY0FBYztBQUNkOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsSUFBSTs7O0FBR0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEdBQUc7QUFDZCxXQUFXLEdBQUc7QUFDZCxXQUFXLEdBQUc7QUFDZCxXQUFXLGVBQWU7QUFDMUIsV0FBVyxHQUFHO0FBQ2QsV0FBVyxHQUFHO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEdBQUc7QUFDZDtBQUNBO0FBQ0E7OztBQUdBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSyxHQUFHOztBQUVSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsR0FBRztBQUNkLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7O0FBRUE7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHFCQUFxQjtBQUNqRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLE1BQU07OztBQUdOO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLDhDQUE4QyxnREFBZ0QsTUFBTSxhQUFhOztBQUVqSDtBQUNBLCtDQUErQyxrQ0FBa0MsT0FBTzs7QUFFeEYsdUdBQXVHLGNBQWMsVUFBVSxnR0FBZ0csa0JBQWtCLFVBQVUsVUFBVTs7QUFFclE7QUFDQTtBQUNBOztBQUVBLGtCQUFrQjs7QUFFbEI7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQiwyREFBMkQsVUFBVTtBQUNyRSx5QkFBeUIsVUFBVTtBQUNuQztBQUNBLGFBQWEsVUFBVTtBQUN2Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxNQUFNOzs7QUFHTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTs7O0FBR047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFdBQVc7QUFDdEIsV0FBVyxHQUFHO0FBQ2Q7OztBQUdBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsb0RBQW9EO0FBQ3BELHNCQUFzQixpQkFBaUI7QUFDdkM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksU0FBUztBQUNyQjtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekIsV0FBVyxHQUFHO0FBQ2Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSw2REFBNkQ7QUFDN0Q7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7OztBQUdBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLG9CQUFvQixpQkFBaUI7QUFDckM7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGdCQUFnQjtBQUNoQixjQUFjO0FBQ2QsR0FBRztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcz80YTY1Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2UgUmVhY3RcbiAqIHJlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qc1xuICpcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gIChmdW5jdGlvbigpIHtcbid1c2Ugc3RyaWN0JztcblxudmFyIFJlYWN0ID0gcmVxdWlyZShcIm5leHQvZGlzdC9jb21waWxlZC9yZWFjdFwiKTtcblxuLy8gQVRURU5USU9OXG4vLyBXaGVuIGFkZGluZyBuZXcgc3ltYm9scyB0byB0aGlzIGZpbGUsXG4vLyBQbGVhc2UgY29uc2lkZXIgYWxzbyBhZGRpbmcgdG8gJ3JlYWN0LWRldnRvb2xzLXNoYXJlZC9zcmMvYmFja2VuZC9SZWFjdFN5bWJvbHMnXG4vLyBUaGUgU3ltYm9sIHVzZWQgdG8gdGFnIHRoZSBSZWFjdEVsZW1lbnQtbGlrZSB0eXBlcy5cbnZhciBSRUFDVF9FTEVNRU5UX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5lbGVtZW50Jyk7XG52YXIgUkVBQ1RfUE9SVEFMX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5wb3J0YWwnKTtcbnZhciBSRUFDVF9GUkFHTUVOVF9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QuZnJhZ21lbnQnKTtcbnZhciBSRUFDVF9TVFJJQ1RfTU9ERV9UWVBFID0gU3ltYm9sLmZvcigncmVhY3Quc3RyaWN0X21vZGUnKTtcbnZhciBSRUFDVF9QUk9GSUxFUl9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QucHJvZmlsZXInKTtcbnZhciBSRUFDVF9QUk9WSURFUl9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QucHJvdmlkZXInKTsgLy8gVE9ETzogRGVsZXRlIHdpdGggZW5hYmxlUmVuZGVyYWJsZUNvbnRleHRcblxudmFyIFJFQUNUX0NPTlNVTUVSX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5jb25zdW1lcicpO1xudmFyIFJFQUNUX0NPTlRFWFRfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LmNvbnRleHQnKTtcbnZhciBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFID0gU3ltYm9sLmZvcigncmVhY3QuZm9yd2FyZF9yZWYnKTtcbnZhciBSRUFDVF9TVVNQRU5TRV9UWVBFID0gU3ltYm9sLmZvcigncmVhY3Quc3VzcGVuc2UnKTtcbnZhciBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5zdXNwZW5zZV9saXN0Jyk7XG52YXIgUkVBQ1RfTUVNT19UWVBFID0gU3ltYm9sLmZvcigncmVhY3QubWVtbycpO1xudmFyIFJFQUNUX0xBWllfVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0LmxhenknKTtcbnZhciBSRUFDVF9PRkZTQ1JFRU5fVFlQRSA9IFN5bWJvbC5mb3IoJ3JlYWN0Lm9mZnNjcmVlbicpO1xudmFyIFJFQUNUX0NBQ0hFX1RZUEUgPSBTeW1ib2wuZm9yKCdyZWFjdC5jYWNoZScpO1xudmFyIE1BWUJFX0lURVJBVE9SX1NZTUJPTCA9IFN5bWJvbC5pdGVyYXRvcjtcbnZhciBGQVVYX0lURVJBVE9SX1NZTUJPTCA9ICdAQGl0ZXJhdG9yJztcbmZ1bmN0aW9uIGdldEl0ZXJhdG9yRm4obWF5YmVJdGVyYWJsZSkge1xuICBpZiAobWF5YmVJdGVyYWJsZSA9PT0gbnVsbCB8fCB0eXBlb2YgbWF5YmVJdGVyYWJsZSAhPT0gJ29iamVjdCcpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHZhciBtYXliZUl0ZXJhdG9yID0gTUFZQkVfSVRFUkFUT1JfU1lNQk9MICYmIG1heWJlSXRlcmFibGVbTUFZQkVfSVRFUkFUT1JfU1lNQk9MXSB8fCBtYXliZUl0ZXJhYmxlW0ZBVVhfSVRFUkFUT1JfU1lNQk9MXTtcblxuICBpZiAodHlwZW9mIG1heWJlSXRlcmF0b3IgPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gbWF5YmVJdGVyYXRvcjtcbiAgfVxuXG4gIHJldHVybiBudWxsO1xufVxuXG52YXIgUmVhY3RTaGFyZWRJbnRlcm5hbHMgPSBSZWFjdC5fX1NFQ1JFVF9JTlRFUk5BTFNfRE9fTk9UX1VTRV9PUl9ZT1VfV0lMTF9CRV9GSVJFRDtcblxuZnVuY3Rpb24gZXJyb3IoZm9ybWF0KSB7XG4gIHtcbiAgICB7XG4gICAgICBmb3IgKHZhciBfbGVuMiA9IGFyZ3VtZW50cy5sZW5ndGgsIGFyZ3MgPSBuZXcgQXJyYXkoX2xlbjIgPiAxID8gX2xlbjIgLSAxIDogMCksIF9rZXkyID0gMTsgX2tleTIgPCBfbGVuMjsgX2tleTIrKykge1xuICAgICAgICBhcmdzW19rZXkyIC0gMV0gPSBhcmd1bWVudHNbX2tleTJdO1xuICAgICAgfVxuXG4gICAgICBwcmludFdhcm5pbmcoJ2Vycm9yJywgZm9ybWF0LCBhcmdzKTtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gcHJpbnRXYXJuaW5nKGxldmVsLCBmb3JtYXQsIGFyZ3MpIHtcbiAgLy8gV2hlbiBjaGFuZ2luZyB0aGlzIGxvZ2ljLCB5b3UgbWlnaHQgd2FudCB0byBhbHNvXG4gIC8vIHVwZGF0ZSBjb25zb2xlV2l0aFN0YWNrRGV2Lnd3dy5qcyBhcyB3ZWxsLlxuICB7XG4gICAgdmFyIFJlYWN0RGVidWdDdXJyZW50RnJhbWUgPSBSZWFjdFNoYXJlZEludGVybmFscy5SZWFjdERlYnVnQ3VycmVudEZyYW1lO1xuICAgIHZhciBzdGFjayA9IFJlYWN0RGVidWdDdXJyZW50RnJhbWUuZ2V0U3RhY2tBZGRlbmR1bSgpO1xuXG4gICAgaWYgKHN0YWNrICE9PSAnJykge1xuICAgICAgZm9ybWF0ICs9ICclcyc7XG4gICAgICBhcmdzID0gYXJncy5jb25jYXQoW3N0YWNrXSk7XG4gICAgfSAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaW50ZXJuYWwvc2FmZS1zdHJpbmctY29lcmNpb25cblxuXG4gICAgdmFyIGFyZ3NXaXRoRm9ybWF0ID0gYXJncy5tYXAoZnVuY3Rpb24gKGl0ZW0pIHtcbiAgICAgIHJldHVybiBTdHJpbmcoaXRlbSk7XG4gICAgfSk7IC8vIENhcmVmdWw6IFJOIGN1cnJlbnRseSBkZXBlbmRzIG9uIHRoaXMgcHJlZml4XG5cbiAgICBhcmdzV2l0aEZvcm1hdC51bnNoaWZ0KCdXYXJuaW5nOiAnICsgZm9ybWF0KTsgLy8gV2UgaW50ZW50aW9uYWxseSBkb24ndCB1c2Ugc3ByZWFkIChvciAuYXBwbHkpIGRpcmVjdGx5IGJlY2F1c2UgaXRcbiAgICAvLyBicmVha3MgSUU5OiBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvaXNzdWVzLzEzNjEwXG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZ1xuXG4gICAgRnVuY3Rpb24ucHJvdG90eXBlLmFwcGx5LmNhbGwoY29uc29sZVtsZXZlbF0sIGNvbnNvbGUsIGFyZ3NXaXRoRm9ybWF0KTtcbiAgfVxufVxuXG4vLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuXG52YXIgZW5hYmxlU2NvcGVBUEkgPSBmYWxzZTsgLy8gRXhwZXJpbWVudGFsIENyZWF0ZSBFdmVudCBIYW5kbGUgQVBJLlxudmFyIGVuYWJsZUNhY2hlRWxlbWVudCA9IGZhbHNlO1xudmFyIGVuYWJsZVRyYW5zaXRpb25UcmFjaW5nID0gZmFsc2U7IC8vIE5vIGtub3duIGJ1Z3MsIGJ1dCBuZWVkcyBwZXJmb3JtYW5jZSB0ZXN0aW5nXG5cbnZhciBlbmFibGVMZWdhY3lIaWRkZW4gPSBmYWxzZTsgLy8gRW5hYmxlcyB1bnN0YWJsZV9hdm9pZFRoaXNGYWxsYmFjayBmZWF0dXJlIGluIEZpYmVyXG52YXIgZW5hYmxlUmVuZGVyYWJsZUNvbnRleHQgPSBmYWxzZTtcbi8vIHN0dWZmLiBJbnRlbmRlZCB0byBlbmFibGUgUmVhY3QgY29yZSBtZW1iZXJzIHRvIG1vcmUgZWFzaWx5IGRlYnVnIHNjaGVkdWxpbmdcbi8vIGlzc3VlcyBpbiBERVYgYnVpbGRzLlxuXG52YXIgZW5hYmxlRGVidWdUcmFjaW5nID0gZmFsc2U7XG5cbmZ1bmN0aW9uIGdldFdyYXBwZWROYW1lKG91dGVyVHlwZSwgaW5uZXJUeXBlLCB3cmFwcGVyTmFtZSkge1xuICB2YXIgZGlzcGxheU5hbWUgPSBvdXRlclR5cGUuZGlzcGxheU5hbWU7XG5cbiAgaWYgKGRpc3BsYXlOYW1lKSB7XG4gICAgcmV0dXJuIGRpc3BsYXlOYW1lO1xuICB9XG5cbiAgdmFyIGZ1bmN0aW9uTmFtZSA9IGlubmVyVHlwZS5kaXNwbGF5TmFtZSB8fCBpbm5lclR5cGUubmFtZSB8fCAnJztcbiAgcmV0dXJuIGZ1bmN0aW9uTmFtZSAhPT0gJycgPyB3cmFwcGVyTmFtZSArIFwiKFwiICsgZnVuY3Rpb25OYW1lICsgXCIpXCIgOiB3cmFwcGVyTmFtZTtcbn0gLy8gS2VlcCBpbiBzeW5jIHdpdGggcmVhY3QtcmVjb25jaWxlci9nZXRDb21wb25lbnROYW1lRnJvbUZpYmVyXG5cblxuZnVuY3Rpb24gZ2V0Q29udGV4dE5hbWUodHlwZSkge1xuICByZXR1cm4gdHlwZS5kaXNwbGF5TmFtZSB8fCAnQ29udGV4dCc7XG59XG5cbnZhciBSRUFDVF9DTElFTlRfUkVGRVJFTkNFJDIgPSBTeW1ib2wuZm9yKCdyZWFjdC5jbGllbnQucmVmZXJlbmNlJyk7IC8vIE5vdGUgdGhhdCB0aGUgcmVjb25jaWxlciBwYWNrYWdlIHNob3VsZCBnZW5lcmFsbHkgcHJlZmVyIHRvIHVzZSBnZXRDb21wb25lbnROYW1lRnJvbUZpYmVyKCkgaW5zdGVhZC5cblxuZnVuY3Rpb24gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpIHtcbiAgaWYgKHR5cGUgPT0gbnVsbCkge1xuICAgIC8vIEhvc3Qgcm9vdCwgdGV4dCBub2RlIG9yIGp1c3QgaW52YWxpZCB0eXBlLlxuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgaWYgKHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UkMikge1xuICAgICAgLy8gVE9ETzogQ3JlYXRlIGEgY29udmVudGlvbiBmb3IgbmFtaW5nIGNsaWVudCByZWZlcmVuY2VzIHdpdGggZGVidWcgaW5mby5cbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cblxuICAgIHJldHVybiB0eXBlLmRpc3BsYXlOYW1lIHx8IHR5cGUubmFtZSB8fCBudWxsO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJykge1xuICAgIHJldHVybiB0eXBlO1xuICB9XG5cbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSBSRUFDVF9GUkFHTUVOVF9UWVBFOlxuICAgICAgcmV0dXJuICdGcmFnbWVudCc7XG5cbiAgICBjYXNlIFJFQUNUX1BPUlRBTF9UWVBFOlxuICAgICAgcmV0dXJuICdQb3J0YWwnO1xuXG4gICAgY2FzZSBSRUFDVF9QUk9GSUxFUl9UWVBFOlxuICAgICAgcmV0dXJuICdQcm9maWxlcic7XG5cbiAgICBjYXNlIFJFQUNUX1NUUklDVF9NT0RFX1RZUEU6XG4gICAgICByZXR1cm4gJ1N0cmljdE1vZGUnO1xuXG4gICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgcmV0dXJuICdTdXNwZW5zZSc7XG5cbiAgICBjYXNlIFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRTpcbiAgICAgIHJldHVybiAnU3VzcGVuc2VMaXN0JztcblxuICAgIGNhc2UgUkVBQ1RfQ0FDSEVfVFlQRTpcbiAgICAgIHtcbiAgICAgICAgcmV0dXJuICdDYWNoZSc7XG4gICAgICB9XG5cbiAgfVxuXG4gIGlmICh0eXBlb2YgdHlwZSA9PT0gJ29iamVjdCcpIHtcbiAgICB7XG4gICAgICBpZiAodHlwZW9mIHR5cGUudGFnID09PSAnbnVtYmVyJykge1xuICAgICAgICBlcnJvcignUmVjZWl2ZWQgYW4gdW5leHBlY3RlZCBvYmplY3QgaW4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKCkuICcgKyAnVGhpcyBpcyBsaWtlbHkgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLicpO1xuICAgICAgfVxuICAgIH1cblxuICAgIHN3aXRjaCAodHlwZS4kJHR5cGVvZikge1xuICAgICAgY2FzZSBSRUFDVF9QUk9WSURFUl9UWVBFOlxuICAgICAgICB7XG4gICAgICAgICAgdmFyIHByb3ZpZGVyID0gdHlwZTtcbiAgICAgICAgICByZXR1cm4gZ2V0Q29udGV4dE5hbWUocHJvdmlkZXIuX2NvbnRleHQpICsgJy5Qcm92aWRlcic7XG4gICAgICAgIH1cblxuICAgICAgY2FzZSBSRUFDVF9DT05URVhUX1RZUEU6XG4gICAgICAgIHZhciBjb250ZXh0ID0gdHlwZTtcblxuICAgICAgICB7XG4gICAgICAgICAgcmV0dXJuIGdldENvbnRleHROYW1lKGNvbnRleHQpICsgJy5Db25zdW1lcic7XG4gICAgICAgIH1cblxuICAgICAgY2FzZSBSRUFDVF9DT05TVU1FUl9UWVBFOlxuICAgICAgICB7XG4gICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cblxuICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICByZXR1cm4gZ2V0V3JhcHBlZE5hbWUodHlwZSwgdHlwZS5yZW5kZXIsICdGb3J3YXJkUmVmJyk7XG5cbiAgICAgIGNhc2UgUkVBQ1RfTUVNT19UWVBFOlxuICAgICAgICB2YXIgb3V0ZXJOYW1lID0gdHlwZS5kaXNwbGF5TmFtZSB8fCBudWxsO1xuXG4gICAgICAgIGlmIChvdXRlck5hbWUgIT09IG51bGwpIHtcbiAgICAgICAgICByZXR1cm4gb3V0ZXJOYW1lO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8ICdNZW1vJztcblxuICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgIHtcbiAgICAgICAgICB2YXIgbGF6eUNvbXBvbmVudCA9IHR5cGU7XG4gICAgICAgICAgdmFyIHBheWxvYWQgPSBsYXp5Q29tcG9uZW50Ll9wYXlsb2FkO1xuICAgICAgICAgIHZhciBpbml0ID0gbGF6eUNvbXBvbmVudC5faW5pdDtcblxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICByZXR1cm4gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKGluaXQocGF5bG9hZCkpO1xuICAgICAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBudWxsO1xufVxuXG4vLyAkRmxvd0ZpeE1lW21ldGhvZC11bmJpbmRpbmddXG52YXIgaGFzT3duUHJvcGVydHkgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xuXG52YXIgYXNzaWduID0gT2JqZWN0LmFzc2lnbjtcblxuLypcbiAqIFRoZSBgJycgKyB2YWx1ZWAgcGF0dGVybiAodXNlZCBpbiBwZXJmLXNlbnNpdGl2ZSBjb2RlKSB0aHJvd3MgZm9yIFN5bWJvbFxuICogYW5kIFRlbXBvcmFsLiogdHlwZXMuIFNlZSBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvcHVsbC8yMjA2NC5cbiAqXG4gKiBUaGUgZnVuY3Rpb25zIGluIHRoaXMgbW9kdWxlIHdpbGwgdGhyb3cgYW4gZWFzaWVyLXRvLXVuZGVyc3RhbmQsXG4gKiBlYXNpZXItdG8tZGVidWcgZXhjZXB0aW9uIHdpdGggYSBjbGVhciBlcnJvcnMgbWVzc2FnZSBtZXNzYWdlIGV4cGxhaW5pbmcgdGhlXG4gKiBwcm9ibGVtLiAoSW5zdGVhZCBvZiBhIGNvbmZ1c2luZyBleGNlcHRpb24gdGhyb3duIGluc2lkZSB0aGUgaW1wbGVtZW50YXRpb25cbiAqIG9mIHRoZSBgdmFsdWVgIG9iamVjdCkuXG4gKi9cbi8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXJldHVybl0gb25seSBjYWxsZWQgaW4gREVWLCBzbyB2b2lkIHJldHVybiBpcyBub3QgcG9zc2libGUuXG5mdW5jdGlvbiB0eXBlTmFtZSh2YWx1ZSkge1xuICB7XG4gICAgLy8gdG9TdHJpbmdUYWcgaXMgbmVlZGVkIGZvciBuYW1lc3BhY2VkIHR5cGVzIGxpa2UgVGVtcG9yYWwuSW5zdGFudFxuICAgIHZhciBoYXNUb1N0cmluZ1RhZyA9IHR5cGVvZiBTeW1ib2wgPT09ICdmdW5jdGlvbicgJiYgU3ltYm9sLnRvU3RyaW5nVGFnO1xuICAgIHZhciB0eXBlID0gaGFzVG9TdHJpbmdUYWcgJiYgdmFsdWVbU3ltYm9sLnRvU3RyaW5nVGFnXSB8fCB2YWx1ZS5jb25zdHJ1Y3Rvci5uYW1lIHx8ICdPYmplY3QnOyAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dXG5cbiAgICByZXR1cm4gdHlwZTtcbiAgfVxufSAvLyAkRmxvd0ZpeE1lW2luY29tcGF0aWJsZS1yZXR1cm5dIG9ubHkgY2FsbGVkIGluIERFViwgc28gdm9pZCByZXR1cm4gaXMgbm90IHBvc3NpYmxlLlxuXG5cbmZ1bmN0aW9uIHdpbGxDb2VyY2lvblRocm93KHZhbHVlKSB7XG4gIHtcbiAgICB0cnkge1xuICAgICAgdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gdGVzdFN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gIC8vIElmIHlvdSBlbmRlZCB1cCBoZXJlIGJ5IGZvbGxvd2luZyBhbiBleGNlcHRpb24gY2FsbCBzdGFjaywgaGVyZSdzIHdoYXQnc1xuICAvLyBoYXBwZW5lZDogeW91IHN1cHBsaWVkIGFuIG9iamVjdCBvciBzeW1ib2wgdmFsdWUgdG8gUmVhY3QgKGFzIGEgcHJvcCwga2V5LFxuICAvLyBET00gYXR0cmlidXRlLCBDU1MgcHJvcGVydHksIHN0cmluZyByZWYsIGV0Yy4pIGFuZCB3aGVuIFJlYWN0IHRyaWVkIHRvXG4gIC8vIGNvZXJjZSBpdCB0byBhIHN0cmluZyB1c2luZyBgJycgKyB2YWx1ZWAsIGFuIGV4Y2VwdGlvbiB3YXMgdGhyb3duLlxuICAvL1xuICAvLyBUaGUgbW9zdCBjb21tb24gdHlwZXMgdGhhdCB3aWxsIGNhdXNlIHRoaXMgZXhjZXB0aW9uIGFyZSBgU3ltYm9sYCBpbnN0YW5jZXNcbiAgLy8gYW5kIFRlbXBvcmFsIG9iamVjdHMgbGlrZSBgVGVtcG9yYWwuSW5zdGFudGAuIEJ1dCBhbnkgb2JqZWN0IHRoYXQgaGFzIGFcbiAgLy8gYHZhbHVlT2ZgIG9yIGBbU3ltYm9sLnRvUHJpbWl0aXZlXWAgbWV0aG9kIHRoYXQgdGhyb3dzIHdpbGwgYWxzbyBjYXVzZSB0aGlzXG4gIC8vIGV4Y2VwdGlvbi4gKExpYnJhcnkgYXV0aG9ycyBkbyB0aGlzIHRvIHByZXZlbnQgdXNlcnMgZnJvbSB1c2luZyBidWlsdC1pblxuICAvLyBudW1lcmljIG9wZXJhdG9ycyBsaWtlIGArYCBvciBjb21wYXJpc29uIG9wZXJhdG9ycyBsaWtlIGA+PWAgYmVjYXVzZSBjdXN0b21cbiAgLy8gbWV0aG9kcyBhcmUgbmVlZGVkIHRvIHBlcmZvcm0gYWNjdXJhdGUgYXJpdGhtZXRpYyBvciBjb21wYXJpc29uLilcbiAgLy9cbiAgLy8gVG8gZml4IHRoZSBwcm9ibGVtLCBjb2VyY2UgdGhpcyBvYmplY3Qgb3Igc3ltYm9sIHZhbHVlIHRvIGEgc3RyaW5nIGJlZm9yZVxuICAvLyBwYXNzaW5nIGl0IHRvIFJlYWN0LiBUaGUgbW9zdCByZWxpYWJsZSB3YXkgaXMgdXN1YWxseSBgU3RyaW5nKHZhbHVlKWAuXG4gIC8vXG4gIC8vIFRvIGZpbmQgd2hpY2ggdmFsdWUgaXMgdGhyb3dpbmcsIGNoZWNrIHRoZSBicm93c2VyIG9yIGRlYnVnZ2VyIGNvbnNvbGUuXG4gIC8vIEJlZm9yZSB0aGlzIGV4Y2VwdGlvbiB3YXMgdGhyb3duLCB0aGVyZSBzaG91bGQgYmUgYGNvbnNvbGUuZXJyb3JgIG91dHB1dFxuICAvLyB0aGF0IHNob3dzIHRoZSB0eXBlIChTeW1ib2wsIFRlbXBvcmFsLlBsYWluRGF0ZSwgZXRjLikgdGhhdCBjYXVzZWQgdGhlXG4gIC8vIHByb2JsZW0gYW5kIGhvdyB0aGF0IHR5cGUgd2FzIHVzZWQ6IGtleSwgYXRycmlidXRlLCBpbnB1dCB2YWx1ZSBwcm9wLCBldGMuXG4gIC8vIEluIG1vc3QgY2FzZXMsIHRoaXMgY29uc29sZSBvdXRwdXQgYWxzbyBzaG93cyB0aGUgY29tcG9uZW50IGFuZCBpdHNcbiAgLy8gYW5jZXN0b3IgY29tcG9uZW50cyB3aGVyZSB0aGUgZXhjZXB0aW9uIGhhcHBlbmVkLlxuICAvL1xuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaW50ZXJuYWwvc2FmZS1zdHJpbmctY29lcmNpb25cbiAgcmV0dXJuICcnICsgdmFsdWU7XG59XG5mdW5jdGlvbiBjaGVja0tleVN0cmluZ0NvZXJjaW9uKHZhbHVlKSB7XG4gIHtcbiAgICBpZiAod2lsbENvZXJjaW9uVGhyb3codmFsdWUpKSB7XG4gICAgICBlcnJvcignVGhlIHByb3ZpZGVkIGtleSBpcyBhbiB1bnN1cHBvcnRlZCB0eXBlICVzLicgKyAnIFRoaXMgdmFsdWUgbXVzdCBiZSBjb2VyY2VkIHRvIGEgc3RyaW5nIGJlZm9yZSB1c2luZyBpdCBoZXJlLicsIHR5cGVOYW1lKHZhbHVlKSk7XG5cbiAgICAgIHJldHVybiB0ZXN0U3RyaW5nQ29lcmNpb24odmFsdWUpOyAvLyB0aHJvdyAodG8gaGVscCBjYWxsZXJzIGZpbmQgdHJvdWJsZXNob290aW5nIGNvbW1lbnRzKVxuICAgIH1cbiAgfVxufVxuXG52YXIgUkVBQ1RfQ0xJRU5UX1JFRkVSRU5DRSQxID0gU3ltYm9sLmZvcigncmVhY3QuY2xpZW50LnJlZmVyZW5jZScpO1xuZnVuY3Rpb24gaXNWYWxpZEVsZW1lbnRUeXBlKHR5cGUpIHtcbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdHlwZSA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIHJldHVybiB0cnVlO1xuICB9IC8vIE5vdGU6IHR5cGVvZiBtaWdodCBiZSBvdGhlciB0aGFuICdzeW1ib2wnIG9yICdudW1iZXInIChlLmcuIGlmIGl0J3MgYSBwb2x5ZmlsbCkuXG5cblxuICBpZiAodHlwZSA9PT0gUkVBQ1RfRlJBR01FTlRfVFlQRSB8fCB0eXBlID09PSBSRUFDVF9QUk9GSUxFUl9UWVBFIHx8IGVuYWJsZURlYnVnVHJhY2luZyAgfHwgdHlwZSA9PT0gUkVBQ1RfU1RSSUNUX01PREVfVFlQRSB8fCB0eXBlID09PSBSRUFDVF9TVVNQRU5TRV9UWVBFIHx8IHR5cGUgPT09IFJFQUNUX1NVU1BFTlNFX0xJU1RfVFlQRSB8fCBlbmFibGVMZWdhY3lIaWRkZW4gIHx8IHR5cGUgPT09IFJFQUNUX09GRlNDUkVFTl9UWVBFIHx8IGVuYWJsZVNjb3BlQVBJICB8fCBlbmFibGVDYWNoZUVsZW1lbnQgIHx8IGVuYWJsZVRyYW5zaXRpb25UcmFjaW5nICkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnb2JqZWN0JyAmJiB0eXBlICE9PSBudWxsKSB7XG4gICAgaWYgKHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0xBWllfVFlQRSB8fCB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9NRU1PX1RZUEUgfHwgdHlwZS4kJHR5cGVvZiA9PT0gUkVBQ1RfQ09OVEVYVF9UWVBFIHx8IHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX1BST1ZJREVSX1RZUEUgfHwgZW5hYmxlUmVuZGVyYWJsZUNvbnRleHQgIHx8IHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0ZPUldBUkRfUkVGX1RZUEUgfHwgLy8gVGhpcyBuZWVkcyB0byBpbmNsdWRlIGFsbCBwb3NzaWJsZSBtb2R1bGUgcmVmZXJlbmNlIG9iamVjdFxuICAgIC8vIHR5cGVzIHN1cHBvcnRlZCBieSBhbnkgRmxpZ2h0IGNvbmZpZ3VyYXRpb24gYW55d2hlcmUgc2luY2VcbiAgICAvLyB3ZSBkb24ndCBrbm93IHdoaWNoIEZsaWdodCBidWlsZCB0aGlzIHdpbGwgZW5kIHVwIGJlaW5nIHVzZWRcbiAgICAvLyB3aXRoLlxuICAgIHR5cGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UkMSB8fCB0eXBlLmdldE1vZHVsZUlkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTtcbn1cblxudmFyIGlzQXJyYXlJbXBsID0gQXJyYXkuaXNBcnJheTsgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXJlZGVjbGFyZVxuXG5mdW5jdGlvbiBpc0FycmF5KGEpIHtcbiAgcmV0dXJuIGlzQXJyYXlJbXBsKGEpO1xufVxuXG4vLyBIZWxwZXJzIHRvIHBhdGNoIGNvbnNvbGUubG9ncyB0byBhdm9pZCBsb2dnaW5nIGR1cmluZyBzaWRlLWVmZmVjdCBmcmVlXG4vLyByZXBsYXlpbmcgb24gcmVuZGVyIGZ1bmN0aW9uLiBUaGlzIGN1cnJlbnRseSBvbmx5IHBhdGNoZXMgdGhlIG9iamVjdFxuLy8gbGF6aWx5IHdoaWNoIHdvbid0IGNvdmVyIGlmIHRoZSBsb2cgZnVuY3Rpb24gd2FzIGV4dHJhY3RlZCBlYWdlcmx5LlxuLy8gV2UgY291bGQgYWxzbyBlYWdlcmx5IHBhdGNoIHRoZSBtZXRob2QuXG52YXIgZGlzYWJsZWREZXB0aCA9IDA7XG52YXIgcHJldkxvZztcbnZhciBwcmV2SW5mbztcbnZhciBwcmV2V2FybjtcbnZhciBwcmV2RXJyb3I7XG52YXIgcHJldkdyb3VwO1xudmFyIHByZXZHcm91cENvbGxhcHNlZDtcbnZhciBwcmV2R3JvdXBFbmQ7XG5cbmZ1bmN0aW9uIGRpc2FibGVkTG9nKCkge31cblxuZGlzYWJsZWRMb2cuX19yZWFjdERpc2FibGVkTG9nID0gdHJ1ZTtcbmZ1bmN0aW9uIGRpc2FibGVMb2dzKCkge1xuICB7XG4gICAgaWYgKGRpc2FibGVkRGVwdGggPT09IDApIHtcbiAgICAgIC8qIGVzbGludC1kaXNhYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgICAgcHJldkxvZyA9IGNvbnNvbGUubG9nO1xuICAgICAgcHJldkluZm8gPSBjb25zb2xlLmluZm87XG4gICAgICBwcmV2V2FybiA9IGNvbnNvbGUud2FybjtcbiAgICAgIHByZXZFcnJvciA9IGNvbnNvbGUuZXJyb3I7XG4gICAgICBwcmV2R3JvdXAgPSBjb25zb2xlLmdyb3VwO1xuICAgICAgcHJldkdyb3VwQ29sbGFwc2VkID0gY29uc29sZS5ncm91cENvbGxhcHNlZDtcbiAgICAgIHByZXZHcm91cEVuZCA9IGNvbnNvbGUuZ3JvdXBFbmQ7IC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC9pc3N1ZXMvMTkwOTlcblxuICAgICAgdmFyIHByb3BzID0ge1xuICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIHZhbHVlOiBkaXNhYmxlZExvZyxcbiAgICAgICAgd3JpdGFibGU6IHRydWVcbiAgICAgIH07IC8vICRGbG93Rml4TWVbY2Fubm90LXdyaXRlXSBGbG93IHRoaW5rcyBjb25zb2xlIGlzIGltbXV0YWJsZS5cblxuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoY29uc29sZSwge1xuICAgICAgICBpbmZvOiBwcm9wcyxcbiAgICAgICAgbG9nOiBwcm9wcyxcbiAgICAgICAgd2FybjogcHJvcHMsXG4gICAgICAgIGVycm9yOiBwcm9wcyxcbiAgICAgICAgZ3JvdXA6IHByb3BzLFxuICAgICAgICBncm91cENvbGxhcHNlZDogcHJvcHMsXG4gICAgICAgIGdyb3VwRW5kOiBwcm9wc1xuICAgICAgfSk7XG4gICAgICAvKiBlc2xpbnQtZW5hYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgIH1cblxuICAgIGRpc2FibGVkRGVwdGgrKztcbiAgfVxufVxuZnVuY3Rpb24gcmVlbmFibGVMb2dzKCkge1xuICB7XG4gICAgZGlzYWJsZWREZXB0aC0tO1xuXG4gICAgaWYgKGRpc2FibGVkRGVwdGggPT09IDApIHtcbiAgICAgIC8qIGVzbGludC1kaXNhYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgICAgdmFyIHByb3BzID0ge1xuICAgICAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgIHdyaXRhYmxlOiB0cnVlXG4gICAgICB9OyAvLyAkRmxvd0ZpeE1lW2Nhbm5vdC13cml0ZV0gRmxvdyB0aGlua3MgY29uc29sZSBpcyBpbW11dGFibGUuXG5cbiAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0aWVzKGNvbnNvbGUsIHtcbiAgICAgICAgbG9nOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZMb2dcbiAgICAgICAgfSksXG4gICAgICAgIGluZm86IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldkluZm9cbiAgICAgICAgfSksXG4gICAgICAgIHdhcm46IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldldhcm5cbiAgICAgICAgfSksXG4gICAgICAgIGVycm9yOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZFcnJvclxuICAgICAgICB9KSxcbiAgICAgICAgZ3JvdXA6IGFzc2lnbih7fSwgcHJvcHMsIHtcbiAgICAgICAgICB2YWx1ZTogcHJldkdyb3VwXG4gICAgICAgIH0pLFxuICAgICAgICBncm91cENvbGxhcHNlZDogYXNzaWduKHt9LCBwcm9wcywge1xuICAgICAgICAgIHZhbHVlOiBwcmV2R3JvdXBDb2xsYXBzZWRcbiAgICAgICAgfSksXG4gICAgICAgIGdyb3VwRW5kOiBhc3NpZ24oe30sIHByb3BzLCB7XG4gICAgICAgICAgdmFsdWU6IHByZXZHcm91cEVuZFxuICAgICAgICB9KVxuICAgICAgfSk7XG4gICAgICAvKiBlc2xpbnQtZW5hYmxlIHJlYWN0LWludGVybmFsL25vLXByb2R1Y3Rpb24tbG9nZ2luZyAqL1xuICAgIH1cblxuICAgIGlmIChkaXNhYmxlZERlcHRoIDwgMCkge1xuICAgICAgZXJyb3IoJ2Rpc2FibGVkRGVwdGggZmVsbCBiZWxvdyB6ZXJvLiAnICsgJ1RoaXMgaXMgYSBidWcgaW4gUmVhY3QuIFBsZWFzZSBmaWxlIGFuIGlzc3VlLicpO1xuICAgIH1cbiAgfVxufVxuXG52YXIgUmVhY3RDdXJyZW50RGlzcGF0Y2hlciA9IFJlYWN0U2hhcmVkSW50ZXJuYWxzLlJlYWN0Q3VycmVudERpc3BhdGNoZXI7XG52YXIgcHJlZml4O1xuZnVuY3Rpb24gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUobmFtZSwgb3duZXJGbikge1xuICB7XG4gICAgaWYgKHByZWZpeCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAvLyBFeHRyYWN0IHRoZSBWTSBzcGVjaWZpYyBwcmVmaXggdXNlZCBieSBlYWNoIGxpbmUuXG4gICAgICB0cnkge1xuICAgICAgICB0aHJvdyBFcnJvcigpO1xuICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICB2YXIgbWF0Y2ggPSB4LnN0YWNrLnRyaW0oKS5tYXRjaCgvXFxuKCAqKGF0ICk/KS8pO1xuICAgICAgICBwcmVmaXggPSBtYXRjaCAmJiBtYXRjaFsxXSB8fCAnJztcbiAgICAgIH1cbiAgICB9IC8vIFdlIHVzZSB0aGUgcHJlZml4IHRvIGVuc3VyZSBvdXIgc3RhY2tzIGxpbmUgdXAgd2l0aCBuYXRpdmUgc3RhY2sgZnJhbWVzLlxuXG5cbiAgICByZXR1cm4gJ1xcbicgKyBwcmVmaXggKyBuYW1lO1xuICB9XG59XG52YXIgcmVlbnRyeSA9IGZhbHNlO1xudmFyIGNvbXBvbmVudEZyYW1lQ2FjaGU7XG5cbntcbiAgdmFyIFBvc3NpYmx5V2Vha01hcCA9IHR5cGVvZiBXZWFrTWFwID09PSAnZnVuY3Rpb24nID8gV2Vha01hcCA6IE1hcDtcbiAgY29tcG9uZW50RnJhbWVDYWNoZSA9IG5ldyBQb3NzaWJseVdlYWtNYXAoKTtcbn1cbi8qKlxuICogTGV2ZXJhZ2VzIG5hdGl2ZSBicm93c2VyL1ZNIHN0YWNrIGZyYW1lcyB0byBnZXQgcHJvcGVyIGRldGFpbHMgKGUuZy5cbiAqIGZpbGVuYW1lLCBsaW5lICsgY29sIG51bWJlcikgZm9yIGEgc2luZ2xlIGNvbXBvbmVudCBpbiBhIGNvbXBvbmVudCBzdGFjay4gV2VcbiAqIGRvIHRoaXMgYnk6XG4gKiAgICgxKSB0aHJvd2luZyBhbmQgY2F0Y2hpbmcgYW4gZXJyb3IgaW4gdGhlIGZ1bmN0aW9uIC0gdGhpcyB3aWxsIGJlIG91clxuICogICAgICAgY29udHJvbCBlcnJvci5cbiAqICAgKDIpIGNhbGxpbmcgdGhlIGNvbXBvbmVudCB3aGljaCB3aWxsIGV2ZW50dWFsbHkgdGhyb3cgYW4gZXJyb3IgdGhhdCB3ZSdsbFxuICogICAgICAgY2F0Y2ggLSB0aGlzIHdpbGwgYmUgb3VyIHNhbXBsZSBlcnJvci5cbiAqICAgKDMpIGRpZmZpbmcgdGhlIGNvbnRyb2wgYW5kIHNhbXBsZSBlcnJvciBzdGFja3MgdG8gZmluZCB0aGUgc3RhY2sgZnJhbWVcbiAqICAgICAgIHdoaWNoIHJlcHJlc2VudHMgb3VyIGNvbXBvbmVudC5cbiAqL1xuXG5cbmZ1bmN0aW9uIGRlc2NyaWJlTmF0aXZlQ29tcG9uZW50RnJhbWUoZm4sIGNvbnN0cnVjdCkge1xuICAvLyBJZiBzb21ldGhpbmcgYXNrZWQgZm9yIGEgc3RhY2sgaW5zaWRlIGEgZmFrZSByZW5kZXIsIGl0IHNob3VsZCBnZXQgaWdub3JlZC5cbiAgaWYgKCFmbiB8fCByZWVudHJ5KSB7XG4gICAgcmV0dXJuICcnO1xuICB9XG5cbiAge1xuICAgIHZhciBmcmFtZSA9IGNvbXBvbmVudEZyYW1lQ2FjaGUuZ2V0KGZuKTtcblxuICAgIGlmIChmcmFtZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICByZXR1cm4gZnJhbWU7XG4gICAgfVxuICB9XG5cbiAgcmVlbnRyeSA9IHRydWU7XG4gIHZhciBwcmV2aW91c1ByZXBhcmVTdGFja1RyYWNlID0gRXJyb3IucHJlcGFyZVN0YWNrVHJhY2U7IC8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXR5cGVdIEl0IGRvZXMgYWNjZXB0IHVuZGVmaW5lZC5cblxuICBFcnJvci5wcmVwYXJlU3RhY2tUcmFjZSA9IHVuZGVmaW5lZDtcbiAgdmFyIHByZXZpb3VzRGlzcGF0Y2hlcjtcblxuICB7XG4gICAgcHJldmlvdXNEaXNwYXRjaGVyID0gUmVhY3RDdXJyZW50RGlzcGF0Y2hlci5jdXJyZW50OyAvLyBTZXQgdGhlIGRpc3BhdGNoZXIgaW4gREVWIGJlY2F1c2UgdGhpcyBtaWdodCBiZSBjYWxsIGluIHRoZSByZW5kZXIgZnVuY3Rpb25cbiAgICAvLyBmb3Igd2FybmluZ3MuXG5cbiAgICBSZWFjdEN1cnJlbnREaXNwYXRjaGVyLmN1cnJlbnQgPSBudWxsO1xuICAgIGRpc2FibGVMb2dzKCk7XG4gIH1cbiAgLyoqXG4gICAqIEZpbmRpbmcgYSBjb21tb24gc3RhY2sgZnJhbWUgYmV0d2VlbiBzYW1wbGUgYW5kIGNvbnRyb2wgZXJyb3JzIGNhbiBiZVxuICAgKiB0cmlja3kgZ2l2ZW4gdGhlIGRpZmZlcmVudCB0eXBlcyBhbmQgbGV2ZWxzIG9mIHN0YWNrIHRyYWNlIHRydW5jYXRpb24gZnJvbVxuICAgKiBkaWZmZXJlbnQgSlMgVk1zLiBTbyBpbnN0ZWFkIHdlJ2xsIGF0dGVtcHQgdG8gY29udHJvbCB3aGF0IHRoYXQgY29tbW9uXG4gICAqIGZyYW1lIHNob3VsZCBiZSB0aHJvdWdoIHRoaXMgb2JqZWN0IG1ldGhvZDpcbiAgICogSGF2aW5nIGJvdGggdGhlIHNhbXBsZSBhbmQgY29udHJvbCBlcnJvcnMgYmUgaW4gdGhlIGZ1bmN0aW9uIHVuZGVyIHRoZVxuICAgKiBgRGVzY3JpYmVOYXRpdmVDb21wb25lbnRGcmFtZVJvb3RgIHByb3BlcnR5LCArIHNldHRpbmcgdGhlIGBuYW1lYCBhbmRcbiAgICogYGRpc3BsYXlOYW1lYCBwcm9wZXJ0aWVzIG9mIHRoZSBmdW5jdGlvbiBlbnN1cmVzIHRoYXQgYSBzdGFja1xuICAgKiBmcmFtZSBleGlzdHMgdGhhdCBoYXMgdGhlIG1ldGhvZCBuYW1lIGBEZXNjcmliZU5hdGl2ZUNvbXBvbmVudEZyYW1lUm9vdGAgaW5cbiAgICogaXQgZm9yIGJvdGggY29udHJvbCBhbmQgc2FtcGxlIHN0YWNrcy5cbiAgICovXG5cblxuICB2YXIgUnVuSW5Sb290RnJhbWUgPSB7XG4gICAgRGV0ZXJtaW5lQ29tcG9uZW50RnJhbWVSb290OiBmdW5jdGlvbiAoKSB7XG4gICAgICB2YXIgY29udHJvbDtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gVGhpcyBzaG91bGQgdGhyb3cuXG4gICAgICAgIGlmIChjb25zdHJ1Y3QpIHtcbiAgICAgICAgICAvLyBTb21ldGhpbmcgc2hvdWxkIGJlIHNldHRpbmcgdGhlIHByb3BzIGluIHRoZSBjb25zdHJ1Y3Rvci5cbiAgICAgICAgICB2YXIgRmFrZSA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgICAgIHRocm93IEVycm9yKCk7XG4gICAgICAgICAgfTsgLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddXG5cblxuICAgICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShGYWtlLnByb3RvdHlwZSwgJ3Byb3BzJywge1xuICAgICAgICAgICAgc2V0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgICAgIC8vIFdlIHVzZSBhIHRocm93aW5nIHNldHRlciBpbnN0ZWFkIG9mIGZyb3plbiBvciBub24td3JpdGFibGUgcHJvcHNcbiAgICAgICAgICAgICAgLy8gYmVjYXVzZSB0aGF0IHdvbid0IHRocm93IGluIGEgbm9uLXN0cmljdCBtb2RlIGZ1bmN0aW9uLlxuICAgICAgICAgICAgICB0aHJvdyBFcnJvcigpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgaWYgKHR5cGVvZiBSZWZsZWN0ID09PSAnb2JqZWN0JyAmJiBSZWZsZWN0LmNvbnN0cnVjdCkge1xuICAgICAgICAgICAgLy8gV2UgY29uc3RydWN0IGEgZGlmZmVyZW50IGNvbnRyb2wgZm9yIHRoaXMgY2FzZSB0byBpbmNsdWRlIGFueSBleHRyYVxuICAgICAgICAgICAgLy8gZnJhbWVzIGFkZGVkIGJ5IHRoZSBjb25zdHJ1Y3QgY2FsbC5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIFJlZmxlY3QuY29uc3RydWN0KEZha2UsIFtdKTtcbiAgICAgICAgICAgIH0gY2F0Y2ggKHgpIHtcbiAgICAgICAgICAgICAgY29udHJvbCA9IHg7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIFJlZmxlY3QuY29uc3RydWN0KGZuLCBbXSwgRmFrZSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIEZha2UuY2FsbCgpO1xuICAgICAgICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICAgICAgICBjb250cm9sID0geDtcbiAgICAgICAgICAgIH0gLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddIGZvdW5kIHdoZW4gdXBncmFkaW5nIEZsb3dcblxuXG4gICAgICAgICAgICBmbi5jYWxsKEZha2UucHJvdG90eXBlKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHRocm93IEVycm9yKCk7XG4gICAgICAgICAgfSBjYXRjaCAoeCkge1xuICAgICAgICAgICAgY29udHJvbCA9IHg7XG4gICAgICAgICAgfSAvLyBUT0RPKGx1bmEpOiBUaGlzIHdpbGwgY3VycmVudGx5IG9ubHkgdGhyb3cgaWYgdGhlIGZ1bmN0aW9uIGNvbXBvbmVudFxuICAgICAgICAgIC8vIHRyaWVzIHRvIGFjY2VzcyBSZWFjdC9SZWFjdERPTS9wcm9wcy4gV2Ugc2hvdWxkIHByb2JhYmx5IG1ha2UgdGhpcyB0aHJvd1xuICAgICAgICAgIC8vIGluIHNpbXBsZSBjb21wb25lbnRzIHRvb1xuXG5cbiAgICAgICAgICB2YXIgbWF5YmVQcm9taXNlID0gZm4oKTsgLy8gSWYgdGhlIGZ1bmN0aW9uIGNvbXBvbmVudCByZXR1cm5zIGEgcHJvbWlzZSwgaXQncyBsaWtlbHkgYW4gYXN5bmNcbiAgICAgICAgICAvLyBjb21wb25lbnQsIHdoaWNoIHdlIGRvbid0IHlldCBzdXBwb3J0LiBBdHRhY2ggYSBub29wIGNhdGNoIGhhbmRsZXIgdG9cbiAgICAgICAgICAvLyBzaWxlbmNlIHRoZSBlcnJvci5cbiAgICAgICAgICAvLyBUT0RPOiBJbXBsZW1lbnQgY29tcG9uZW50IHN0YWNrcyBmb3IgYXN5bmMgY2xpZW50IGNvbXBvbmVudHM/XG5cbiAgICAgICAgICBpZiAobWF5YmVQcm9taXNlICYmIHR5cGVvZiBtYXliZVByb21pc2UuY2F0Y2ggPT09ICdmdW5jdGlvbicpIHtcbiAgICAgICAgICAgIG1heWJlUHJvbWlzZS5jYXRjaChmdW5jdGlvbiAoKSB7fSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChzYW1wbGUpIHtcbiAgICAgICAgLy8gVGhpcyBpcyBpbmxpbmVkIG1hbnVhbGx5IGJlY2F1c2UgY2xvc3VyZSBkb2Vzbid0IGRvIGl0IGZvciB1cy5cbiAgICAgICAgaWYgKHNhbXBsZSAmJiBjb250cm9sICYmIHR5cGVvZiBzYW1wbGUuc3RhY2sgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgcmV0dXJuIFtzYW1wbGUuc3RhY2ssIGNvbnRyb2wuc3RhY2tdO1xuICAgICAgICB9XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBbbnVsbCwgbnVsbF07XG4gICAgfVxuICB9OyAvLyAkRmxvd0ZpeE1lW3Byb3AtbWlzc2luZ11cblxuICBSdW5JblJvb3RGcmFtZS5EZXRlcm1pbmVDb21wb25lbnRGcmFtZVJvb3QuZGlzcGxheU5hbWUgPSAnRGV0ZXJtaW5lQ29tcG9uZW50RnJhbWVSb290JztcbiAgdmFyIG5hbWVQcm9wRGVzY3JpcHRvciA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IoUnVuSW5Sb290RnJhbWUuRGV0ZXJtaW5lQ29tcG9uZW50RnJhbWVSb290LCAnbmFtZScpOyAvLyBCZWZvcmUgRVM2LCB0aGUgYG5hbWVgIHByb3BlcnR5IHdhcyBub3QgY29uZmlndXJhYmxlLlxuXG4gIGlmIChuYW1lUHJvcERlc2NyaXB0b3IgJiYgbmFtZVByb3BEZXNjcmlwdG9yLmNvbmZpZ3VyYWJsZSkge1xuICAgIC8vIFY4IHV0aWxpemVzIGEgZnVuY3Rpb24ncyBgbmFtZWAgcHJvcGVydHkgd2hlbiBnZW5lcmF0aW5nIGEgc3RhY2sgdHJhY2UuXG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KFJ1bkluUm9vdEZyYW1lLkRldGVybWluZUNvbXBvbmVudEZyYW1lUm9vdCwgLy8gQ29uZmlndXJhYmxlIHByb3BlcnRpZXMgY2FuIGJlIHVwZGF0ZWQgZXZlbiBpZiBpdHMgd3JpdGFibGUgZGVzY3JpcHRvclxuICAgIC8vIGlzIHNldCB0byBgZmFsc2VgLlxuICAgIC8vICRGbG93Rml4TWVbY2Fubm90LXdyaXRlXVxuICAgICduYW1lJywge1xuICAgICAgdmFsdWU6ICdEZXRlcm1pbmVDb21wb25lbnRGcmFtZVJvb3QnXG4gICAgfSk7XG4gIH1cblxuICB0cnkge1xuICAgIHZhciBfUnVuSW5Sb290RnJhbWUkRGV0ZXIgPSBSdW5JblJvb3RGcmFtZS5EZXRlcm1pbmVDb21wb25lbnRGcmFtZVJvb3QoKSxcbiAgICAgICAgc2FtcGxlU3RhY2sgPSBfUnVuSW5Sb290RnJhbWUkRGV0ZXJbMF0sXG4gICAgICAgIGNvbnRyb2xTdGFjayA9IF9SdW5JblJvb3RGcmFtZSREZXRlclsxXTtcblxuICAgIGlmIChzYW1wbGVTdGFjayAmJiBjb250cm9sU3RhY2spIHtcbiAgICAgIC8vIFRoaXMgZXh0cmFjdHMgdGhlIGZpcnN0IGZyYW1lIGZyb20gdGhlIHNhbXBsZSB0aGF0IGlzbid0IGFsc28gaW4gdGhlIGNvbnRyb2wuXG4gICAgICAvLyBTa2lwcGluZyBvbmUgZnJhbWUgdGhhdCB3ZSBhc3N1bWUgaXMgdGhlIGZyYW1lIHRoYXQgY2FsbHMgdGhlIHR3by5cbiAgICAgIHZhciBzYW1wbGVMaW5lcyA9IHNhbXBsZVN0YWNrLnNwbGl0KCdcXG4nKTtcbiAgICAgIHZhciBjb250cm9sTGluZXMgPSBjb250cm9sU3RhY2suc3BsaXQoJ1xcbicpO1xuICAgICAgdmFyIHMgPSAwO1xuICAgICAgdmFyIGMgPSAwO1xuXG4gICAgICB3aGlsZSAocyA8IHNhbXBsZUxpbmVzLmxlbmd0aCAmJiAhc2FtcGxlTGluZXNbc10uaW5jbHVkZXMoJ0RldGVybWluZUNvbXBvbmVudEZyYW1lUm9vdCcpKSB7XG4gICAgICAgIHMrKztcbiAgICAgIH1cblxuICAgICAgd2hpbGUgKGMgPCBjb250cm9sTGluZXMubGVuZ3RoICYmICFjb250cm9sTGluZXNbY10uaW5jbHVkZXMoJ0RldGVybWluZUNvbXBvbmVudEZyYW1lUm9vdCcpKSB7XG4gICAgICAgIGMrKztcbiAgICAgIH0gLy8gV2UgY291bGRuJ3QgZmluZCBvdXIgaW50ZW50aW9uYWxseSBpbmplY3RlZCBjb21tb24gcm9vdCBmcmFtZSwgYXR0ZW1wdFxuICAgICAgLy8gdG8gZmluZCBhbm90aGVyIGNvbW1vbiByb290IGZyYW1lIGJ5IHNlYXJjaCBmcm9tIHRoZSBib3R0b20gb2YgdGhlXG4gICAgICAvLyBjb250cm9sIHN0YWNrLi4uXG5cblxuICAgICAgaWYgKHMgPT09IHNhbXBsZUxpbmVzLmxlbmd0aCB8fCBjID09PSBjb250cm9sTGluZXMubGVuZ3RoKSB7XG4gICAgICAgIHMgPSBzYW1wbGVMaW5lcy5sZW5ndGggLSAxO1xuICAgICAgICBjID0gY29udHJvbExpbmVzLmxlbmd0aCAtIDE7XG5cbiAgICAgICAgd2hpbGUgKHMgPj0gMSAmJiBjID49IDAgJiYgc2FtcGxlTGluZXNbc10gIT09IGNvbnRyb2xMaW5lc1tjXSkge1xuICAgICAgICAgIC8vIFdlIGV4cGVjdCBhdCBsZWFzdCBvbmUgc3RhY2sgZnJhbWUgdG8gYmUgc2hhcmVkLlxuICAgICAgICAgIC8vIFR5cGljYWxseSB0aGlzIHdpbGwgYmUgdGhlIHJvb3QgbW9zdCBvbmUuIEhvd2V2ZXIsIHN0YWNrIGZyYW1lcyBtYXkgYmVcbiAgICAgICAgICAvLyBjdXQgb2ZmIGR1ZSB0byBtYXhpbXVtIHN0YWNrIGxpbWl0cy4gSW4gdGhpcyBjYXNlLCBvbmUgbWF5YmUgY3V0IG9mZlxuICAgICAgICAgIC8vIGVhcmxpZXIgdGhhbiB0aGUgb3RoZXIuIFdlIGFzc3VtZSB0aGF0IHRoZSBzYW1wbGUgaXMgbG9uZ2VyIG9yIHRoZSBzYW1lXG4gICAgICAgICAgLy8gYW5kIHRoZXJlIGZvciBjdXQgb2ZmIGVhcmxpZXIuIFNvIHdlIHNob3VsZCBmaW5kIHRoZSByb290IG1vc3QgZnJhbWUgaW5cbiAgICAgICAgICAvLyB0aGUgc2FtcGxlIHNvbWV3aGVyZSBpbiB0aGUgY29udHJvbC5cbiAgICAgICAgICBjLS07XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgZm9yICg7IHMgPj0gMSAmJiBjID49IDA7IHMtLSwgYy0tKSB7XG4gICAgICAgIC8vIE5leHQgd2UgZmluZCB0aGUgZmlyc3Qgb25lIHRoYXQgaXNuJ3QgdGhlIHNhbWUgd2hpY2ggc2hvdWxkIGJlIHRoZVxuICAgICAgICAvLyBmcmFtZSB0aGF0IGNhbGxlZCBvdXIgc2FtcGxlIGZ1bmN0aW9uIGFuZCB0aGUgY29udHJvbC5cbiAgICAgICAgaWYgKHNhbXBsZUxpbmVzW3NdICE9PSBjb250cm9sTGluZXNbY10pIHtcbiAgICAgICAgICAvLyBJbiBWOCwgdGhlIGZpcnN0IGxpbmUgaXMgZGVzY3JpYmluZyB0aGUgbWVzc2FnZSBidXQgb3RoZXIgVk1zIGRvbid0LlxuICAgICAgICAgIC8vIElmIHdlJ3JlIGFib3V0IHRvIHJldHVybiB0aGUgZmlyc3QgbGluZSwgYW5kIHRoZSBjb250cm9sIGlzIGFsc28gb24gdGhlIHNhbWVcbiAgICAgICAgICAvLyBsaW5lLCB0aGF0J3MgYSBwcmV0dHkgZ29vZCBpbmRpY2F0b3IgdGhhdCBvdXIgc2FtcGxlIHRocmV3IGF0IHNhbWUgbGluZSBhc1xuICAgICAgICAgIC8vIHRoZSBjb250cm9sLiBJLmUuIGJlZm9yZSB3ZSBlbnRlcmVkIHRoZSBzYW1wbGUgZnJhbWUuIFNvIHdlIGlnbm9yZSB0aGlzIHJlc3VsdC5cbiAgICAgICAgICAvLyBUaGlzIGNhbiBoYXBwZW4gaWYgeW91IHBhc3NlZCBhIGNsYXNzIHRvIGZ1bmN0aW9uIGNvbXBvbmVudCwgb3Igbm9uLWZ1bmN0aW9uLlxuICAgICAgICAgIGlmIChzICE9PSAxIHx8IGMgIT09IDEpIHtcbiAgICAgICAgICAgIGRvIHtcbiAgICAgICAgICAgICAgcy0tO1xuICAgICAgICAgICAgICBjLS07IC8vIFdlIG1heSBzdGlsbCBoYXZlIHNpbWlsYXIgaW50ZXJtZWRpYXRlIGZyYW1lcyBmcm9tIHRoZSBjb25zdHJ1Y3QgY2FsbC5cbiAgICAgICAgICAgICAgLy8gVGhlIG5leHQgb25lIHRoYXQgaXNuJ3QgdGhlIHNhbWUgc2hvdWxkIGJlIG91ciBtYXRjaCB0aG91Z2guXG5cbiAgICAgICAgICAgICAgaWYgKGMgPCAwIHx8IHNhbXBsZUxpbmVzW3NdICE9PSBjb250cm9sTGluZXNbY10pIHtcbiAgICAgICAgICAgICAgICAvLyBWOCBhZGRzIGEgXCJuZXdcIiBwcmVmaXggZm9yIG5hdGl2ZSBjbGFzc2VzLiBMZXQncyByZW1vdmUgaXQgdG8gbWFrZSBpdCBwcmV0dGllci5cbiAgICAgICAgICAgICAgICB2YXIgX2ZyYW1lID0gJ1xcbicgKyBzYW1wbGVMaW5lc1tzXS5yZXBsYWNlKCcgYXQgbmV3ICcsICcgYXQgJyk7IC8vIElmIG91ciBjb21wb25lbnQgZnJhbWUgaXMgbGFiZWxlZCBcIjxhbm9ueW1vdXM+XCJcbiAgICAgICAgICAgICAgICAvLyBidXQgd2UgaGF2ZSBhIHVzZXItcHJvdmlkZWQgXCJkaXNwbGF5TmFtZVwiXG4gICAgICAgICAgICAgICAgLy8gc3BsaWNlIGl0IGluIHRvIG1ha2UgdGhlIHN0YWNrIG1vcmUgcmVhZGFibGUuXG5cblxuICAgICAgICAgICAgICAgIGlmIChmbi5kaXNwbGF5TmFtZSAmJiBfZnJhbWUuaW5jbHVkZXMoJzxhbm9ueW1vdXM+JykpIHtcbiAgICAgICAgICAgICAgICAgIF9mcmFtZSA9IF9mcmFtZS5yZXBsYWNlKCc8YW5vbnltb3VzPicsIGZuLmRpc3BsYXlOYW1lKTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBpZiAodHJ1ZSkge1xuICAgICAgICAgICAgICAgICAgaWYgKHR5cGVvZiBmbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAgICAgICAgICAgICBjb21wb25lbnRGcmFtZUNhY2hlLnNldChmbiwgX2ZyYW1lKTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IC8vIFJldHVybiB0aGUgbGluZSB3ZSBmb3VuZC5cblxuXG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mcmFtZTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSB3aGlsZSAocyA+PSAxICYmIGMgPj0gMCk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgYnJlYWs7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0gZmluYWxseSB7XG4gICAgcmVlbnRyeSA9IGZhbHNlO1xuXG4gICAge1xuICAgICAgUmVhY3RDdXJyZW50RGlzcGF0Y2hlci5jdXJyZW50ID0gcHJldmlvdXNEaXNwYXRjaGVyO1xuICAgICAgcmVlbmFibGVMb2dzKCk7XG4gICAgfVxuXG4gICAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBwcmV2aW91c1ByZXBhcmVTdGFja1RyYWNlO1xuICB9IC8vIEZhbGxiYWNrIHRvIGp1c3QgdXNpbmcgdGhlIG5hbWUgaWYgd2UgY291bGRuJ3QgbWFrZSBpdCB0aHJvdy5cblxuXG4gIHZhciBuYW1lID0gZm4gPyBmbi5kaXNwbGF5TmFtZSB8fCBmbi5uYW1lIDogJyc7XG4gIHZhciBzeW50aGV0aWNGcmFtZSA9IG5hbWUgPyBkZXNjcmliZUJ1aWx0SW5Db21wb25lbnRGcmFtZShuYW1lKSA6ICcnO1xuXG4gIHtcbiAgICBpZiAodHlwZW9mIGZuID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBjb21wb25lbnRGcmFtZUNhY2hlLnNldChmbiwgc3ludGhldGljRnJhbWUpO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBzeW50aGV0aWNGcmFtZTtcbn1cbmZ1bmN0aW9uIGRlc2NyaWJlRnVuY3Rpb25Db21wb25lbnRGcmFtZShmbiwgb3duZXJGbikge1xuICB7XG4gICAgcmV0dXJuIGRlc2NyaWJlTmF0aXZlQ29tcG9uZW50RnJhbWUoZm4sIGZhbHNlKTtcbiAgfVxufVxuXG5mdW5jdGlvbiBzaG91bGRDb25zdHJ1Y3QoQ29tcG9uZW50KSB7XG4gIHZhciBwcm90b3R5cGUgPSBDb21wb25lbnQucHJvdG90eXBlO1xuICByZXR1cm4gISEocHJvdG90eXBlICYmIHByb3RvdHlwZS5pc1JlYWN0Q29tcG9uZW50KTtcbn1cblxuZnVuY3Rpb24gZGVzY3JpYmVVbmtub3duRWxlbWVudFR5cGVGcmFtZUluREVWKHR5cGUsIG93bmVyRm4pIHtcblxuICBpZiAodHlwZSA9PSBudWxsKSB7XG4gICAgcmV0dXJuICcnO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnZnVuY3Rpb24nKSB7XG4gICAge1xuICAgICAgcmV0dXJuIGRlc2NyaWJlTmF0aXZlQ29tcG9uZW50RnJhbWUodHlwZSwgc2hvdWxkQ29uc3RydWN0KHR5cGUpKTtcbiAgICB9XG4gIH1cblxuICBpZiAodHlwZW9mIHR5cGUgPT09ICdzdHJpbmcnKSB7XG4gICAgcmV0dXJuIGRlc2NyaWJlQnVpbHRJbkNvbXBvbmVudEZyYW1lKHR5cGUpO1xuICB9XG5cbiAgc3dpdGNoICh0eXBlKSB7XG4gICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9UWVBFOlxuICAgICAgcmV0dXJuIGRlc2NyaWJlQnVpbHRJbkNvbXBvbmVudEZyYW1lKCdTdXNwZW5zZScpO1xuXG4gICAgY2FzZSBSRUFDVF9TVVNQRU5TRV9MSVNUX1RZUEU6XG4gICAgICByZXR1cm4gZGVzY3JpYmVCdWlsdEluQ29tcG9uZW50RnJhbWUoJ1N1c3BlbnNlTGlzdCcpO1xuICB9XG5cbiAgaWYgKHR5cGVvZiB0eXBlID09PSAnb2JqZWN0Jykge1xuICAgIHN3aXRjaCAodHlwZS4kJHR5cGVvZikge1xuICAgICAgY2FzZSBSRUFDVF9GT1JXQVJEX1JFRl9UWVBFOlxuICAgICAgICByZXR1cm4gZGVzY3JpYmVGdW5jdGlvbkNvbXBvbmVudEZyYW1lKHR5cGUucmVuZGVyKTtcblxuICAgICAgY2FzZSBSRUFDVF9NRU1PX1RZUEU6XG4gICAgICAgIC8vIE1lbW8gbWF5IGNvbnRhaW4gYW55IGNvbXBvbmVudCB0eXBlIHNvIHdlIHJlY3Vyc2l2ZWx5IHJlc29sdmUgaXQuXG4gICAgICAgIHJldHVybiBkZXNjcmliZVVua25vd25FbGVtZW50VHlwZUZyYW1lSW5ERVYodHlwZS50eXBlLCBvd25lckZuKTtcblxuICAgICAgY2FzZSBSRUFDVF9MQVpZX1RZUEU6XG4gICAgICAgIHtcbiAgICAgICAgICB2YXIgbGF6eUNvbXBvbmVudCA9IHR5cGU7XG4gICAgICAgICAgdmFyIHBheWxvYWQgPSBsYXp5Q29tcG9uZW50Ll9wYXlsb2FkO1xuICAgICAgICAgIHZhciBpbml0ID0gbGF6eUNvbXBvbmVudC5faW5pdDtcblxuICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBMYXp5IG1heSBjb250YWluIGFueSBjb21wb25lbnQgdHlwZSBzbyB3ZSByZWN1cnNpdmVseSByZXNvbHZlIGl0LlxuICAgICAgICAgICAgcmV0dXJuIGRlc2NyaWJlVW5rbm93bkVsZW1lbnRUeXBlRnJhbWVJbkRFVihpbml0KHBheWxvYWQpLCBvd25lckZuKTtcbiAgICAgICAgICB9IGNhdGNoICh4KSB7fVxuICAgICAgICB9XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuICcnO1xufVxuXG52YXIgUmVhY3RDdXJyZW50T3duZXIgPSBSZWFjdFNoYXJlZEludGVybmFscy5SZWFjdEN1cnJlbnRPd25lcjtcbnZhciBSZWFjdERlYnVnQ3VycmVudEZyYW1lID0gUmVhY3RTaGFyZWRJbnRlcm5hbHMuUmVhY3REZWJ1Z0N1cnJlbnRGcmFtZTtcbnZhciBSRUFDVF9DTElFTlRfUkVGRVJFTkNFID0gU3ltYm9sLmZvcigncmVhY3QuY2xpZW50LnJlZmVyZW5jZScpO1xudmFyIHNwZWNpYWxQcm9wS2V5V2FybmluZ1Nob3duO1xudmFyIHNwZWNpYWxQcm9wUmVmV2FybmluZ1Nob3duO1xudmFyIGRpZFdhcm5BYm91dFN0cmluZ1JlZnM7XG5cbntcbiAgZGlkV2FybkFib3V0U3RyaW5nUmVmcyA9IHt9O1xufVxuXG5mdW5jdGlvbiBoYXNWYWxpZFJlZihjb25maWcpIHtcbiAge1xuICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgJ3JlZicpKSB7XG4gICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsICdyZWYnKS5nZXQ7XG5cbiAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gY29uZmlnLnJlZiAhPT0gdW5kZWZpbmVkO1xufVxuXG5mdW5jdGlvbiBoYXNWYWxpZEtleShjb25maWcpIHtcbiAge1xuICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgJ2tleScpKSB7XG4gICAgICB2YXIgZ2V0dGVyID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihjb25maWcsICdrZXknKS5nZXQ7XG5cbiAgICAgIGlmIChnZXR0ZXIgJiYgZ2V0dGVyLmlzUmVhY3RXYXJuaW5nKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICByZXR1cm4gY29uZmlnLmtleSAhPT0gdW5kZWZpbmVkO1xufVxuXG5mdW5jdGlvbiB3YXJuSWZTdHJpbmdSZWZDYW5ub3RCZUF1dG9Db252ZXJ0ZWQoY29uZmlnLCBzZWxmKSB7XG4gIHtcbiAgICBpZiAodHlwZW9mIGNvbmZpZy5yZWYgPT09ICdzdHJpbmcnICYmIFJlYWN0Q3VycmVudE93bmVyLmN1cnJlbnQgJiYgc2VsZiAmJiBSZWFjdEN1cnJlbnRPd25lci5jdXJyZW50LnN0YXRlTm9kZSAhPT0gc2VsZikge1xuICAgICAgdmFyIGNvbXBvbmVudE5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoUmVhY3RDdXJyZW50T3duZXIuY3VycmVudC50eXBlKTtcblxuICAgICAgaWYgKCFkaWRXYXJuQWJvdXRTdHJpbmdSZWZzW2NvbXBvbmVudE5hbWVdKSB7XG4gICAgICAgIGVycm9yKCdDb21wb25lbnQgXCIlc1wiIGNvbnRhaW5zIHRoZSBzdHJpbmcgcmVmIFwiJXNcIi4gJyArICdTdXBwb3J0IGZvciBzdHJpbmcgcmVmcyB3aWxsIGJlIHJlbW92ZWQgaW4gYSBmdXR1cmUgbWFqb3IgcmVsZWFzZS4gJyArICdUaGlzIGNhc2UgY2Fubm90IGJlIGF1dG9tYXRpY2FsbHkgY29udmVydGVkIHRvIGFuIGFycm93IGZ1bmN0aW9uLiAnICsgJ1dlIGFzayB5b3UgdG8gbWFudWFsbHkgZml4IHRoaXMgY2FzZSBieSB1c2luZyB1c2VSZWYoKSBvciBjcmVhdGVSZWYoKSBpbnN0ZWFkLiAnICsgJ0xlYXJuIG1vcmUgYWJvdXQgdXNpbmcgcmVmcyBzYWZlbHkgaGVyZTogJyArICdodHRwczovL3JlYWN0anMub3JnL2xpbmsvc3RyaWN0LW1vZGUtc3RyaW5nLXJlZicsIGdldENvbXBvbmVudE5hbWVGcm9tVHlwZShSZWFjdEN1cnJlbnRPd25lci5jdXJyZW50LnR5cGUpLCBjb25maWcucmVmKTtcblxuICAgICAgICBkaWRXYXJuQWJvdXRTdHJpbmdSZWZzW2NvbXBvbmVudE5hbWVdID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuZnVuY3Rpb24gZGVmaW5lS2V5UHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKSB7XG4gIHtcbiAgICB2YXIgd2FybkFib3V0QWNjZXNzaW5nS2V5ID0gZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKCFzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93bikge1xuICAgICAgICBzcGVjaWFsUHJvcEtleVdhcm5pbmdTaG93biA9IHRydWU7XG5cbiAgICAgICAgZXJyb3IoJyVzOiBga2V5YCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0ICcgKyAnaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSAnICsgJ3ZhbHVlIHdpdGhpbiB0aGUgY2hpbGQgY29tcG9uZW50LCB5b3Ugc2hvdWxkIHBhc3MgaXQgYXMgYSBkaWZmZXJlbnQgJyArICdwcm9wLiAoaHR0cHM6Ly9yZWFjdGpzLm9yZy9saW5rL3NwZWNpYWwtcHJvcHMpJywgZGlzcGxheU5hbWUpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICB3YXJuQWJvdXRBY2Nlc3NpbmdLZXkuaXNSZWFjdFdhcm5pbmcgPSB0cnVlO1xuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShwcm9wcywgJ2tleScsIHtcbiAgICAgIGdldDogd2FybkFib3V0QWNjZXNzaW5nS2V5LFxuICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgfSk7XG4gIH1cbn1cblxuZnVuY3Rpb24gZGVmaW5lUmVmUHJvcFdhcm5pbmdHZXR0ZXIocHJvcHMsIGRpc3BsYXlOYW1lKSB7XG4gIHtcbiAgICB7XG4gICAgICB2YXIgd2FybkFib3V0QWNjZXNzaW5nUmVmID0gZnVuY3Rpb24gKCkge1xuICAgICAgICBpZiAoIXNwZWNpYWxQcm9wUmVmV2FybmluZ1Nob3duKSB7XG4gICAgICAgICAgc3BlY2lhbFByb3BSZWZXYXJuaW5nU2hvd24gPSB0cnVlO1xuXG4gICAgICAgICAgZXJyb3IoJyVzOiBgcmVmYCBpcyBub3QgYSBwcm9wLiBUcnlpbmcgdG8gYWNjZXNzIGl0IHdpbGwgcmVzdWx0ICcgKyAnaW4gYHVuZGVmaW5lZGAgYmVpbmcgcmV0dXJuZWQuIElmIHlvdSBuZWVkIHRvIGFjY2VzcyB0aGUgc2FtZSAnICsgJ3ZhbHVlIHdpdGhpbiB0aGUgY2hpbGQgY29tcG9uZW50LCB5b3Ugc2hvdWxkIHBhc3MgaXQgYXMgYSBkaWZmZXJlbnQgJyArICdwcm9wLiAoaHR0cHM6Ly9yZWFjdGpzLm9yZy9saW5rL3NwZWNpYWwtcHJvcHMpJywgZGlzcGxheU5hbWUpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICB3YXJuQWJvdXRBY2Nlc3NpbmdSZWYuaXNSZWFjdFdhcm5pbmcgPSB0cnVlO1xuICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHByb3BzLCAncmVmJywge1xuICAgICAgICBnZXQ6IHdhcm5BYm91dEFjY2Vzc2luZ1JlZixcbiAgICAgICAgY29uZmlndXJhYmxlOiB0cnVlXG4gICAgICB9KTtcbiAgICB9XG4gIH1cbn1cbi8qKlxuICogRmFjdG9yeSBtZXRob2QgdG8gY3JlYXRlIGEgbmV3IFJlYWN0IGVsZW1lbnQuIFRoaXMgbm8gbG9uZ2VyIGFkaGVyZXMgdG9cbiAqIHRoZSBjbGFzcyBwYXR0ZXJuLCBzbyBkbyBub3QgdXNlIG5ldyB0byBjYWxsIGl0LiBBbHNvLCBpbnN0YW5jZW9mIGNoZWNrXG4gKiB3aWxsIG5vdCB3b3JrLiBJbnN0ZWFkIHRlc3QgJCR0eXBlb2YgZmllbGQgYWdhaW5zdCBTeW1ib2wuZm9yKCdyZWFjdC5lbGVtZW50JykgdG8gY2hlY2tcbiAqIGlmIHNvbWV0aGluZyBpcyBhIFJlYWN0IEVsZW1lbnQuXG4gKlxuICogQHBhcmFtIHsqfSB0eXBlXG4gKiBAcGFyYW0geyp9IHByb3BzXG4gKiBAcGFyYW0geyp9IGtleVxuICogQHBhcmFtIHtzdHJpbmd8b2JqZWN0fSByZWZcbiAqIEBwYXJhbSB7Kn0gb3duZXJcbiAqIEBwYXJhbSB7Kn0gc2VsZiBBICp0ZW1wb3JhcnkqIGhlbHBlciB0byBkZXRlY3QgcGxhY2VzIHdoZXJlIGB0aGlzYCBpc1xuICogZGlmZmVyZW50IGZyb20gdGhlIGBvd25lcmAgd2hlbiBSZWFjdC5jcmVhdGVFbGVtZW50IGlzIGNhbGxlZCwgc28gdGhhdCB3ZVxuICogY2FuIHdhcm4uIFdlIHdhbnQgdG8gZ2V0IHJpZCBvZiBvd25lciBhbmQgcmVwbGFjZSBzdHJpbmcgYHJlZmBzIHdpdGggYXJyb3dcbiAqIGZ1bmN0aW9ucywgYW5kIGFzIGxvbmcgYXMgYHRoaXNgIGFuZCBvd25lciBhcmUgdGhlIHNhbWUsIHRoZXJlIHdpbGwgYmUgbm9cbiAqIGNoYW5nZSBpbiBiZWhhdmlvci5cbiAqIEBwYXJhbSB7Kn0gc291cmNlIEFuIGFubm90YXRpb24gb2JqZWN0IChhZGRlZCBieSBhIHRyYW5zcGlsZXIgb3Igb3RoZXJ3aXNlKVxuICogaW5kaWNhdGluZyBmaWxlbmFtZSwgbGluZSBudW1iZXIsIGFuZC9vciBvdGhlciBpbmZvcm1hdGlvbi5cbiAqIEBpbnRlcm5hbFxuICovXG5cblxuZnVuY3Rpb24gUmVhY3RFbGVtZW50KHR5cGUsIGtleSwgX3JlZiwgc2VsZiwgc291cmNlLCBvd25lciwgcHJvcHMpIHtcbiAgdmFyIHJlZjtcblxuICB7XG4gICAgcmVmID0gX3JlZjtcbiAgfVxuXG4gIHZhciBlbGVtZW50O1xuXG4gIHtcbiAgICAvLyBJbiBwcm9kLCBgcmVmYCBpcyBhIHJlZ3VsYXIgcHJvcGVydHkuIEl0IHdpbGwgYmUgcmVtb3ZlZCBpbiBhXG4gICAgLy8gZnV0dXJlIHJlbGVhc2UuXG4gICAgZWxlbWVudCA9IHtcbiAgICAgIC8vIFRoaXMgdGFnIGFsbG93cyB1cyB0byB1bmlxdWVseSBpZGVudGlmeSB0aGlzIGFzIGEgUmVhY3QgRWxlbWVudFxuICAgICAgJCR0eXBlb2Y6IFJFQUNUX0VMRU1FTlRfVFlQRSxcbiAgICAgIC8vIEJ1aWx0LWluIHByb3BlcnRpZXMgdGhhdCBiZWxvbmcgb24gdGhlIGVsZW1lbnRcbiAgICAgIHR5cGU6IHR5cGUsXG4gICAgICBrZXk6IGtleSxcbiAgICAgIHJlZjogcmVmLFxuICAgICAgcHJvcHM6IHByb3BzLFxuICAgICAgLy8gUmVjb3JkIHRoZSBjb21wb25lbnQgcmVzcG9uc2libGUgZm9yIGNyZWF0aW5nIHRoaXMgZWxlbWVudC5cbiAgICAgIF9vd25lcjogb3duZXJcbiAgICB9O1xuICB9XG5cbiAge1xuICAgIC8vIFRoZSB2YWxpZGF0aW9uIGZsYWcgaXMgY3VycmVudGx5IG11dGF0aXZlLiBXZSBwdXQgaXQgb25cbiAgICAvLyBhbiBleHRlcm5hbCBiYWNraW5nIHN0b3JlIHNvIHRoYXQgd2UgY2FuIGZyZWV6ZSB0aGUgd2hvbGUgb2JqZWN0LlxuICAgIC8vIFRoaXMgY2FuIGJlIHJlcGxhY2VkIHdpdGggYSBXZWFrTWFwIG9uY2UgdGhleSBhcmUgaW1wbGVtZW50ZWQgaW5cbiAgICAvLyBjb21tb25seSB1c2VkIGRldmVsb3BtZW50IGVudmlyb25tZW50cy5cbiAgICBlbGVtZW50Ll9zdG9yZSA9IHt9OyAvLyBUbyBtYWtlIGNvbXBhcmluZyBSZWFjdEVsZW1lbnRzIGVhc2llciBmb3IgdGVzdGluZyBwdXJwb3Nlcywgd2UgbWFrZVxuICAgIC8vIHRoZSB2YWxpZGF0aW9uIGZsYWcgbm9uLWVudW1lcmFibGUgKHdoZXJlIHBvc3NpYmxlLCB3aGljaCBzaG91bGRcbiAgICAvLyBpbmNsdWRlIGV2ZXJ5IGVudmlyb25tZW50IHdlIHJ1biB0ZXN0cyBpbiksIHNvIHRoZSB0ZXN0IGZyYW1ld29ya1xuICAgIC8vIGlnbm9yZXMgaXQuXG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZWxlbWVudC5fc3RvcmUsICd2YWxpZGF0ZWQnLCB7XG4gICAgICBjb25maWd1cmFibGU6IGZhbHNlLFxuICAgICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgIHZhbHVlOiBmYWxzZVxuICAgIH0pOyAvLyBkZWJ1Z0luZm8gY29udGFpbnMgU2VydmVyIENvbXBvbmVudCBkZWJ1ZyBpbmZvcm1hdGlvbi5cblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlbGVtZW50LCAnX2RlYnVnSW5mbycsIHtcbiAgICAgIGNvbmZpZ3VyYWJsZTogZmFsc2UsXG4gICAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICAgIHdyaXRhYmxlOiB0cnVlLFxuICAgICAgdmFsdWU6IG51bGxcbiAgICB9KTtcblxuICAgIGlmIChPYmplY3QuZnJlZXplKSB7XG4gICAgICBPYmplY3QuZnJlZXplKGVsZW1lbnQucHJvcHMpO1xuICAgICAgT2JqZWN0LmZyZWV6ZShlbGVtZW50KTtcbiAgICB9XG4gIH1cblxuICByZXR1cm4gZWxlbWVudDtcbn1cbnZhciBkaWRXYXJuQWJvdXRLZXlTcHJlYWQgPSB7fTtcbi8qKlxuICogaHR0cHM6Ly9naXRodWIuY29tL3JlYWN0anMvcmZjcy9wdWxsLzEwN1xuICogQHBhcmFtIHsqfSB0eXBlXG4gKiBAcGFyYW0ge29iamVjdH0gcHJvcHNcbiAqIEBwYXJhbSB7c3RyaW5nfSBrZXlcbiAqL1xuXG5mdW5jdGlvbiBqc3hERVYkMSh0eXBlLCBjb25maWcsIG1heWJlS2V5LCBpc1N0YXRpY0NoaWxkcmVuLCBzb3VyY2UsIHNlbGYpIHtcbiAge1xuICAgIGlmICghaXNWYWxpZEVsZW1lbnRUeXBlKHR5cGUpKSB7XG4gICAgICAvLyBUaGlzIGlzIGFuIGludmFsaWQgZWxlbWVudCB0eXBlLlxuICAgICAgLy9cbiAgICAgIC8vIFdlIHdhcm4gaW4gdGhpcyBjYXNlIGJ1dCBkb24ndCB0aHJvdy4gV2UgZXhwZWN0IHRoZSBlbGVtZW50IGNyZWF0aW9uIHRvXG4gICAgICAvLyBzdWNjZWVkIGFuZCB0aGVyZSB3aWxsIGxpa2VseSBiZSBlcnJvcnMgaW4gcmVuZGVyLlxuICAgICAgdmFyIGluZm8gPSAnJztcblxuICAgICAgaWYgKHR5cGUgPT09IHVuZGVmaW5lZCB8fCB0eXBlb2YgdHlwZSA9PT0gJ29iamVjdCcgJiYgdHlwZSAhPT0gbnVsbCAmJiBPYmplY3Qua2V5cyh0eXBlKS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgaW5mbyArPSAnIFlvdSBsaWtlbHkgZm9yZ290IHRvIGV4cG9ydCB5b3VyIGNvbXBvbmVudCBmcm9tIHRoZSBmaWxlICcgKyBcIml0J3MgZGVmaW5lZCBpbiwgb3IgeW91IG1pZ2h0IGhhdmUgbWl4ZWQgdXAgZGVmYXVsdCBhbmQgbmFtZWQgaW1wb3J0cy5cIjtcbiAgICAgIH1cblxuICAgICAgdmFyIHR5cGVTdHJpbmc7XG5cbiAgICAgIGlmICh0eXBlID09PSBudWxsKSB7XG4gICAgICAgIHR5cGVTdHJpbmcgPSAnbnVsbCc7XG4gICAgICB9IGVsc2UgaWYgKGlzQXJyYXkodHlwZSkpIHtcbiAgICAgICAgdHlwZVN0cmluZyA9ICdhcnJheSc7XG4gICAgICB9IGVsc2UgaWYgKHR5cGUgIT09IHVuZGVmaW5lZCAmJiB0eXBlLiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEUpIHtcbiAgICAgICAgdHlwZVN0cmluZyA9IFwiPFwiICsgKGdldENvbXBvbmVudE5hbWVGcm9tVHlwZSh0eXBlLnR5cGUpIHx8ICdVbmtub3duJykgKyBcIiAvPlwiO1xuICAgICAgICBpbmZvID0gJyBEaWQgeW91IGFjY2lkZW50YWxseSBleHBvcnQgYSBKU1ggbGl0ZXJhbCBpbnN0ZWFkIG9mIGEgY29tcG9uZW50Pyc7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0eXBlU3RyaW5nID0gdHlwZW9mIHR5cGU7XG4gICAgICB9XG5cbiAgICAgIGVycm9yKCdSZWFjdC5qc3g6IHR5cGUgaXMgaW52YWxpZCAtLSBleHBlY3RlZCBhIHN0cmluZyAoZm9yICcgKyAnYnVpbHQtaW4gY29tcG9uZW50cykgb3IgYSBjbGFzcy9mdW5jdGlvbiAoZm9yIGNvbXBvc2l0ZSAnICsgJ2NvbXBvbmVudHMpIGJ1dCBnb3Q6ICVzLiVzJywgdHlwZVN0cmluZywgaW5mbyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIFRoaXMgaXMgYSB2YWxpZCBlbGVtZW50IHR5cGUuXG4gICAgICAvLyBTa2lwIGtleSB3YXJuaW5nIGlmIHRoZSB0eXBlIGlzbid0IHZhbGlkIHNpbmNlIG91ciBrZXkgdmFsaWRhdGlvbiBsb2dpY1xuICAgICAgLy8gZG9lc24ndCBleHBlY3QgYSBub24tc3RyaW5nL2Z1bmN0aW9uIHR5cGUgYW5kIGNhbiB0aHJvdyBjb25mdXNpbmdcbiAgICAgIC8vIGVycm9ycy4gV2UgZG9uJ3Qgd2FudCBleGNlcHRpb24gYmVoYXZpb3IgdG8gZGlmZmVyIGJldHdlZW4gZGV2IGFuZFxuICAgICAgLy8gcHJvZC4gKFJlbmRlcmluZyB3aWxsIHRocm93IHdpdGggYSBoZWxwZnVsIG1lc3NhZ2UgYW5kIGFzIHNvb24gYXMgdGhlXG4gICAgICAvLyB0eXBlIGlzIGZpeGVkLCB0aGUga2V5IHdhcm5pbmdzIHdpbGwgYXBwZWFyLilcbiAgICAgIHZhciBjaGlsZHJlbiA9IGNvbmZpZy5jaGlsZHJlbjtcblxuICAgICAgaWYgKGNoaWxkcmVuICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgaWYgKGlzU3RhdGljQ2hpbGRyZW4pIHtcbiAgICAgICAgICBpZiAoaXNBcnJheShjaGlsZHJlbikpIHtcbiAgICAgICAgICAgIGZvciAodmFyIGkgPSAwOyBpIDwgY2hpbGRyZW4ubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgICAgdmFsaWRhdGVDaGlsZEtleXMoY2hpbGRyZW5baV0sIHR5cGUpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAoT2JqZWN0LmZyZWV6ZSkge1xuICAgICAgICAgICAgICBPYmplY3QuZnJlZXplKGNoaWxkcmVuKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgZXJyb3IoJ1JlYWN0LmpzeDogU3RhdGljIGNoaWxkcmVuIHNob3VsZCBhbHdheXMgYmUgYW4gYXJyYXkuICcgKyAnWW91IGFyZSBsaWtlbHkgZXhwbGljaXRseSBjYWxsaW5nIFJlYWN0LmpzeHMgb3IgUmVhY3QuanN4REVWLiAnICsgJ1VzZSB0aGUgQmFiZWwgdHJhbnNmb3JtIGluc3RlYWQuJyk7XG4gICAgICAgICAgfVxuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHZhbGlkYXRlQ2hpbGRLZXlzKGNoaWxkcmVuLCB0eXBlKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gLy8gV2FybiBhYm91dCBrZXkgc3ByZWFkIHJlZ2FyZGxlc3Mgb2Ygd2hldGhlciB0aGUgdHlwZSBpcyB2YWxpZC5cblxuXG4gICAgaWYgKGhhc093blByb3BlcnR5LmNhbGwoY29uZmlnLCAna2V5JykpIHtcbiAgICAgIHZhciBjb21wb25lbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHR5cGUpO1xuICAgICAgdmFyIGtleXMgPSBPYmplY3Qua2V5cyhjb25maWcpLmZpbHRlcihmdW5jdGlvbiAoaykge1xuICAgICAgICByZXR1cm4gayAhPT0gJ2tleSc7XG4gICAgICB9KTtcbiAgICAgIHZhciBiZWZvcmVFeGFtcGxlID0ga2V5cy5sZW5ndGggPiAwID8gJ3trZXk6IHNvbWVLZXksICcgKyBrZXlzLmpvaW4oJzogLi4uLCAnKSArICc6IC4uLn0nIDogJ3trZXk6IHNvbWVLZXl9JztcblxuICAgICAgaWYgKCFkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY29tcG9uZW50TmFtZSArIGJlZm9yZUV4YW1wbGVdKSB7XG4gICAgICAgIHZhciBhZnRlckV4YW1wbGUgPSBrZXlzLmxlbmd0aCA+IDAgPyAneycgKyBrZXlzLmpvaW4oJzogLi4uLCAnKSArICc6IC4uLn0nIDogJ3t9JztcblxuICAgICAgICBlcnJvcignQSBwcm9wcyBvYmplY3QgY29udGFpbmluZyBhIFwia2V5XCIgcHJvcCBpcyBiZWluZyBzcHJlYWQgaW50byBKU1g6XFxuJyArICcgIGxldCBwcm9wcyA9ICVzO1xcbicgKyAnICA8JXMgey4uLnByb3BzfSAvPlxcbicgKyAnUmVhY3Qga2V5cyBtdXN0IGJlIHBhc3NlZCBkaXJlY3RseSB0byBKU1ggd2l0aG91dCB1c2luZyBzcHJlYWQ6XFxuJyArICcgIGxldCBwcm9wcyA9ICVzO1xcbicgKyAnICA8JXMga2V5PXtzb21lS2V5fSB7Li4ucHJvcHN9IC8+JywgYmVmb3JlRXhhbXBsZSwgY29tcG9uZW50TmFtZSwgYWZ0ZXJFeGFtcGxlLCBjb21wb25lbnROYW1lKTtcblxuICAgICAgICBkaWRXYXJuQWJvdXRLZXlTcHJlYWRbY29tcG9uZW50TmFtZSArIGJlZm9yZUV4YW1wbGVdID0gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG5cbiAgICB2YXIgcHJvcE5hbWU7IC8vIFJlc2VydmVkIG5hbWVzIGFyZSBleHRyYWN0ZWRcblxuICAgIHZhciBwcm9wcyA9IHt9O1xuICAgIHZhciBrZXkgPSBudWxsO1xuICAgIHZhciByZWYgPSBudWxsOyAvLyBDdXJyZW50bHksIGtleSBjYW4gYmUgc3ByZWFkIGluIGFzIGEgcHJvcC4gVGhpcyBjYXVzZXMgYSBwb3RlbnRpYWxcbiAgICAvLyBpc3N1ZSBpZiBrZXkgaXMgYWxzbyBleHBsaWNpdGx5IGRlY2xhcmVkIChpZS4gPGRpdiB7Li4ucHJvcHN9IGtleT1cIkhpXCIgLz5cbiAgICAvLyBvciA8ZGl2IGtleT1cIkhpXCIgey4uLnByb3BzfSAvPiApLiBXZSB3YW50IHRvIGRlcHJlY2F0ZSBrZXkgc3ByZWFkLFxuICAgIC8vIGJ1dCBhcyBhbiBpbnRlcm1lZGlhcnkgc3RlcCwgd2Ugd2lsbCB1c2UganN4REVWIGZvciBldmVyeXRoaW5nIGV4Y2VwdFxuICAgIC8vIDxkaXYgey4uLnByb3BzfSBrZXk9XCJIaVwiIC8+LCBiZWNhdXNlIHdlIGFyZW4ndCBjdXJyZW50bHkgYWJsZSB0byB0ZWxsIGlmXG4gICAgLy8ga2V5IGlzIGV4cGxpY2l0bHkgZGVjbGFyZWQgdG8gYmUgdW5kZWZpbmVkIG9yIG5vdC5cblxuICAgIGlmIChtYXliZUtleSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICB7XG4gICAgICAgIGNoZWNrS2V5U3RyaW5nQ29lcmNpb24obWF5YmVLZXkpO1xuICAgICAgfVxuXG4gICAgICBrZXkgPSAnJyArIG1heWJlS2V5O1xuICAgIH1cblxuICAgIGlmIChoYXNWYWxpZEtleShjb25maWcpKSB7XG4gICAgICB7XG4gICAgICAgIGNoZWNrS2V5U3RyaW5nQ29lcmNpb24oY29uZmlnLmtleSk7XG4gICAgICB9XG5cbiAgICAgIGtleSA9ICcnICsgY29uZmlnLmtleTtcbiAgICB9XG5cbiAgICBpZiAoaGFzVmFsaWRSZWYoY29uZmlnKSkge1xuICAgICAge1xuICAgICAgICByZWYgPSBjb25maWcucmVmO1xuICAgICAgfVxuXG4gICAgICB3YXJuSWZTdHJpbmdSZWZDYW5ub3RCZUF1dG9Db252ZXJ0ZWQoY29uZmlnLCBzZWxmKTtcbiAgICB9IC8vIFJlbWFpbmluZyBwcm9wZXJ0aWVzIGFyZSBhZGRlZCB0byBhIG5ldyBwcm9wcyBvYmplY3RcblxuXG4gICAgZm9yIChwcm9wTmFtZSBpbiBjb25maWcpIHtcbiAgICAgIGlmIChoYXNPd25Qcm9wZXJ0eS5jYWxsKGNvbmZpZywgcHJvcE5hbWUpICYmIC8vIFNraXAgb3ZlciByZXNlcnZlZCBwcm9wIG5hbWVzXG4gICAgICBwcm9wTmFtZSAhPT0gJ2tleScgJiYgKHByb3BOYW1lICE9PSAncmVmJykpIHtcbiAgICAgICAgcHJvcHNbcHJvcE5hbWVdID0gY29uZmlnW3Byb3BOYW1lXTtcbiAgICAgIH1cbiAgICB9IC8vIFJlc29sdmUgZGVmYXVsdCBwcm9wc1xuXG5cbiAgICBpZiAodHlwZSAmJiB0eXBlLmRlZmF1bHRQcm9wcykge1xuICAgICAgdmFyIGRlZmF1bHRQcm9wcyA9IHR5cGUuZGVmYXVsdFByb3BzO1xuXG4gICAgICBmb3IgKHByb3BOYW1lIGluIGRlZmF1bHRQcm9wcykge1xuICAgICAgICBpZiAocHJvcHNbcHJvcE5hbWVdID09PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICBwcm9wc1twcm9wTmFtZV0gPSBkZWZhdWx0UHJvcHNbcHJvcE5hbWVdO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKGtleSB8fCByZWYpIHtcbiAgICAgIHZhciBkaXNwbGF5TmFtZSA9IHR5cGVvZiB0eXBlID09PSAnZnVuY3Rpb24nID8gdHlwZS5kaXNwbGF5TmFtZSB8fCB0eXBlLm5hbWUgfHwgJ1Vua25vd24nIDogdHlwZTtcblxuICAgICAgaWYgKGtleSkge1xuICAgICAgICBkZWZpbmVLZXlQcm9wV2FybmluZ0dldHRlcihwcm9wcywgZGlzcGxheU5hbWUpO1xuICAgICAgfVxuXG4gICAgICBpZiAocmVmKSB7XG4gICAgICAgIGRlZmluZVJlZlByb3BXYXJuaW5nR2V0dGVyKHByb3BzLCBkaXNwbGF5TmFtZSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgdmFyIGVsZW1lbnQgPSBSZWFjdEVsZW1lbnQodHlwZSwga2V5LCByZWYsIHNlbGYsIHNvdXJjZSwgUmVhY3RDdXJyZW50T3duZXIuY3VycmVudCwgcHJvcHMpO1xuXG4gICAgaWYgKHR5cGUgPT09IFJFQUNUX0ZSQUdNRU5UX1RZUEUpIHtcbiAgICAgIHZhbGlkYXRlRnJhZ21lbnRQcm9wcyhlbGVtZW50KTtcbiAgICB9XG5cbiAgICByZXR1cm4gZWxlbWVudDtcbiAgfVxufVxuXG5mdW5jdGlvbiBnZXREZWNsYXJhdGlvbkVycm9yQWRkZW5kdW0oKSB7XG4gIHtcbiAgICBpZiAoUmVhY3RDdXJyZW50T3duZXIuY3VycmVudCkge1xuICAgICAgdmFyIG5hbWUgPSBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoUmVhY3RDdXJyZW50T3duZXIuY3VycmVudC50eXBlKTtcblxuICAgICAgaWYgKG5hbWUpIHtcbiAgICAgICAgcmV0dXJuICdcXG5cXG5DaGVjayB0aGUgcmVuZGVyIG1ldGhvZCBvZiBgJyArIG5hbWUgKyAnYC4nO1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiAnJztcbiAgfVxufVxuLyoqXG4gKiBFbnN1cmUgdGhhdCBldmVyeSBlbGVtZW50IGVpdGhlciBpcyBwYXNzZWQgaW4gYSBzdGF0aWMgbG9jYXRpb24sIGluIGFuXG4gKiBhcnJheSB3aXRoIGFuIGV4cGxpY2l0IGtleXMgcHJvcGVydHkgZGVmaW5lZCwgb3IgaW4gYW4gb2JqZWN0IGxpdGVyYWxcbiAqIHdpdGggdmFsaWQga2V5IHByb3BlcnR5LlxuICpcbiAqIEBpbnRlcm5hbFxuICogQHBhcmFtIHtSZWFjdE5vZGV9IG5vZGUgU3RhdGljYWxseSBwYXNzZWQgY2hpbGQgb2YgYW55IHR5cGUuXG4gKiBAcGFyYW0geyp9IHBhcmVudFR5cGUgbm9kZSdzIHBhcmVudCdzIHR5cGUuXG4gKi9cblxuXG5mdW5jdGlvbiB2YWxpZGF0ZUNoaWxkS2V5cyhub2RlLCBwYXJlbnRUeXBlKSB7XG4gIHtcbiAgICBpZiAodHlwZW9mIG5vZGUgIT09ICdvYmplY3QnIHx8ICFub2RlKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuXG4gICAgaWYgKG5vZGUuJCR0eXBlb2YgPT09IFJFQUNUX0NMSUVOVF9SRUZFUkVOQ0UpIDsgZWxzZSBpZiAoaXNBcnJheShub2RlKSkge1xuICAgICAgZm9yICh2YXIgaSA9IDA7IGkgPCBub2RlLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgIHZhciBjaGlsZCA9IG5vZGVbaV07XG5cbiAgICAgICAgaWYgKGlzVmFsaWRFbGVtZW50KGNoaWxkKSkge1xuICAgICAgICAgIHZhbGlkYXRlRXhwbGljaXRLZXkoY2hpbGQsIHBhcmVudFR5cGUpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChpc1ZhbGlkRWxlbWVudChub2RlKSkge1xuICAgICAgLy8gVGhpcyBlbGVtZW50IHdhcyBwYXNzZWQgaW4gYSB2YWxpZCBsb2NhdGlvbi5cbiAgICAgIGlmIChub2RlLl9zdG9yZSkge1xuICAgICAgICBub2RlLl9zdG9yZS52YWxpZGF0ZWQgPSB0cnVlO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICB2YXIgaXRlcmF0b3JGbiA9IGdldEl0ZXJhdG9yRm4obm9kZSk7XG5cbiAgICAgIGlmICh0eXBlb2YgaXRlcmF0b3JGbiA9PT0gJ2Z1bmN0aW9uJykge1xuICAgICAgICAvLyBFbnRyeSBpdGVyYXRvcnMgdXNlZCB0byBwcm92aWRlIGltcGxpY2l0IGtleXMsXG4gICAgICAgIC8vIGJ1dCBub3cgd2UgcHJpbnQgYSBzZXBhcmF0ZSB3YXJuaW5nIGZvciB0aGVtIGxhdGVyLlxuICAgICAgICBpZiAoaXRlcmF0b3JGbiAhPT0gbm9kZS5lbnRyaWVzKSB7XG4gICAgICAgICAgdmFyIGl0ZXJhdG9yID0gaXRlcmF0b3JGbi5jYWxsKG5vZGUpO1xuICAgICAgICAgIHZhciBzdGVwO1xuXG4gICAgICAgICAgd2hpbGUgKCEoc3RlcCA9IGl0ZXJhdG9yLm5leHQoKSkuZG9uZSkge1xuICAgICAgICAgICAgaWYgKGlzVmFsaWRFbGVtZW50KHN0ZXAudmFsdWUpKSB7XG4gICAgICAgICAgICAgIHZhbGlkYXRlRXhwbGljaXRLZXkoc3RlcC52YWx1ZSwgcGFyZW50VHlwZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9XG59XG4vKipcbiAqIFZlcmlmaWVzIHRoZSBvYmplY3QgaXMgYSBSZWFjdEVsZW1lbnQuXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL3JlYWN0LWFwaS5odG1sI2lzdmFsaWRlbGVtZW50XG4gKiBAcGFyYW0gez9vYmplY3R9IG9iamVjdFxuICogQHJldHVybiB7Ym9vbGVhbn0gVHJ1ZSBpZiBgb2JqZWN0YCBpcyBhIFJlYWN0RWxlbWVudC5cbiAqIEBmaW5hbFxuICovXG5cblxuZnVuY3Rpb24gaXNWYWxpZEVsZW1lbnQob2JqZWN0KSB7XG4gIHJldHVybiB0eXBlb2Ygb2JqZWN0ID09PSAnb2JqZWN0JyAmJiBvYmplY3QgIT09IG51bGwgJiYgb2JqZWN0LiQkdHlwZW9mID09PSBSRUFDVF9FTEVNRU5UX1RZUEU7XG59XG52YXIgb3duZXJIYXNLZXlVc2VXYXJuaW5nID0ge307XG4vKipcbiAqIFdhcm4gaWYgdGhlIGVsZW1lbnQgZG9lc24ndCBoYXZlIGFuIGV4cGxpY2l0IGtleSBhc3NpZ25lZCB0byBpdC5cbiAqIFRoaXMgZWxlbWVudCBpcyBpbiBhbiBhcnJheS4gVGhlIGFycmF5IGNvdWxkIGdyb3cgYW5kIHNocmluayBvciBiZVxuICogcmVvcmRlcmVkLiBBbGwgY2hpbGRyZW4gdGhhdCBoYXZlbid0IGFscmVhZHkgYmVlbiB2YWxpZGF0ZWQgYXJlIHJlcXVpcmVkIHRvXG4gKiBoYXZlIGEgXCJrZXlcIiBwcm9wZXJ0eSBhc3NpZ25lZCB0byBpdC4gRXJyb3Igc3RhdHVzZXMgYXJlIGNhY2hlZCBzbyBhIHdhcm5pbmdcbiAqIHdpbGwgb25seSBiZSBzaG93biBvbmNlLlxuICpcbiAqIEBpbnRlcm5hbFxuICogQHBhcmFtIHtSZWFjdEVsZW1lbnR9IGVsZW1lbnQgRWxlbWVudCB0aGF0IHJlcXVpcmVzIGEga2V5LlxuICogQHBhcmFtIHsqfSBwYXJlbnRUeXBlIGVsZW1lbnQncyBwYXJlbnQncyB0eXBlLlxuICovXG5cbmZ1bmN0aW9uIHZhbGlkYXRlRXhwbGljaXRLZXkoZWxlbWVudCwgcGFyZW50VHlwZSkge1xuICB7XG4gICAgaWYgKCFlbGVtZW50Ll9zdG9yZSB8fCBlbGVtZW50Ll9zdG9yZS52YWxpZGF0ZWQgfHwgZWxlbWVudC5rZXkgIT0gbnVsbCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGVsZW1lbnQuX3N0b3JlLnZhbGlkYXRlZCA9IHRydWU7XG4gICAgdmFyIGN1cnJlbnRDb21wb25lbnRFcnJvckluZm8gPSBnZXRDdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvKHBhcmVudFR5cGUpO1xuXG4gICAgaWYgKG93bmVySGFzS2V5VXNlV2FybmluZ1tjdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvXSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIG93bmVySGFzS2V5VXNlV2FybmluZ1tjdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvXSA9IHRydWU7IC8vIFVzdWFsbHkgdGhlIGN1cnJlbnQgb3duZXIgaXMgdGhlIG9mZmVuZGVyLCBidXQgaWYgaXQgYWNjZXB0cyBjaGlsZHJlbiBhcyBhXG4gICAgLy8gcHJvcGVydHksIGl0IG1heSBiZSB0aGUgY3JlYXRvciBvZiB0aGUgY2hpbGQgdGhhdCdzIHJlc3BvbnNpYmxlIGZvclxuICAgIC8vIGFzc2lnbmluZyBpdCBhIGtleS5cblxuICAgIHZhciBjaGlsZE93bmVyID0gJyc7XG5cbiAgICBpZiAoZWxlbWVudCAmJiBlbGVtZW50Ll9vd25lciAmJiBlbGVtZW50Ll9vd25lciAhPT0gUmVhY3RDdXJyZW50T3duZXIuY3VycmVudCkge1xuICAgICAgLy8gR2l2ZSB0aGUgY29tcG9uZW50IHRoYXQgb3JpZ2luYWxseSBjcmVhdGVkIHRoaXMgY2hpbGQuXG4gICAgICBjaGlsZE93bmVyID0gXCIgSXQgd2FzIHBhc3NlZCBhIGNoaWxkIGZyb20gXCIgKyBnZXRDb21wb25lbnROYW1lRnJvbVR5cGUoZWxlbWVudC5fb3duZXIudHlwZSkgKyBcIi5cIjtcbiAgICB9XG5cbiAgICBzZXRDdXJyZW50bHlWYWxpZGF0aW5nRWxlbWVudChlbGVtZW50KTtcblxuICAgIGVycm9yKCdFYWNoIGNoaWxkIGluIGEgbGlzdCBzaG91bGQgaGF2ZSBhIHVuaXF1ZSBcImtleVwiIHByb3AuJyArICclcyVzIFNlZSBodHRwczovL3JlYWN0anMub3JnL2xpbmsvd2FybmluZy1rZXlzIGZvciBtb3JlIGluZm9ybWF0aW9uLicsIGN1cnJlbnRDb21wb25lbnRFcnJvckluZm8sIGNoaWxkT3duZXIpO1xuXG4gICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQobnVsbCk7XG4gIH1cbn1cblxuZnVuY3Rpb24gc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQoZWxlbWVudCkge1xuICB7XG4gICAgaWYgKGVsZW1lbnQpIHtcbiAgICAgIHZhciBvd25lciA9IGVsZW1lbnQuX293bmVyO1xuICAgICAgdmFyIHN0YWNrID0gZGVzY3JpYmVVbmtub3duRWxlbWVudFR5cGVGcmFtZUluREVWKGVsZW1lbnQudHlwZSwgb3duZXIgPyBvd25lci50eXBlIDogbnVsbCk7XG4gICAgICBSZWFjdERlYnVnQ3VycmVudEZyYW1lLnNldEV4dHJhU3RhY2tGcmFtZShzdGFjayk7XG4gICAgfSBlbHNlIHtcbiAgICAgIFJlYWN0RGVidWdDdXJyZW50RnJhbWUuc2V0RXh0cmFTdGFja0ZyYW1lKG51bGwpO1xuICAgIH1cbiAgfVxufVxuXG5mdW5jdGlvbiBnZXRDdXJyZW50Q29tcG9uZW50RXJyb3JJbmZvKHBhcmVudFR5cGUpIHtcbiAge1xuICAgIHZhciBpbmZvID0gZ2V0RGVjbGFyYXRpb25FcnJvckFkZGVuZHVtKCk7XG5cbiAgICBpZiAoIWluZm8pIHtcbiAgICAgIHZhciBwYXJlbnROYW1lID0gZ2V0Q29tcG9uZW50TmFtZUZyb21UeXBlKHBhcmVudFR5cGUpO1xuXG4gICAgICBpZiAocGFyZW50TmFtZSkge1xuICAgICAgICBpbmZvID0gXCJcXG5cXG5DaGVjayB0aGUgdG9wLWxldmVsIHJlbmRlciBjYWxsIHVzaW5nIDxcIiArIHBhcmVudE5hbWUgKyBcIj4uXCI7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIGluZm87XG4gIH1cbn1cbi8qKlxuICogR2l2ZW4gYSBmcmFnbWVudCwgdmFsaWRhdGUgdGhhdCBpdCBjYW4gb25seSBiZSBwcm92aWRlZCB3aXRoIGZyYWdtZW50IHByb3BzXG4gKiBAcGFyYW0ge1JlYWN0RWxlbWVudH0gZnJhZ21lbnRcbiAqL1xuXG5cbmZ1bmN0aW9uIHZhbGlkYXRlRnJhZ21lbnRQcm9wcyhmcmFnbWVudCkge1xuICAvLyBUT0RPOiBNb3ZlIHRoaXMgdG8gcmVuZGVyIHBoYXNlIGluc3RlYWQgb2YgYXQgZWxlbWVudCBjcmVhdGlvbi5cbiAge1xuICAgIHZhciBrZXlzID0gT2JqZWN0LmtleXMoZnJhZ21lbnQucHJvcHMpO1xuXG4gICAgZm9yICh2YXIgaSA9IDA7IGkgPCBrZXlzLmxlbmd0aDsgaSsrKSB7XG4gICAgICB2YXIga2V5ID0ga2V5c1tpXTtcblxuICAgICAgaWYgKGtleSAhPT0gJ2NoaWxkcmVuJyAmJiBrZXkgIT09ICdrZXknKSB7XG4gICAgICAgIHNldEN1cnJlbnRseVZhbGlkYXRpbmdFbGVtZW50KGZyYWdtZW50KTtcblxuICAgICAgICBlcnJvcignSW52YWxpZCBwcm9wIGAlc2Agc3VwcGxpZWQgdG8gYFJlYWN0LkZyYWdtZW50YC4gJyArICdSZWFjdC5GcmFnbWVudCBjYW4gb25seSBoYXZlIGBrZXlgIGFuZCBgY2hpbGRyZW5gIHByb3BzLicsIGtleSk7XG5cbiAgICAgICAgc2V0Q3VycmVudGx5VmFsaWRhdGluZ0VsZW1lbnQobnVsbCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfVxuICAgIH1cblxuICAgIGlmIChmcmFnbWVudC5yZWYgIT09IG51bGwpIHtcbiAgICAgIHNldEN1cnJlbnRseVZhbGlkYXRpbmdFbGVtZW50KGZyYWdtZW50KTtcblxuICAgICAgZXJyb3IoJ0ludmFsaWQgYXR0cmlidXRlIGByZWZgIHN1cHBsaWVkIHRvIGBSZWFjdC5GcmFnbWVudGAuJyk7XG5cbiAgICAgIHNldEN1cnJlbnRseVZhbGlkYXRpbmdFbGVtZW50KG51bGwpO1xuICAgIH1cbiAgfVxufVxuXG52YXIganN4REVWID0ganN4REVWJDEgO1xuXG5leHBvcnRzLkZyYWdtZW50ID0gUkVBQ1RfRlJBR01FTlRfVFlQRTtcbmV4cG9ydHMuanN4REVWID0ganN4REVWO1xuICB9KSgpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/NWM1OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cmowee%5C%5CDocuments%5C%5Caugment-projects%5C%5Cchatbot%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);