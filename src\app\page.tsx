'use client'

import { useState, useEffect } from 'react'
import ChatInterface from '@/components/ChatInterface'
import Header from '@/components/Header'
import MemoryPanel from '@/components/MemoryPanel'
import { Message } from '@/types/chat'
import { useJarvisMemory } from '@/hooks/useJarvisMemory'

export default function Home() {
  const {
    userProfile,
    memoryContext,
    isInitialized,
    addMessage,
    updateUserInfo,
    getPersonalizedPrompt,
    getMemoryStats
  } = useJarvisMemory()

  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [showMemoryPanel, setShowMemoryPanel] = useState(false)

  // Initialize welcome message based on memory
  useEffect(() => {
    if (isInitialized && messages.length === 0) {
      const welcomeMessage: Message = {
        id: '1',
        content: userProfile?.name
          ? `Welcome back, ${userProfile.name}! I'm <PERSON>, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?`
          : "Good day! I'm <PERSON>, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?",
        role: 'assistant',
        timestamp: new Date(),
      }
      setMessages([welcomeMessage])
      addMessage(welcomeMessage)
    }
  }, [isInitialized, userProfile])

  const handleSendMessage = async (content: string) => {
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      role: 'user',
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    addMessage(userMessage) // Add to memory system
    setIsLoading(true)

    try {
      // Include memory context in the request
      const personalizedPrompt = getPersonalizedPrompt()

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: content,
          history: messages,
          memoryContext: personalizedPrompt
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to get response')
      }

      const data = await response.json()
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.message || "I'm still learning! This is a placeholder response.",
        role: 'assistant',
        timestamp: new Date(),
        metadata: data.metadata
      }

      setMessages(prev => [...prev, assistantMessage])
      addMessage(assistantMessage) // Add to memory system
    } catch (error) {
      console.error('Error sending message:', error)
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: "Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!",
        role: 'assistant',
        timestamp: new Date(),
      }
      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <main className="flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark">
      <Header onMemoryClick={() => setShowMemoryPanel(!showMemoryPanel)} />
      <div className="flex flex-1 overflow-hidden relative">
        <div className="flex-1">
          <ChatInterface
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
          />
        </div>

        {/* Memory Panel */}
        {showMemoryPanel && (
          <MemoryPanel
            userProfile={userProfile}
            memoryStats={getMemoryStats()}
            onUpdateProfile={updateUserInfo}
            onClose={() => setShowMemoryPanel(false)}
          />
        )}
      </div>
    </main>
  )
}
