"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ChatInterface */ \"(app-pages-browser)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.tsx\");\n/* harmony import */ var _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useJarvisMemory */ \"(app-pages-browser)/./src/hooks/useJarvisMemory.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const { userProfile, memoryContext, isInitialized, addMessage, updateUserInfo, getPersonalizedPrompt, getMemoryStats } = (0,_hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showMemoryPanel, setShowMemoryPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize welcome message based on memory\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInitialized && messages.length === 0) {\n            const welcomeMessage = {\n                id: \"1\",\n                content: (userProfile === null || userProfile === void 0 ? void 0 : userProfile.name) ? \"Welcome back, \".concat(userProfile.name, \"! I'm Jarvis, and I remember our previous conversations. I'm ready to assist you with the same professional efficiency and personality you've come to expect. How may I help you today?\") : \"Good day! I'm Jarvis, your advanced AI assistant. I'm here to help you with any questions, provide real-time information, and assist with various tasks. I combine professional efficiency with a touch of personality - think of me as your reliable digital companion. How may I be of service today?\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages([\n                welcomeMessage\n            ]);\n            addMessage(welcomeMessage);\n        }\n    }, [\n        isInitialized,\n        userProfile\n    ]);\n    const handleSendMessage = async (content)=>{\n        const userMessage = {\n            id: Date.now().toString(),\n            content,\n            role: \"user\",\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        try {\n            // TODO: Replace with actual API call\n            const response = await fetch(\"/api/chat\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    message: content,\n                    history: messages\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to get response\");\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                id: (Date.now() + 1).toString(),\n                content: data.message || \"I'm still learning! This is a placeholder response.\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            const errorMessage = {\n                id: (Date.now() + 1).toString(),\n                content: \"Oops! Something went wrong. I'm having trouble connecting right now. Try again in a moment!\",\n                role: \"assistant\",\n                timestamp: new Date()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"flex flex-col h-screen bg-gradient-to-br from-grok-dark via-grok-gray to-grok-dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    messages: messages,\n                    onSendMessage: handleSendMessage,\n                    isLoading: isLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\chatbot\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"3dB+vyKJP+vxT+qSB199HXUCEXw=\", false, function() {\n    return [\n        _hooks_useJarvisMemory__WEBPACK_IMPORTED_MODULE_4__.useJarvisMemory\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useJarvisMemory.ts":
/*!**************************************!*\
  !*** ./src/hooks/useJarvisMemory.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useJarvisMemory: function() { return /* binding */ useJarvisMemory; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/memory-system */ \"(app-pages-browser)/./src/lib/memory-system.ts\");\n/* __next_internal_client_entry_do_not_use__ useJarvisMemory auto */ \n\nfunction useJarvisMemory() {\n    const [userProfile, setUserProfile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [memoryContext, setMemoryContext] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isInitialized, setIsInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    // Initialize memory system\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initializeMemory = async ()=>{\n            try {\n                const profile = await _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser();\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n                console.log(\"Jarvis memory system initialized\");\n            } catch (error) {\n                console.error(\"Failed to initialize memory system:\", error);\n                setIsInitialized(true) // Still mark as initialized to prevent infinite loading\n                ;\n            }\n        };\n        initializeMemory();\n    }, []);\n    // Add message to memory\n    const addMessage = (message)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.addMessage(message);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n        // Update profile state if it changed\n        const updatedProfile = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext();\n        if (updatedProfile) {\n            // Trigger a re-render by updating the timestamp\n            setUserProfile((prev)=>prev ? {\n                    ...prev,\n                    lastActive: new Date()\n                } : null);\n        }\n    };\n    // Update user information\n    const updateUserInfo = (updates)=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.updateUserInfo(updates);\n        setUserProfile((prev)=>prev ? {\n                ...prev,\n                ...updates\n            } : null);\n        setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n    };\n    // Get personalized system prompt\n    const getPersonalizedPrompt = ()=>{\n        if (!memoryContext) return \"\";\n        const conversationSummary = _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getConversationSummary();\n        return \"\\n\".concat(conversationSummary, \"\\n\\nPERSONALIZATION INSTRUCTIONS:\\n- Adapt your communication style to match the user's preference: \").concat(memoryContext.userPreferences.communicationStyle, \"\\n- Provide \").concat(memoryContext.userPreferences.responseLength, \" responses\\n- Reference their interests when relevant: \").concat(memoryContext.recentTopics.join(\", \"), \"\\n- Maintain the \").concat(memoryContext.userPreferences.personalityMode, \" personality mode\\n- Use context from previous conversations when appropriate\\n- Be more personal and contextually aware in your responses\\n    \").trim();\n    };\n    // Clear all memory\n    const clearMemory = ()=>{\n        _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.clearMemory();\n        setUserProfile(null);\n        setMemoryContext(null);\n        setIsInitialized(false);\n        // Reinitialize\n        setTimeout(()=>{\n            _lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.initializeUser().then((profile)=>{\n                setUserProfile(profile);\n                setMemoryContext(_lib_memory_system__WEBPACK_IMPORTED_MODULE_1__.jarvisMemory.getMemoryContext());\n                setIsInitialized(true);\n            });\n        }, 100);\n    };\n    // Get memory stats for display\n    const getMemoryStats = ()=>{\n        if (!userProfile || !memoryContext) return null;\n        return {\n            interests: userProfile.personalInfo.interests.length,\n            conversationDays: Math.floor((new Date().getTime() - new Date(userProfile.createdAt).getTime()) / (1000 * 60 * 60 * 24)),\n            communicationStyle: userProfile.preferences.communicationStyle,\n            personalityMode: userProfile.preferences.personalityMode,\n            lastActive: userProfile.lastActive\n        };\n    };\n    return {\n        userProfile,\n        memoryContext,\n        isInitialized,\n        addMessage,\n        updateUserInfo,\n        getPersonalizedPrompt,\n        clearMemory,\n        getMemoryStats\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useJarvisMemory.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/memory-system.ts":
/*!**********************************!*\
  !*** ./src/lib/memory-system.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   jarvisMemory: function() { return /* binding */ jarvisMemory; }\n/* harmony export */ });\n// Jarvis Memory System - Advanced AI Memory and Learning\nclass JarvisMemorySystem {\n    // Initialize or load user profile\n    async initializeUser() {\n        let userId = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : \"default\";\n        // Try to load existing profile from localStorage\n        const stored = localStorage.getItem(\"jarvis_profile_\".concat(userId));\n        if (stored) {\n            var _this_userProfile;\n            this.userProfile = JSON.parse(stored);\n            console.log(\"Loaded existing user profile:\", ((_this_userProfile = this.userProfile) === null || _this_userProfile === void 0 ? void 0 : _this_userProfile.name) || \"Anonymous\");\n        } else {\n            // Create new profile\n            this.userProfile = {\n                id: userId,\n                preferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                personalInfo: {\n                    interests: []\n                },\n                conversationHistory: [],\n                learningData: [],\n                createdAt: new Date(),\n                lastActive: new Date()\n            };\n            this.saveProfile();\n            console.log(\"Created new user profile\");\n        }\n        return this.userProfile;\n    }\n    // Save profile to localStorage\n    saveProfile() {\n        if (this.userProfile) {\n            localStorage.setItem(\"jarvis_profile_\".concat(this.userProfile.id), JSON.stringify(this.userProfile));\n        }\n    }\n    // Add message to conversation buffer\n    addMessage(message) {\n        this.conversationBuffer.push(message);\n        // Keep only last 20 messages in buffer\n        if (this.conversationBuffer.length > 20) {\n            this.conversationBuffer = this.conversationBuffer.slice(-20);\n        }\n        // Learn from user messages\n        if (message.role === \"user\") {\n            this.learnFromMessage(message.content);\n        }\n    }\n    // Extract learning patterns from user messages\n    learnFromMessage(content) {\n        if (!this.userProfile) return;\n        const words = content.toLowerCase().split(/\\s+/);\n        const topics = this.extractTopics(content);\n        const mood = this.detectMood(content);\n        // Update topics of interest\n        topics.forEach((topic)=>{\n            if (!this.userProfile.personalInfo.interests.includes(topic)) {\n                this.userProfile.personalInfo.interests.push(topic);\n            }\n        });\n        // Update communication style based on patterns\n        this.updateCommunicationStyle(content);\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Extract topics from message content\n    extractTopics(content) {\n        const topicKeywords = {\n            \"technology\": [\n                \"tech\",\n                \"computer\",\n                \"software\",\n                \"programming\",\n                \"ai\",\n                \"code\",\n                \"development\"\n            ],\n            \"science\": [\n                \"science\",\n                \"research\",\n                \"experiment\",\n                \"theory\",\n                \"physics\",\n                \"chemistry\",\n                \"biology\"\n            ],\n            \"business\": [\n                \"business\",\n                \"work\",\n                \"job\",\n                \"career\",\n                \"company\",\n                \"meeting\",\n                \"project\"\n            ],\n            \"entertainment\": [\n                \"movie\",\n                \"music\",\n                \"game\",\n                \"book\",\n                \"show\",\n                \"entertainment\",\n                \"fun\"\n            ],\n            \"health\": [\n                \"health\",\n                \"fitness\",\n                \"exercise\",\n                \"diet\",\n                \"medical\",\n                \"wellness\"\n            ],\n            \"travel\": [\n                \"travel\",\n                \"trip\",\n                \"vacation\",\n                \"country\",\n                \"city\",\n                \"flight\",\n                \"hotel\"\n            ],\n            \"food\": [\n                \"food\",\n                \"recipe\",\n                \"cooking\",\n                \"restaurant\",\n                \"meal\",\n                \"cuisine\"\n            ],\n            \"sports\": [\n                \"sport\",\n                \"football\",\n                \"basketball\",\n                \"soccer\",\n                \"tennis\",\n                \"game\",\n                \"team\"\n            ]\n        };\n        const topics = [];\n        const lowerContent = content.toLowerCase();\n        Object.entries(topicKeywords).forEach((param)=>{\n            let [topic, keywords] = param;\n            if (keywords.some((keyword)=>lowerContent.includes(keyword))) {\n                topics.push(topic);\n            }\n        });\n        return topics;\n    }\n    // Detect user mood from message\n    detectMood(content) {\n        const positiveWords = [\n            \"great\",\n            \"awesome\",\n            \"excellent\",\n            \"love\",\n            \"amazing\",\n            \"perfect\",\n            \"wonderful\"\n        ];\n        const negativeWords = [\n            \"bad\",\n            \"terrible\",\n            \"awful\",\n            \"hate\",\n            \"frustrated\",\n            \"annoying\",\n            \"problem\"\n        ];\n        const excitedWords = [\n            \"excited\",\n            \"can't wait\",\n            \"!\",\n            \"wow\",\n            \"incredible\",\n            \"fantastic\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (excitedWords.some((word)=>lowerContent.includes(word))) return \"excited\";\n        if (positiveWords.some((word)=>lowerContent.includes(word))) return \"positive\";\n        if (negativeWords.some((word)=>lowerContent.includes(word))) return \"frustrated\";\n        return \"neutral\";\n    }\n    // Update communication style based on user patterns\n    updateCommunicationStyle(content) {\n        if (!this.userProfile) return;\n        const formalIndicators = [\n            \"please\",\n            \"thank you\",\n            \"could you\",\n            \"would you\"\n        ];\n        const casualIndicators = [\n            \"hey\",\n            \"what's up\",\n            \"cool\",\n            \"awesome\",\n            \"yeah\"\n        ];\n        const technicalIndicators = [\n            \"algorithm\",\n            \"function\",\n            \"variable\",\n            \"database\",\n            \"api\"\n        ];\n        const lowerContent = content.toLowerCase();\n        if (technicalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"technical\";\n        } else if (formalIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"formal\";\n        } else if (casualIndicators.some((word)=>lowerContent.includes(word))) {\n            this.userProfile.preferences.communicationStyle = \"casual\";\n        }\n    }\n    // Get memory context for AI responses\n    getMemoryContext() {\n        if (!this.userProfile) {\n            return {\n                recentTopics: [],\n                userPreferences: {\n                    communicationStyle: \"friendly\",\n                    responseLength: \"detailed\",\n                    topics: [],\n                    personalityMode: \"professional\"\n                },\n                relevantHistory: [],\n                personalContext: []\n            };\n        }\n        const recentTopics = this.userProfile.personalInfo.interests.slice(-5);\n        const relevantHistory = this.conversationBuffer.slice(-5).map((msg)=>\"\".concat(msg.role, \": \").concat(msg.content.substring(0, 100)));\n        const personalContext = [\n            this.userProfile.name ? \"User's name: \".concat(this.userProfile.name) : \"\",\n            this.userProfile.personalInfo.occupation ? \"Occupation: \".concat(this.userProfile.personalInfo.occupation) : \"\",\n            \"Interests: \".concat(this.userProfile.personalInfo.interests.join(\", \")),\n            \"Communication style: \".concat(this.userProfile.preferences.communicationStyle),\n            \"Preferred response length: \".concat(this.userProfile.preferences.responseLength)\n        ].filter(Boolean);\n        return {\n            recentTopics,\n            userPreferences: this.userProfile.preferences,\n            relevantHistory,\n            personalContext\n        };\n    }\n    // Update user information\n    updateUserInfo(updates) {\n        if (!this.userProfile) return;\n        this.userProfile = {\n            ...this.userProfile,\n            ...updates\n        };\n        this.userProfile.lastActive = new Date();\n        this.saveProfile();\n    }\n    // Get conversation summary\n    getConversationSummary() {\n        const context = this.getMemoryContext();\n        return \"\\nMEMORY CONTEXT:\\n- User interests: \".concat(context.recentTopics.join(\", \") || \"None recorded yet\", \"\\n- Communication style: \").concat(context.userPreferences.communicationStyle, \"\\n- Preferred response length: \").concat(context.userPreferences.responseLength, \"\\n- Personal context: \").concat(context.personalContext.join(\"; \"), \"\\n- Recent conversation: \").concat(context.relevantHistory.slice(-3).join(\"; \"), \"\\n\\nUse this context to provide personalized, contextually aware responses.\\n    \").trim();\n    }\n    // Clear memory (for privacy)\n    clearMemory() {\n        if (this.userProfile) {\n            localStorage.removeItem(\"jarvis_profile_\".concat(this.userProfile.id));\n        }\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n    constructor(){\n        this.userProfile = null;\n        this.conversationBuffer = [];\n    }\n}\n// Export singleton instance\nconst jarvisMemory = new JarvisMemorySystem();\n/* harmony default export */ __webpack_exports__[\"default\"] = (jarvisMemory);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvbWVtb3J5LXN5c3RlbS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEseURBQXlEO0FBa0R6RCxNQUFNQTtJQUlKLGtDQUFrQztJQUNsQyxNQUFNQyxpQkFBaUU7WUFBbERDLFNBQUFBLGlFQUFpQjtRQUNwQyxpREFBaUQ7UUFDakQsTUFBTUMsU0FBU0MsYUFBYUMsT0FBTyxDQUFDLGtCQUF5QixPQUFQSDtRQUV0RCxJQUFJQyxRQUFRO2dCQUVtQztZQUQ3QyxJQUFJLENBQUNHLFdBQVcsR0FBR0MsS0FBS0MsS0FBSyxDQUFDTDtZQUM5Qk0sUUFBUUMsR0FBRyxDQUFDLGlDQUFpQywwQkFBSSxDQUFDSixXQUFXLGNBQWhCLDBEQUFrQkssSUFBSSxLQUFJO1FBQ3pFLE9BQU87WUFDTCxxQkFBcUI7WUFDckIsSUFBSSxDQUFDTCxXQUFXLEdBQUc7Z0JBQ2pCTSxJQUFJVjtnQkFDSlcsYUFBYTtvQkFDWEMsb0JBQW9CO29CQUNwQkMsZ0JBQWdCO29CQUNoQkMsUUFBUSxFQUFFO29CQUNWQyxpQkFBaUI7Z0JBQ25CO2dCQUNBQyxjQUFjO29CQUNaQyxXQUFXLEVBQUU7Z0JBQ2Y7Z0JBQ0FDLHFCQUFxQixFQUFFO2dCQUN2QkMsY0FBYyxFQUFFO2dCQUNoQkMsV0FBVyxJQUFJQztnQkFDZkMsWUFBWSxJQUFJRDtZQUNsQjtZQUNBLElBQUksQ0FBQ0UsV0FBVztZQUNoQmhCLFFBQVFDLEdBQUcsQ0FBQztRQUNkO1FBRUEsT0FBTyxJQUFJLENBQUNKLFdBQVc7SUFDekI7SUFFQSwrQkFBK0I7SUFDdkJtQixjQUFvQjtRQUMxQixJQUFJLElBQUksQ0FBQ25CLFdBQVcsRUFBRTtZQUNwQkYsYUFBYXNCLE9BQU8sQ0FBQyxrQkFBc0MsT0FBcEIsSUFBSSxDQUFDcEIsV0FBVyxDQUFDTSxFQUFFLEdBQUlMLEtBQUtvQixTQUFTLENBQUMsSUFBSSxDQUFDckIsV0FBVztRQUMvRjtJQUNGO0lBRUEscUNBQXFDO0lBQ3JDc0IsV0FBV0MsT0FBZ0IsRUFBUTtRQUNqQyxJQUFJLENBQUNDLGtCQUFrQixDQUFDQyxJQUFJLENBQUNGO1FBRTdCLHVDQUF1QztRQUN2QyxJQUFJLElBQUksQ0FBQ0Msa0JBQWtCLENBQUNFLE1BQU0sR0FBRyxJQUFJO1lBQ3ZDLElBQUksQ0FBQ0Ysa0JBQWtCLEdBQUcsSUFBSSxDQUFDQSxrQkFBa0IsQ0FBQ0csS0FBSyxDQUFDLENBQUM7UUFDM0Q7UUFFQSwyQkFBMkI7UUFDM0IsSUFBSUosUUFBUUssSUFBSSxLQUFLLFFBQVE7WUFDM0IsSUFBSSxDQUFDQyxnQkFBZ0IsQ0FBQ04sUUFBUU8sT0FBTztRQUN2QztJQUNGO0lBRUEsK0NBQStDO0lBQ3ZDRCxpQkFBaUJDLE9BQWUsRUFBUTtRQUM5QyxJQUFJLENBQUMsSUFBSSxDQUFDOUIsV0FBVyxFQUFFO1FBRXZCLE1BQU0rQixRQUFRRCxRQUFRRSxXQUFXLEdBQUdDLEtBQUssQ0FBQztRQUMxQyxNQUFNdkIsU0FBUyxJQUFJLENBQUN3QixhQUFhLENBQUNKO1FBQ2xDLE1BQU1LLE9BQU8sSUFBSSxDQUFDQyxVQUFVLENBQUNOO1FBRTdCLDRCQUE0QjtRQUM1QnBCLE9BQU8yQixPQUFPLENBQUNDLENBQUFBO1lBQ2IsSUFBSSxDQUFDLElBQUksQ0FBQ3RDLFdBQVcsQ0FBRVksWUFBWSxDQUFDQyxTQUFTLENBQUMwQixRQUFRLENBQUNELFFBQVE7Z0JBQzdELElBQUksQ0FBQ3RDLFdBQVcsQ0FBRVksWUFBWSxDQUFDQyxTQUFTLENBQUNZLElBQUksQ0FBQ2E7WUFDaEQ7UUFDRjtRQUVBLCtDQUErQztRQUMvQyxJQUFJLENBQUNFLHdCQUF3QixDQUFDVjtRQUU5QixJQUFJLENBQUM5QixXQUFXLENBQUNrQixVQUFVLEdBQUcsSUFBSUQ7UUFDbEMsSUFBSSxDQUFDRSxXQUFXO0lBQ2xCO0lBRUEsc0NBQXNDO0lBQzlCZSxjQUFjSixPQUFlLEVBQVk7UUFDL0MsTUFBTVcsZ0JBQWdCO1lBQ3BCLGNBQWM7Z0JBQUM7Z0JBQVE7Z0JBQVk7Z0JBQVk7Z0JBQWU7Z0JBQU07Z0JBQVE7YUFBYztZQUMxRixXQUFXO2dCQUFDO2dCQUFXO2dCQUFZO2dCQUFjO2dCQUFVO2dCQUFXO2dCQUFhO2FBQVU7WUFDN0YsWUFBWTtnQkFBQztnQkFBWTtnQkFBUTtnQkFBTztnQkFBVTtnQkFBVztnQkFBVzthQUFVO1lBQ2xGLGlCQUFpQjtnQkFBQztnQkFBUztnQkFBUztnQkFBUTtnQkFBUTtnQkFBUTtnQkFBaUI7YUFBTTtZQUNuRixVQUFVO2dCQUFDO2dCQUFVO2dCQUFXO2dCQUFZO2dCQUFRO2dCQUFXO2FBQVc7WUFDMUUsVUFBVTtnQkFBQztnQkFBVTtnQkFBUTtnQkFBWTtnQkFBVztnQkFBUTtnQkFBVTthQUFRO1lBQzlFLFFBQVE7Z0JBQUM7Z0JBQVE7Z0JBQVU7Z0JBQVc7Z0JBQWM7Z0JBQVE7YUFBVTtZQUN0RSxVQUFVO2dCQUFDO2dCQUFTO2dCQUFZO2dCQUFjO2dCQUFVO2dCQUFVO2dCQUFRO2FBQU87UUFDbkY7UUFFQSxNQUFNL0IsU0FBbUIsRUFBRTtRQUMzQixNQUFNZ0MsZUFBZVosUUFBUUUsV0FBVztRQUV4Q1csT0FBT0MsT0FBTyxDQUFDSCxlQUFlSixPQUFPLENBQUM7Z0JBQUMsQ0FBQ0MsT0FBT08sU0FBUztZQUN0RCxJQUFJQSxTQUFTQyxJQUFJLENBQUNDLENBQUFBLFVBQVdMLGFBQWFILFFBQVEsQ0FBQ1EsV0FBVztnQkFDNURyQyxPQUFPZSxJQUFJLENBQUNhO1lBQ2Q7UUFDRjtRQUVBLE9BQU81QjtJQUNUO0lBRUEsZ0NBQWdDO0lBQ3hCMEIsV0FBV04sT0FBZSxFQUFVO1FBQzFDLE1BQU1rQixnQkFBZ0I7WUFBQztZQUFTO1lBQVc7WUFBYTtZQUFRO1lBQVc7WUFBVztTQUFZO1FBQ2xHLE1BQU1DLGdCQUFnQjtZQUFDO1lBQU87WUFBWTtZQUFTO1lBQVE7WUFBYztZQUFZO1NBQVU7UUFDL0YsTUFBTUMsZUFBZTtZQUFDO1lBQVc7WUFBZTtZQUFLO1lBQU87WUFBYztTQUFZO1FBRXRGLE1BQU1SLGVBQWVaLFFBQVFFLFdBQVc7UUFFeEMsSUFBSWtCLGFBQWFKLElBQUksQ0FBQ0ssQ0FBQUEsT0FBUVQsYUFBYUgsUUFBUSxDQUFDWSxRQUFRLE9BQU87UUFDbkUsSUFBSUgsY0FBY0YsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVEsT0FBTztRQUNwRSxJQUFJRixjQUFjSCxJQUFJLENBQUNLLENBQUFBLE9BQVFULGFBQWFILFFBQVEsQ0FBQ1ksUUFBUSxPQUFPO1FBRXBFLE9BQU87SUFDVDtJQUVBLG9EQUFvRDtJQUM1Q1gseUJBQXlCVixPQUFlLEVBQVE7UUFDdEQsSUFBSSxDQUFDLElBQUksQ0FBQzlCLFdBQVcsRUFBRTtRQUV2QixNQUFNb0QsbUJBQW1CO1lBQUM7WUFBVTtZQUFhO1lBQWE7U0FBWTtRQUMxRSxNQUFNQyxtQkFBbUI7WUFBQztZQUFPO1lBQWM7WUFBUTtZQUFXO1NBQU87UUFDekUsTUFBTUMsc0JBQXNCO1lBQUM7WUFBYTtZQUFZO1lBQVk7WUFBWTtTQUFNO1FBRXBGLE1BQU1aLGVBQWVaLFFBQVFFLFdBQVc7UUFFeEMsSUFBSXNCLG9CQUFvQlIsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDakUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BELE9BQU8sSUFBSTRDLGlCQUFpQk4sSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDckUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BELE9BQU8sSUFBSTZDLGlCQUFpQlAsSUFBSSxDQUFDSyxDQUFBQSxPQUFRVCxhQUFhSCxRQUFRLENBQUNZLFFBQVE7WUFDckUsSUFBSSxDQUFDbkQsV0FBVyxDQUFDTyxXQUFXLENBQUNDLGtCQUFrQixHQUFHO1FBQ3BEO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFDdEMrQyxtQkFBa0M7UUFDaEMsSUFBSSxDQUFDLElBQUksQ0FBQ3ZELFdBQVcsRUFBRTtZQUNyQixPQUFPO2dCQUNMd0QsY0FBYyxFQUFFO2dCQUNoQkMsaUJBQWlCO29CQUNmakQsb0JBQW9CO29CQUNwQkMsZ0JBQWdCO29CQUNoQkMsUUFBUSxFQUFFO29CQUNWQyxpQkFBaUI7Z0JBQ25CO2dCQUNBK0MsaUJBQWlCLEVBQUU7Z0JBQ25CQyxpQkFBaUIsRUFBRTtZQUNyQjtRQUNGO1FBRUEsTUFBTUgsZUFBZSxJQUFJLENBQUN4RCxXQUFXLENBQUNZLFlBQVksQ0FBQ0MsU0FBUyxDQUFDYyxLQUFLLENBQUMsQ0FBQztRQUNwRSxNQUFNK0Isa0JBQWtCLElBQUksQ0FBQ2xDLGtCQUFrQixDQUM1Q0csS0FBSyxDQUFDLENBQUMsR0FDUGlDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBTyxHQUFnQkEsT0FBYkEsSUFBSWpDLElBQUksRUFBQyxNQUFrQyxPQUE5QmlDLElBQUkvQixPQUFPLENBQUNnQyxTQUFTLENBQUMsR0FBRztRQUV2RCxNQUFNSCxrQkFBa0I7WUFDdEIsSUFBSSxDQUFDM0QsV0FBVyxDQUFDSyxJQUFJLEdBQUcsZ0JBQXNDLE9BQXRCLElBQUksQ0FBQ0wsV0FBVyxDQUFDSyxJQUFJLElBQUs7WUFDbEUsSUFBSSxDQUFDTCxXQUFXLENBQUNZLFlBQVksQ0FBQ21ELFVBQVUsR0FBRyxlQUF3RCxPQUF6QyxJQUFJLENBQUMvRCxXQUFXLENBQUNZLFlBQVksQ0FBQ21ELFVBQVUsSUFBSztZQUN0RyxjQUFnRSxPQUFuRCxJQUFJLENBQUMvRCxXQUFXLENBQUNZLFlBQVksQ0FBQ0MsU0FBUyxDQUFDbUQsSUFBSSxDQUFDO1lBQzFELHdCQUF1RSxPQUFoRCxJQUFJLENBQUNoRSxXQUFXLENBQUNPLFdBQVcsQ0FBQ0Msa0JBQWtCO1lBQ3RFLDhCQUF5RSxPQUE1QyxJQUFJLENBQUNSLFdBQVcsQ0FBQ08sV0FBVyxDQUFDRSxjQUFjO1NBQzFFLENBQUN3RCxNQUFNLENBQUNDO1FBRVQsT0FBTztZQUNMVjtZQUNBQyxpQkFBaUIsSUFBSSxDQUFDekQsV0FBVyxDQUFDTyxXQUFXO1lBQzdDbUQ7WUFDQUM7UUFDRjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCUSxlQUFlQyxPQUE2QixFQUFRO1FBQ2xELElBQUksQ0FBQyxJQUFJLENBQUNwRSxXQUFXLEVBQUU7UUFFdkIsSUFBSSxDQUFDQSxXQUFXLEdBQUc7WUFBRSxHQUFHLElBQUksQ0FBQ0EsV0FBVztZQUFFLEdBQUdvRSxPQUFPO1FBQUM7UUFDckQsSUFBSSxDQUFDcEUsV0FBVyxDQUFDa0IsVUFBVSxHQUFHLElBQUlEO1FBQ2xDLElBQUksQ0FBQ0UsV0FBVztJQUNsQjtJQUVBLDJCQUEyQjtJQUMzQmtELHlCQUFpQztRQUMvQixNQUFNQyxVQUFVLElBQUksQ0FBQ2YsZ0JBQWdCO1FBRXJDLE9BQU8sd0NBR2NlLE9BRExBLFFBQVFkLFlBQVksQ0FBQ1EsSUFBSSxDQUFDLFNBQVMscUJBQW9CLDZCQUU1Q00sT0FETkEsUUFBUWIsZUFBZSxDQUFDakQsa0JBQWtCLEVBQUMsbUNBRTlDOEQsT0FEU0EsUUFBUWIsZUFBZSxDQUFDaEQsY0FBYyxFQUFDLDBCQUU3QzZELE9BREhBLFFBQVFYLGVBQWUsQ0FBQ0ssSUFBSSxDQUFDLE9BQU0sNkJBQ2EsT0FBN0NNLFFBQVFaLGVBQWUsQ0FBQy9CLEtBQUssQ0FBQyxDQUFDLEdBQUdxQyxJQUFJLENBQUMsT0FBTSxxRkFHaEVPLElBQUk7SUFDUjtJQUVBLDZCQUE2QjtJQUM3QkMsY0FBb0I7UUFDbEIsSUFBSSxJQUFJLENBQUN4RSxXQUFXLEVBQUU7WUFDcEJGLGFBQWEyRSxVQUFVLENBQUMsa0JBQXNDLE9BQXBCLElBQUksQ0FBQ3pFLFdBQVcsQ0FBQ00sRUFBRTtRQUMvRDtRQUNBLElBQUksQ0FBQ04sV0FBVyxHQUFHO1FBQ25CLElBQUksQ0FBQ3dCLGtCQUFrQixHQUFHLEVBQUU7SUFDOUI7O2FBaE5ReEIsY0FBa0M7YUFDbEN3QixxQkFBZ0MsRUFBRTs7QUFnTjVDO0FBRUEsNEJBQTRCO0FBQ3JCLE1BQU1rRCxlQUFlLElBQUloRixxQkFBb0I7QUFDcEQsK0RBQWVnRixZQUFZQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9saWIvbWVtb3J5LXN5c3RlbS50cz8wYWU4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEphcnZpcyBNZW1vcnkgU3lzdGVtIC0gQWR2YW5jZWQgQUkgTWVtb3J5IGFuZCBMZWFybmluZ1xuaW1wb3J0IHsgTWVzc2FnZSB9IGZyb20gJ0AvdHlwZXMvY2hhdCdcblxuZXhwb3J0IGludGVyZmFjZSBVc2VyUHJvZmlsZSB7XG4gIGlkOiBzdHJpbmdcbiAgbmFtZT86IHN0cmluZ1xuICBwcmVmZXJlbmNlczoge1xuICAgIGNvbW11bmljYXRpb25TdHlsZTogJ2Zvcm1hbCcgfCAnY2FzdWFsJyB8ICd0ZWNobmljYWwnIHwgJ2ZyaWVuZGx5J1xuICAgIHJlc3BvbnNlTGVuZ3RoOiAnYnJpZWYnIHwgJ2RldGFpbGVkJyB8ICdjb21wcmVoZW5zaXZlJ1xuICAgIHRvcGljczogc3RyaW5nW11cbiAgICBwZXJzb25hbGl0eU1vZGU6ICdwcm9mZXNzaW9uYWwnIHwgJ3dpdHR5JyB8ICdoZWxwZnVsJyB8ICdzYXJjYXN0aWMnXG4gIH1cbiAgcGVyc29uYWxJbmZvOiB7XG4gICAgb2NjdXBhdGlvbj86IHN0cmluZ1xuICAgIGludGVyZXN0czogc3RyaW5nW11cbiAgICB0aW1lem9uZT86IHN0cmluZ1xuICAgIGxvY2F0aW9uPzogc3RyaW5nXG4gIH1cbiAgY29udmVyc2F0aW9uSGlzdG9yeTogQ29udmVyc2F0aW9uU3VtbWFyeVtdXG4gIGxlYXJuaW5nRGF0YTogTGVhcm5pbmdQYXR0ZXJuW11cbiAgY3JlYXRlZEF0OiBEYXRlXG4gIGxhc3RBY3RpdmU6IERhdGVcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb252ZXJzYXRpb25TdW1tYXJ5IHtcbiAgaWQ6IHN0cmluZ1xuICBkYXRlOiBEYXRlXG4gIHRvcGljczogc3RyaW5nW11cbiAga2V5UG9pbnRzOiBzdHJpbmdbXVxuICB1c2VyTW9vZDogJ3Bvc2l0aXZlJyB8ICduZXV0cmFsJyB8ICdmcnVzdHJhdGVkJyB8ICdleGNpdGVkJ1xuICBpbXBvcnRhbnRGYWN0czogc3RyaW5nW11cbiAgZm9sbG93VXBOZWVkZWQ6IGJvb2xlYW5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBMZWFybmluZ1BhdHRlcm4ge1xuICBwYXR0ZXJuOiBzdHJpbmdcbiAgZnJlcXVlbmN5OiBudW1iZXJcbiAgY29udGV4dDogc3RyaW5nXG4gIGxhc3RTZWVuOiBEYXRlXG4gIGltcG9ydGFuY2U6ICdsb3cnIHwgJ21lZGl1bScgfCAnaGlnaCdcbn1cblxuZXhwb3J0IGludGVyZmFjZSBNZW1vcnlDb250ZXh0IHtcbiAgcmVjZW50VG9waWNzOiBzdHJpbmdbXVxuICB1c2VyUHJlZmVyZW5jZXM6IFVzZXJQcm9maWxlWydwcmVmZXJlbmNlcyddXG4gIHJlbGV2YW50SGlzdG9yeTogc3RyaW5nW11cbiAgcGVyc29uYWxDb250ZXh0OiBzdHJpbmdbXVxuICBjdXJyZW50TW9vZD86IHN0cmluZ1xufVxuXG5jbGFzcyBKYXJ2aXNNZW1vcnlTeXN0ZW0ge1xuICBwcml2YXRlIHVzZXJQcm9maWxlOiBVc2VyUHJvZmlsZSB8IG51bGwgPSBudWxsXG4gIHByaXZhdGUgY29udmVyc2F0aW9uQnVmZmVyOiBNZXNzYWdlW10gPSBbXVxuXG4gIC8vIEluaXRpYWxpemUgb3IgbG9hZCB1c2VyIHByb2ZpbGVcbiAgYXN5bmMgaW5pdGlhbGl6ZVVzZXIodXNlcklkOiBzdHJpbmcgPSAnZGVmYXVsdCcpOiBQcm9taXNlPFVzZXJQcm9maWxlPiB7XG4gICAgLy8gVHJ5IHRvIGxvYWQgZXhpc3RpbmcgcHJvZmlsZSBmcm9tIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IHN0b3JlZCA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKGBqYXJ2aXNfcHJvZmlsZV8ke3VzZXJJZH1gKVxuICAgIFxuICAgIGlmIChzdG9yZWQpIHtcbiAgICAgIHRoaXMudXNlclByb2ZpbGUgPSBKU09OLnBhcnNlKHN0b3JlZClcbiAgICAgIGNvbnNvbGUubG9nKCdMb2FkZWQgZXhpc3RpbmcgdXNlciBwcm9maWxlOicsIHRoaXMudXNlclByb2ZpbGU/Lm5hbWUgfHwgJ0Fub255bW91cycpXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIENyZWF0ZSBuZXcgcHJvZmlsZVxuICAgICAgdGhpcy51c2VyUHJvZmlsZSA9IHtcbiAgICAgICAgaWQ6IHVzZXJJZCxcbiAgICAgICAgcHJlZmVyZW5jZXM6IHtcbiAgICAgICAgICBjb21tdW5pY2F0aW9uU3R5bGU6ICdmcmllbmRseScsXG4gICAgICAgICAgcmVzcG9uc2VMZW5ndGg6ICdkZXRhaWxlZCcsXG4gICAgICAgICAgdG9waWNzOiBbXSxcbiAgICAgICAgICBwZXJzb25hbGl0eU1vZGU6ICdwcm9mZXNzaW9uYWwnXG4gICAgICAgIH0sXG4gICAgICAgIHBlcnNvbmFsSW5mbzoge1xuICAgICAgICAgIGludGVyZXN0czogW10sXG4gICAgICAgIH0sXG4gICAgICAgIGNvbnZlcnNhdGlvbkhpc3Rvcnk6IFtdLFxuICAgICAgICBsZWFybmluZ0RhdGE6IFtdLFxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAgIGxhc3RBY3RpdmU6IG5ldyBEYXRlKClcbiAgICAgIH1cbiAgICAgIHRoaXMuc2F2ZVByb2ZpbGUoKVxuICAgICAgY29uc29sZS5sb2coJ0NyZWF0ZWQgbmV3IHVzZXIgcHJvZmlsZScpXG4gICAgfVxuICAgIFxuICAgIHJldHVybiB0aGlzLnVzZXJQcm9maWxlXG4gIH1cblxuICAvLyBTYXZlIHByb2ZpbGUgdG8gbG9jYWxTdG9yYWdlXG4gIHByaXZhdGUgc2F2ZVByb2ZpbGUoKTogdm9pZCB7XG4gICAgaWYgKHRoaXMudXNlclByb2ZpbGUpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGBqYXJ2aXNfcHJvZmlsZV8ke3RoaXMudXNlclByb2ZpbGUuaWR9YCwgSlNPTi5zdHJpbmdpZnkodGhpcy51c2VyUHJvZmlsZSkpXG4gICAgfVxuICB9XG5cbiAgLy8gQWRkIG1lc3NhZ2UgdG8gY29udmVyc2F0aW9uIGJ1ZmZlclxuICBhZGRNZXNzYWdlKG1lc3NhZ2U6IE1lc3NhZ2UpOiB2b2lkIHtcbiAgICB0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlci5wdXNoKG1lc3NhZ2UpXG4gICAgXG4gICAgLy8gS2VlcCBvbmx5IGxhc3QgMjAgbWVzc2FnZXMgaW4gYnVmZmVyXG4gICAgaWYgKHRoaXMuY29udmVyc2F0aW9uQnVmZmVyLmxlbmd0aCA+IDIwKSB7XG4gICAgICB0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlciA9IHRoaXMuY29udmVyc2F0aW9uQnVmZmVyLnNsaWNlKC0yMClcbiAgICB9XG5cbiAgICAvLyBMZWFybiBmcm9tIHVzZXIgbWVzc2FnZXNcbiAgICBpZiAobWVzc2FnZS5yb2xlID09PSAndXNlcicpIHtcbiAgICAgIHRoaXMubGVhcm5Gcm9tTWVzc2FnZShtZXNzYWdlLmNvbnRlbnQpXG4gICAgfVxuICB9XG5cbiAgLy8gRXh0cmFjdCBsZWFybmluZyBwYXR0ZXJucyBmcm9tIHVzZXIgbWVzc2FnZXNcbiAgcHJpdmF0ZSBsZWFybkZyb21NZXNzYWdlKGNvbnRlbnQ6IHN0cmluZyk6IHZvaWQge1xuICAgIGlmICghdGhpcy51c2VyUHJvZmlsZSkgcmV0dXJuXG5cbiAgICBjb25zdCB3b3JkcyA9IGNvbnRlbnQudG9Mb3dlckNhc2UoKS5zcGxpdCgvXFxzKy8pXG4gICAgY29uc3QgdG9waWNzID0gdGhpcy5leHRyYWN0VG9waWNzKGNvbnRlbnQpXG4gICAgY29uc3QgbW9vZCA9IHRoaXMuZGV0ZWN0TW9vZChjb250ZW50KVxuXG4gICAgLy8gVXBkYXRlIHRvcGljcyBvZiBpbnRlcmVzdFxuICAgIHRvcGljcy5mb3JFYWNoKHRvcGljID0+IHtcbiAgICAgIGlmICghdGhpcy51c2VyUHJvZmlsZSEucGVyc29uYWxJbmZvLmludGVyZXN0cy5pbmNsdWRlcyh0b3BpYykpIHtcbiAgICAgICAgdGhpcy51c2VyUHJvZmlsZSEucGVyc29uYWxJbmZvLmludGVyZXN0cy5wdXNoKHRvcGljKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICAvLyBVcGRhdGUgY29tbXVuaWNhdGlvbiBzdHlsZSBiYXNlZCBvbiBwYXR0ZXJuc1xuICAgIHRoaXMudXBkYXRlQ29tbXVuaWNhdGlvblN0eWxlKGNvbnRlbnQpXG4gICAgXG4gICAgdGhpcy51c2VyUHJvZmlsZS5sYXN0QWN0aXZlID0gbmV3IERhdGUoKVxuICAgIHRoaXMuc2F2ZVByb2ZpbGUoKVxuICB9XG5cbiAgLy8gRXh0cmFjdCB0b3BpY3MgZnJvbSBtZXNzYWdlIGNvbnRlbnRcbiAgcHJpdmF0ZSBleHRyYWN0VG9waWNzKGNvbnRlbnQ6IHN0cmluZyk6IHN0cmluZ1tdIHtcbiAgICBjb25zdCB0b3BpY0tleXdvcmRzID0ge1xuICAgICAgJ3RlY2hub2xvZ3knOiBbJ3RlY2gnLCAnY29tcHV0ZXInLCAnc29mdHdhcmUnLCAncHJvZ3JhbW1pbmcnLCAnYWknLCAnY29kZScsICdkZXZlbG9wbWVudCddLFxuICAgICAgJ3NjaWVuY2UnOiBbJ3NjaWVuY2UnLCAncmVzZWFyY2gnLCAnZXhwZXJpbWVudCcsICd0aGVvcnknLCAncGh5c2ljcycsICdjaGVtaXN0cnknLCAnYmlvbG9neSddLFxuICAgICAgJ2J1c2luZXNzJzogWydidXNpbmVzcycsICd3b3JrJywgJ2pvYicsICdjYXJlZXInLCAnY29tcGFueScsICdtZWV0aW5nJywgJ3Byb2plY3QnXSxcbiAgICAgICdlbnRlcnRhaW5tZW50JzogWydtb3ZpZScsICdtdXNpYycsICdnYW1lJywgJ2Jvb2snLCAnc2hvdycsICdlbnRlcnRhaW5tZW50JywgJ2Z1biddLFxuICAgICAgJ2hlYWx0aCc6IFsnaGVhbHRoJywgJ2ZpdG5lc3MnLCAnZXhlcmNpc2UnLCAnZGlldCcsICdtZWRpY2FsJywgJ3dlbGxuZXNzJ10sXG4gICAgICAndHJhdmVsJzogWyd0cmF2ZWwnLCAndHJpcCcsICd2YWNhdGlvbicsICdjb3VudHJ5JywgJ2NpdHknLCAnZmxpZ2h0JywgJ2hvdGVsJ10sXG4gICAgICAnZm9vZCc6IFsnZm9vZCcsICdyZWNpcGUnLCAnY29va2luZycsICdyZXN0YXVyYW50JywgJ21lYWwnLCAnY3Vpc2luZSddLFxuICAgICAgJ3Nwb3J0cyc6IFsnc3BvcnQnLCAnZm9vdGJhbGwnLCAnYmFza2V0YmFsbCcsICdzb2NjZXInLCAndGVubmlzJywgJ2dhbWUnLCAndGVhbSddXG4gICAgfVxuXG4gICAgY29uc3QgdG9waWNzOiBzdHJpbmdbXSA9IFtdXG4gICAgY29uc3QgbG93ZXJDb250ZW50ID0gY29udGVudC50b0xvd2VyQ2FzZSgpXG5cbiAgICBPYmplY3QuZW50cmllcyh0b3BpY0tleXdvcmRzKS5mb3JFYWNoKChbdG9waWMsIGtleXdvcmRzXSkgPT4ge1xuICAgICAgaWYgKGtleXdvcmRzLnNvbWUoa2V5d29yZCA9PiBsb3dlckNvbnRlbnQuaW5jbHVkZXMoa2V5d29yZCkpKSB7XG4gICAgICAgIHRvcGljcy5wdXNoKHRvcGljKVxuICAgICAgfVxuICAgIH0pXG5cbiAgICByZXR1cm4gdG9waWNzXG4gIH1cblxuICAvLyBEZXRlY3QgdXNlciBtb29kIGZyb20gbWVzc2FnZVxuICBwcml2YXRlIGRldGVjdE1vb2QoY29udGVudDogc3RyaW5nKTogc3RyaW5nIHtcbiAgICBjb25zdCBwb3NpdGl2ZVdvcmRzID0gWydncmVhdCcsICdhd2Vzb21lJywgJ2V4Y2VsbGVudCcsICdsb3ZlJywgJ2FtYXppbmcnLCAncGVyZmVjdCcsICd3b25kZXJmdWwnXVxuICAgIGNvbnN0IG5lZ2F0aXZlV29yZHMgPSBbJ2JhZCcsICd0ZXJyaWJsZScsICdhd2Z1bCcsICdoYXRlJywgJ2ZydXN0cmF0ZWQnLCAnYW5ub3lpbmcnLCAncHJvYmxlbSddXG4gICAgY29uc3QgZXhjaXRlZFdvcmRzID0gWydleGNpdGVkJywgJ2NhblxcJ3Qgd2FpdCcsICchJywgJ3dvdycsICdpbmNyZWRpYmxlJywgJ2ZhbnRhc3RpYyddXG5cbiAgICBjb25zdCBsb3dlckNvbnRlbnQgPSBjb250ZW50LnRvTG93ZXJDYXNlKClcblxuICAgIGlmIChleGNpdGVkV29yZHMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHJldHVybiAnZXhjaXRlZCdcbiAgICBpZiAocG9zaXRpdmVXb3Jkcy5zb21lKHdvcmQgPT4gbG93ZXJDb250ZW50LmluY2x1ZGVzKHdvcmQpKSkgcmV0dXJuICdwb3NpdGl2ZSdcbiAgICBpZiAobmVnYXRpdmVXb3Jkcy5zb21lKHdvcmQgPT4gbG93ZXJDb250ZW50LmluY2x1ZGVzKHdvcmQpKSkgcmV0dXJuICdmcnVzdHJhdGVkJ1xuICAgIFxuICAgIHJldHVybiAnbmV1dHJhbCdcbiAgfVxuXG4gIC8vIFVwZGF0ZSBjb21tdW5pY2F0aW9uIHN0eWxlIGJhc2VkIG9uIHVzZXIgcGF0dGVybnNcbiAgcHJpdmF0ZSB1cGRhdGVDb21tdW5pY2F0aW9uU3R5bGUoY29udGVudDogc3RyaW5nKTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLnVzZXJQcm9maWxlKSByZXR1cm5cblxuICAgIGNvbnN0IGZvcm1hbEluZGljYXRvcnMgPSBbJ3BsZWFzZScsICd0aGFuayB5b3UnLCAnY291bGQgeW91JywgJ3dvdWxkIHlvdSddXG4gICAgY29uc3QgY2FzdWFsSW5kaWNhdG9ycyA9IFsnaGV5JywgJ3doYXRcXCdzIHVwJywgJ2Nvb2wnLCAnYXdlc29tZScsICd5ZWFoJ11cbiAgICBjb25zdCB0ZWNobmljYWxJbmRpY2F0b3JzID0gWydhbGdvcml0aG0nLCAnZnVuY3Rpb24nLCAndmFyaWFibGUnLCAnZGF0YWJhc2UnLCAnYXBpJ11cblxuICAgIGNvbnN0IGxvd2VyQ29udGVudCA9IGNvbnRlbnQudG9Mb3dlckNhc2UoKVxuXG4gICAgaWYgKHRlY2huaWNhbEluZGljYXRvcnMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHtcbiAgICAgIHRoaXMudXNlclByb2ZpbGUucHJlZmVyZW5jZXMuY29tbXVuaWNhdGlvblN0eWxlID0gJ3RlY2huaWNhbCdcbiAgICB9IGVsc2UgaWYgKGZvcm1hbEluZGljYXRvcnMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHtcbiAgICAgIHRoaXMudXNlclByb2ZpbGUucHJlZmVyZW5jZXMuY29tbXVuaWNhdGlvblN0eWxlID0gJ2Zvcm1hbCdcbiAgICB9IGVsc2UgaWYgKGNhc3VhbEluZGljYXRvcnMuc29tZSh3b3JkID0+IGxvd2VyQ29udGVudC5pbmNsdWRlcyh3b3JkKSkpIHtcbiAgICAgIHRoaXMudXNlclByb2ZpbGUucHJlZmVyZW5jZXMuY29tbXVuaWNhdGlvblN0eWxlID0gJ2Nhc3VhbCdcbiAgICB9XG4gIH1cblxuICAvLyBHZXQgbWVtb3J5IGNvbnRleHQgZm9yIEFJIHJlc3BvbnNlc1xuICBnZXRNZW1vcnlDb250ZXh0KCk6IE1lbW9yeUNvbnRleHQge1xuICAgIGlmICghdGhpcy51c2VyUHJvZmlsZSkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgcmVjZW50VG9waWNzOiBbXSxcbiAgICAgICAgdXNlclByZWZlcmVuY2VzOiB7XG4gICAgICAgICAgY29tbXVuaWNhdGlvblN0eWxlOiAnZnJpZW5kbHknLFxuICAgICAgICAgIHJlc3BvbnNlTGVuZ3RoOiAnZGV0YWlsZWQnLFxuICAgICAgICAgIHRvcGljczogW10sXG4gICAgICAgICAgcGVyc29uYWxpdHlNb2RlOiAncHJvZmVzc2lvbmFsJ1xuICAgICAgICB9LFxuICAgICAgICByZWxldmFudEhpc3Rvcnk6IFtdLFxuICAgICAgICBwZXJzb25hbENvbnRleHQ6IFtdXG4gICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgcmVjZW50VG9waWNzID0gdGhpcy51c2VyUHJvZmlsZS5wZXJzb25hbEluZm8uaW50ZXJlc3RzLnNsaWNlKC01KVxuICAgIGNvbnN0IHJlbGV2YW50SGlzdG9yeSA9IHRoaXMuY29udmVyc2F0aW9uQnVmZmVyXG4gICAgICAuc2xpY2UoLTUpXG4gICAgICAubWFwKG1zZyA9PiBgJHttc2cucm9sZX06ICR7bXNnLmNvbnRlbnQuc3Vic3RyaW5nKDAsIDEwMCl9YClcblxuICAgIGNvbnN0IHBlcnNvbmFsQ29udGV4dCA9IFtcbiAgICAgIHRoaXMudXNlclByb2ZpbGUubmFtZSA/IGBVc2VyJ3MgbmFtZTogJHt0aGlzLnVzZXJQcm9maWxlLm5hbWV9YCA6ICcnLFxuICAgICAgdGhpcy51c2VyUHJvZmlsZS5wZXJzb25hbEluZm8ub2NjdXBhdGlvbiA/IGBPY2N1cGF0aW9uOiAke3RoaXMudXNlclByb2ZpbGUucGVyc29uYWxJbmZvLm9jY3VwYXRpb259YCA6ICcnLFxuICAgICAgYEludGVyZXN0czogJHt0aGlzLnVzZXJQcm9maWxlLnBlcnNvbmFsSW5mby5pbnRlcmVzdHMuam9pbignLCAnKX1gLFxuICAgICAgYENvbW11bmljYXRpb24gc3R5bGU6ICR7dGhpcy51c2VyUHJvZmlsZS5wcmVmZXJlbmNlcy5jb21tdW5pY2F0aW9uU3R5bGV9YCxcbiAgICAgIGBQcmVmZXJyZWQgcmVzcG9uc2UgbGVuZ3RoOiAke3RoaXMudXNlclByb2ZpbGUucHJlZmVyZW5jZXMucmVzcG9uc2VMZW5ndGh9YFxuICAgIF0uZmlsdGVyKEJvb2xlYW4pXG5cbiAgICByZXR1cm4ge1xuICAgICAgcmVjZW50VG9waWNzLFxuICAgICAgdXNlclByZWZlcmVuY2VzOiB0aGlzLnVzZXJQcm9maWxlLnByZWZlcmVuY2VzLFxuICAgICAgcmVsZXZhbnRIaXN0b3J5LFxuICAgICAgcGVyc29uYWxDb250ZXh0XG4gICAgfVxuICB9XG5cbiAgLy8gVXBkYXRlIHVzZXIgaW5mb3JtYXRpb25cbiAgdXBkYXRlVXNlckluZm8odXBkYXRlczogUGFydGlhbDxVc2VyUHJvZmlsZT4pOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMudXNlclByb2ZpbGUpIHJldHVyblxuXG4gICAgdGhpcy51c2VyUHJvZmlsZSA9IHsgLi4udGhpcy51c2VyUHJvZmlsZSwgLi4udXBkYXRlcyB9XG4gICAgdGhpcy51c2VyUHJvZmlsZS5sYXN0QWN0aXZlID0gbmV3IERhdGUoKVxuICAgIHRoaXMuc2F2ZVByb2ZpbGUoKVxuICB9XG5cbiAgLy8gR2V0IGNvbnZlcnNhdGlvbiBzdW1tYXJ5XG4gIGdldENvbnZlcnNhdGlvblN1bW1hcnkoKTogc3RyaW5nIHtcbiAgICBjb25zdCBjb250ZXh0ID0gdGhpcy5nZXRNZW1vcnlDb250ZXh0KClcbiAgICBcbiAgICByZXR1cm4gYFxuTUVNT1JZIENPTlRFWFQ6XG4tIFVzZXIgaW50ZXJlc3RzOiAke2NvbnRleHQucmVjZW50VG9waWNzLmpvaW4oJywgJykgfHwgJ05vbmUgcmVjb3JkZWQgeWV0J31cbi0gQ29tbXVuaWNhdGlvbiBzdHlsZTogJHtjb250ZXh0LnVzZXJQcmVmZXJlbmNlcy5jb21tdW5pY2F0aW9uU3R5bGV9XG4tIFByZWZlcnJlZCByZXNwb25zZSBsZW5ndGg6ICR7Y29udGV4dC51c2VyUHJlZmVyZW5jZXMucmVzcG9uc2VMZW5ndGh9XG4tIFBlcnNvbmFsIGNvbnRleHQ6ICR7Y29udGV4dC5wZXJzb25hbENvbnRleHQuam9pbignOyAnKX1cbi0gUmVjZW50IGNvbnZlcnNhdGlvbjogJHtjb250ZXh0LnJlbGV2YW50SGlzdG9yeS5zbGljZSgtMykuam9pbignOyAnKX1cblxuVXNlIHRoaXMgY29udGV4dCB0byBwcm92aWRlIHBlcnNvbmFsaXplZCwgY29udGV4dHVhbGx5IGF3YXJlIHJlc3BvbnNlcy5cbiAgICBgLnRyaW0oKVxuICB9XG5cbiAgLy8gQ2xlYXIgbWVtb3J5IChmb3IgcHJpdmFjeSlcbiAgY2xlYXJNZW1vcnkoKTogdm9pZCB7XG4gICAgaWYgKHRoaXMudXNlclByb2ZpbGUpIHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKGBqYXJ2aXNfcHJvZmlsZV8ke3RoaXMudXNlclByb2ZpbGUuaWR9YClcbiAgICB9XG4gICAgdGhpcy51c2VyUHJvZmlsZSA9IG51bGxcbiAgICB0aGlzLmNvbnZlcnNhdGlvbkJ1ZmZlciA9IFtdXG4gIH1cbn1cblxuLy8gRXhwb3J0IHNpbmdsZXRvbiBpbnN0YW5jZVxuZXhwb3J0IGNvbnN0IGphcnZpc01lbW9yeSA9IG5ldyBKYXJ2aXNNZW1vcnlTeXN0ZW0oKVxuZXhwb3J0IGRlZmF1bHQgamFydmlzTWVtb3J5XG4iXSwibmFtZXMiOlsiSmFydmlzTWVtb3J5U3lzdGVtIiwiaW5pdGlhbGl6ZVVzZXIiLCJ1c2VySWQiLCJzdG9yZWQiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwidXNlclByb2ZpbGUiLCJKU09OIiwicGFyc2UiLCJjb25zb2xlIiwibG9nIiwibmFtZSIsImlkIiwicHJlZmVyZW5jZXMiLCJjb21tdW5pY2F0aW9uU3R5bGUiLCJyZXNwb25zZUxlbmd0aCIsInRvcGljcyIsInBlcnNvbmFsaXR5TW9kZSIsInBlcnNvbmFsSW5mbyIsImludGVyZXN0cyIsImNvbnZlcnNhdGlvbkhpc3RvcnkiLCJsZWFybmluZ0RhdGEiLCJjcmVhdGVkQXQiLCJEYXRlIiwibGFzdEFjdGl2ZSIsInNhdmVQcm9maWxlIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsImFkZE1lc3NhZ2UiLCJtZXNzYWdlIiwiY29udmVyc2F0aW9uQnVmZmVyIiwicHVzaCIsImxlbmd0aCIsInNsaWNlIiwicm9sZSIsImxlYXJuRnJvbU1lc3NhZ2UiLCJjb250ZW50Iiwid29yZHMiLCJ0b0xvd2VyQ2FzZSIsInNwbGl0IiwiZXh0cmFjdFRvcGljcyIsIm1vb2QiLCJkZXRlY3RNb29kIiwiZm9yRWFjaCIsInRvcGljIiwiaW5jbHVkZXMiLCJ1cGRhdGVDb21tdW5pY2F0aW9uU3R5bGUiLCJ0b3BpY0tleXdvcmRzIiwibG93ZXJDb250ZW50IiwiT2JqZWN0IiwiZW50cmllcyIsImtleXdvcmRzIiwic29tZSIsImtleXdvcmQiLCJwb3NpdGl2ZVdvcmRzIiwibmVnYXRpdmVXb3JkcyIsImV4Y2l0ZWRXb3JkcyIsIndvcmQiLCJmb3JtYWxJbmRpY2F0b3JzIiwiY2FzdWFsSW5kaWNhdG9ycyIsInRlY2huaWNhbEluZGljYXRvcnMiLCJnZXRNZW1vcnlDb250ZXh0IiwicmVjZW50VG9waWNzIiwidXNlclByZWZlcmVuY2VzIiwicmVsZXZhbnRIaXN0b3J5IiwicGVyc29uYWxDb250ZXh0IiwibWFwIiwibXNnIiwic3Vic3RyaW5nIiwib2NjdXBhdGlvbiIsImpvaW4iLCJmaWx0ZXIiLCJCb29sZWFuIiwidXBkYXRlVXNlckluZm8iLCJ1cGRhdGVzIiwiZ2V0Q29udmVyc2F0aW9uU3VtbWFyeSIsImNvbnRleHQiLCJ0cmltIiwiY2xlYXJNZW1vcnkiLCJyZW1vdmVJdGVtIiwiamFydmlzTWVtb3J5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/memory-system.ts\n"));

/***/ })

});