import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Grok Chatbot - Your Personal AI Assistant',
  description: 'A witty, intelligent AI chatbot with real-time information access',
  keywords: 'AI, chatbot, Grok, artificial intelligence, assistant',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gradient-to-br from-grok-dark to-grok-gray">
        <div className="flex flex-col min-h-screen">
          {children}
        </div>
      </body>
    </html>
  )
}
