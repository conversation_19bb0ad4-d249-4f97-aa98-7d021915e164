import type { Metadata } from 'next'
import './globals.css'

export const metadata: Metadata = {
  title: 'Jarvis - Advanced AI Assistant',
  description: 'A sophisticated AI assistant with real-time information access, professional efficiency, and personality',
  keywords: '<PERSON>, chatbot, <PERSON>, artificial intelligence, assistant, Iron Man',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="min-h-screen bg-gradient-to-br from-grok-dark to-grok-gray">
        <div className="flex flex-col min-h-screen">
          {children}
        </div>
      </body>
    </html>
  )
}
