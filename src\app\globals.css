@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

:root {
  --foreground-rgb: 231, 233, 234;
  --background-start-rgb: 15, 15, 15;
  --background-end-rgb: 26, 26, 26;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: linear-gradient(
    to bottom,
    rgb(var(--background-start-rgb)),
    rgb(var(--background-end-rgb))
  );
  color: rgb(var(--foreground-rgb));
  font-family: 'JetBrains Mono', monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #71767b;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1d9bf0;
}

/* Iron Man Visual Effects */

/* Arc Reactor Loading Animation */
@keyframes arcReactor {
  0% {
    transform: rotate(0deg) scale(1);
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.5), inset 0 0 20px rgba(220, 38, 38, 0.3);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
    box-shadow: 0 0 40px rgba(220, 38, 38, 0.8), inset 0 0 30px rgba(220, 38, 38, 0.5);
  }
  100% {
    transform: rotate(360deg) scale(1);
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.5), inset 0 0 20px rgba(220, 38, 38, 0.3);
  }
}

.arc-reactor {
  animation: arcReactor 2s linear infinite;
  border: 2px solid rgba(220, 38, 38, 0.6);
  position: relative;
}

.arc-reactor::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60%;
  height: 60%;
  background: radial-gradient(circle, rgba(220, 38, 38, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

/* Holographic Glow Effects */
@keyframes holographicGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(220, 38, 38, 0.5), 0 0 10px rgba(220, 38, 38, 0.3), 0 0 15px rgba(220, 38, 38, 0.2);
  }
  50% {
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.8), 0 0 20px rgba(220, 38, 38, 0.5), 0 0 30px rgba(220, 38, 38, 0.3);
  }
}

.holographic-glow {
  animation: holographicGlow 2s ease-in-out infinite;
}

/* Scan Line Effect */
@keyframes scanLine {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

.scan-line {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(220, 38, 38, 0.8), transparent);
  animation: scanLine 3s linear infinite;
  z-index: 1000;
  pointer-events: none;
}

/* Enhanced Message Animations */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

@keyframes messageSlideInRight {
  from {
    opacity: 0;
    transform: translateX(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.message-slide-in-left {
  animation: messageSlideIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.message-slide-in-right {
  animation: messageSlideInRight 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced Typing Indicator */
@keyframes energyPulse {
  0%, 100% {
    transform: scale(1);
    background: rgba(220, 38, 38, 0.8);
    box-shadow: 0 0 5px rgba(220, 38, 38, 0.5);
  }
  50% {
    transform: scale(1.2);
    background: rgba(220, 38, 38, 1);
    box-shadow: 0 0 15px rgba(220, 38, 38, 0.8);
  }
}

.energy-dot {
  animation: energyPulse 1.5s ease-in-out infinite;
}

.energy-dot:nth-child(2) {
  animation-delay: 0.3s;
}

.energy-dot:nth-child(3) {
  animation-delay: 0.6s;
}

/* Button Hover Effects */
@keyframes buttonGlow {
  from {
    box-shadow: 0 0 5px rgba(220, 38, 38, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.8), 0 0 30px rgba(220, 38, 38, 0.4);
  }
}

.button-glow:hover {
  animation: buttonGlow 0.3s ease-in-out forwards;
}

/* Grid Pattern Background */
.tech-grid {
  background-image:
    linear-gradient(rgba(220, 38, 38, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(220, 38, 38, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Glowing Border Effect */
@keyframes glowingBorder {
  0%, 100% {
    border-color: rgba(220, 38, 38, 0.5);
    box-shadow: 0 0 5px rgba(220, 38, 38, 0.3);
  }
  50% {
    border-color: rgba(220, 38, 38, 0.8);
    box-shadow: 0 0 15px rgba(220, 38, 38, 0.6);
  }
}

.glowing-border {
  animation: glowingBorder 2s ease-in-out infinite;
}

/* Avatar Pulse Effect */
@keyframes avatarPulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 10px rgba(220, 38, 38, 0.5);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.8);
  }
}

.avatar-pulse {
  animation: avatarPulse 2s ease-in-out infinite;
}
