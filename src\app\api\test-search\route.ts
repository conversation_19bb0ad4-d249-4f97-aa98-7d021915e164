import { NextRequest, NextResponse } from 'next/server'
import { searchWeb, getLatestNews } from '@/lib/realtime-info'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const query = searchParams.get('q') || 'test search'
  const type = searchParams.get('type') || 'search'

  try {
    console.log('Testing real-time info with:', { query, type })
    console.log('Environment variables:', {
      hasGoogleKey: !!process.env.GOOGLE_SEARCH_API_KEY,
      hasSearchEngineId: !!process.env.GOOGLE_SEARCH_ENGINE_ID,
      hasNewsKey: !!process.env.NEWS_API_KEY,
      googleKey: process.env.GOOGLE_SEARCH_API_KEY?.substring(0, 10) + '...',
      searchEngineId: process.env.GOOGLE_SEARCH_ENGINE_ID
    })

    let results: any = {}

    if (type === 'search') {
      console.log('Testing Google Search...')
      results.searchResults = await searchWeb(query, 3)
      console.log('Search results:', results.searchResults)
    } else if (type === 'news') {
      console.log('Testing News API...')
      results.newsResults = await getLatestNews(query, 3)
      console.log('News results:', results.newsResults)
    }

    return NextResponse.json({
      success: true,
      query,
      type,
      results,
      environment: {
        hasGoogleKey: !!process.env.GOOGLE_SEARCH_API_KEY,
        hasSearchEngineId: !!process.env.GOOGLE_SEARCH_ENGINE_ID,
        hasNewsKey: !!process.env.NEWS_API_KEY,
      }
    })

  } catch (error) {
    console.error('Test search error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      query,
      type
    }, { status: 500 })
  }
}
