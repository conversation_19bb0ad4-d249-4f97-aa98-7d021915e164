# <PERSON>rok Chatbot - Your Personal AI Assistant

A modern, witty AI chatbot inspired by Grok, built with Next.js, TypeScript, and Tailwind CSS. Features real-time chat, customizable personality, and a sleek dark theme interface.

## 🚀 Features

- **Witty AI Personality**: Conversational AI with humor and edge, similar to <PERSON>rok
- **Real-time Chat**: Instant messaging with typing indicators and smooth animations
- **Modern UI**: Dark theme with gradient accents and responsive design
- **TypeScript**: Full type safety throughout the application
- **Extensible**: Ready for AI integration (OpenAI, Claude, etc.)
- **Real-time Info**: Architecture ready for web scraping and news APIs

## 🛠️ Prerequisites

Before you begin, ensure you have the following installed:

1. **Node.js** (version 18 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **npm** or **yarn** (comes with Node.js)
   - Verify npm: `npm --version`

## 📦 Installation

1. **Clone or navigate to the project directory**
   ```bash
   cd chatbot
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   - Copy `.env.local` and update the values:
   ```bash
   # OpenAI API Configuration (get from https://platform.openai.com/)
   OPENAI_API_KEY=your_openai_api_key_here
   
   # Database Configuration (optional for now)
   DATABASE_URL="postgresql://username:password@localhost:5432/grok_chatbot"
   
   # JWT Secret for authentication
   JWT_SECRET=your_super_secret_jwt_key_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   - Navigate to: http://localhost:3000
   - Start chatting with your AI assistant!

## 🎯 Current Status

### ✅ Completed (Phase 1)
- [x] Project setup and architecture
- [x] Modern React/Next.js frontend
- [x] Chat interface with message bubbles
- [x] Typing indicators and animations
- [x] Responsive design with dark theme
- [x] TypeScript integration
- [x] Basic API structure

### 🚧 In Progress
- [ ] OpenAI/Claude API integration
- [ ] Real-time information system
- [ ] Database setup and user management
- [ ] Advanced personality customization

### 📋 Next Steps
1. **Install Node.js** if not already installed
2. **Run `npm install`** to install dependencies
3. **Get an OpenAI API key** from https://platform.openai.com/
4. **Update the `.env.local`** file with your API key
5. **Run `npm run dev`** to start the development server

## 🔧 Development

### Project Structure
```
src/
├── app/                 # Next.js app directory
│   ├── api/chat/       # Chat API endpoints
│   ├── globals.css     # Global styles
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Main chat page
├── components/         # React components
│   ├── ChatInterface.tsx
│   ├── Header.tsx
│   ├── MessageBubble.tsx
│   ├── MessageInput.tsx
│   └── TypingIndicator.tsx
├── types/              # TypeScript definitions
│   └── chat.ts
├── lib/                # Utility functions
└── utils/              # Helper functions
```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🎨 Customization

### Personality Settings
The chatbot personality can be customized in the user preferences:
- **Witty**: Humorous and clever responses (default)
- **Professional**: Formal and business-like
- **Casual**: Friendly and relaxed
- **Edgy**: Bold and unconventional (Grok-style)

### Theme Colors
Customize the color scheme in `tailwind.config.js`:
```javascript
colors: {
  'grok-dark': '#0f0f0f',
  'grok-gray': '#1a1a1a',
  'grok-blue': '#1d9bf0',
  'grok-green': '#00ba7c',
  // ... more colors
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is for educational and personal use. Please respect API terms of service when integrating with AI providers.

## 🆘 Troubleshooting

### Common Issues
1. **"npx not recognized"**: Install Node.js from nodejs.org
2. **API errors**: Check your OpenAI API key in `.env.local`
3. **Port conflicts**: Change the port in `package.json` scripts
4. **Build errors**: Run `npm install` to ensure all dependencies are installed

### Getting Help
- Check the console for error messages
- Ensure all environment variables are set
- Verify Node.js version is 18 or higher

---

**Happy chatting! 🤖✨**
